declare
    v_count number;
begin
    select count(*) into v_count
    from all_tables t
    where t.owner = upper('crh_ac') and t.table_name = upper('autorejectrecord');

    if v_count = 0 then
        execute immediate 'create table crh_ac.autorejectrecord(
                               serial_id       varchar2(32)    default '' '' 		 not null,
                               rule_id         varchar2(32)    default '' '' 		 not null,
                               rule_name       varchar2(255)   default '' '' 		 not null,
                               request_no      varchar2(32)    default '' '' 		 not null,
                               task_id         varchar2(32)    default '' ''		 not null,
                               flow_task_id    varchar2(32)    default '' '' 		 not null,
                               record_type     char(1)         default ''1''         not null,
                               create_datetime date            default sysdate       not null,
                               match_info      varchar2(4000)  default '' '' 		 not null
                           )';

        execute immediate 'create unique index crh_ac.idx_autoreject_serialid on crh_ac.autorejectrecord (serial_id)';
        execute immediate 'create index crh_ac.idx_autoreject_requestno on crh_ac.autorejectrecord (request_no)';
        execute immediate 'create index crh_ac.idx_autoreject_flowtaskid on crh_ac.autorejectrecord (flow_task_id)';
    end if;
end;
/