--BusinFlowTask表
declare
    v_rowcount integer;
begin
    select count(*) into v_rowcount from dual where exists(
        select * from all_tab_columns
        where table_name = upper('businflowtask')
          and column_name = upper('white_datetime')
          and owner= upper('crh_ac'));
    if v_rowcount = 0 then
        execute immediate 'alter table crh_ac.businflowtask add white_datetime date';
    end if;
    commit;
end;
/

--FlowTaskRecordDetails表
declare
    v_rowcount integer;
begin
    select count(*) into v_rowcount from dual where exists(
        select * from all_tab_columns
        where table_name = upper('flowtaskrecorddetails')
          and column_name = upper('white_datetime')
          and owner= upper('crh_ac'));
    if v_rowcount = 0 then
        execute immediate 'alter table crh_ac.flowtaskrecorddetails add white_datetime date';
    end if;
    commit;
end;
/