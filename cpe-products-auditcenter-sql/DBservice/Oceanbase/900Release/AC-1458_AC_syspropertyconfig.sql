declare
v_count integer;
begin
select count(1) into v_count from crh_ac.syspropertyconfig where property_key='wskh.delete.aiaudit.reject.reason.switch';
if v_count = 0 then
			insert into crh_ac.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'wskh_back',
				'删除智能驳回项原因展示开关',
				' ',
				38,
				'1',
				'wskh.delete.aiaudit.reject.reason.switch',
				'0',
				'1',
				'[{\\"label\\":\\"是\\",\\"value\\":\\"1\\"},{\\"label\\":\\"否\\",\\"value\\":\\"0\\"}]'
			);
end if;
commit;
end;
/
