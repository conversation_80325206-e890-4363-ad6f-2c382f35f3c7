
insert into "CRH_USER"."SY<PERSON>ENU"("CRHRESOURCE_ID", "MENU_SITE", "MENU_CAPTION", "MENU_NAME", "MENU_CLASS_STYLE", "ACTION_URL", "MENU_HINT", "SU<PERSON>Y<PERSON>_NO", "SHOW_FLAG", "<PERSON>EA<PERSON>_FLAG", "ROLE_RIGHTS", "USE_NEW_SYSMENU", "NEW_ACTION_URL")
values('281608', 'OGH', '驳回原因分布表', '驳回原因分布表', ' ', 'backend20/bm/#/account/rejection-reasons-statistics/list', '  ', '28', '1', '3', ' ', '0', ' ');


insert into "CRH_USER"."CRHRESOURCE"("RESOURCE_ID", "RESOURCE_TYPE", "PARENT_RESOURCE_ID", "RESOURCE_NAME", "RESOURCE_URL", "IS_CONTROL", "CREATE_DATETIME")
values('281608', '0', ' ', '驳回原因分布表', ' ', '1', CURRENT_TIMESTAMP);



ALTER TABLE CRH_AC.TASKREASONRECORD ADD (OPERATER_NO VARCHAR2(18) DEFAULT ' ' );
COMMENT ON COLUMN CRH_AC.TASKREASONRECORD.OPERATER_NO IS '操作员工号';

ALTER TABLE CRH_AC.TASKREASONRECORD ADD (OPERATER_NAME VARCHAR2(60) DEFAULT ' ' );
COMMENT ON COLUMN CRH_AC.TASKREASONRECORD.OPERATER_NAME IS '操作员名称';

ALTER TABLE CRH_AC.TASKREASONRECORD ADD (TASK_TYPE VARCHAR2(32) DEFAULT ' ' );
COMMENT ON COLUMN CRH_AC.TASKREASONRECORD.TASK_TYPE IS '任务类型';


ALTER TABLE CRH_AC.TASKREASONRECORD ADD (CHANNEL_CODE VARCHAR2(200) DEFAULT ' '  NOT NULL );
COMMENT ON COLUMN CRH_AC.TASKREASONRECORD.CHANNEL_CODE IS '渠道码';

ALTER TABLE CRH_AC.TASKREASONRECORD ADD (BRANCH_NO VARCHAR2(20) DEFAULT ' '  NOT NULL );
COMMENT ON COLUMN CRH_AC.TASKREASONRECORD.BRANCH_NO IS '机构编号';

ALTER TABLE CRH_AC.TASKREASONRECORD ADD (ACTIVITY_NAME VARCHAR2(200) DEFAULT ' '  NOT NULL);
COMMENT ON COLUMN CRH_AC.TASKREASONRECORD.ACTIVITY_NAME IS '活动名称';

ALTER TABLE CRH_AC.TASKREASONRECORD ADD ("MARKETING_TEAM" VARCHAR2(200) DEFAULT ' '  NOT NULL);
COMMENT ON COLUMN CRH_AC.TASKREASONRECORD.MARKETING_TEAM IS '营销团队';

ALTER TABLE CRH_AC.TASKREASONRECORD ADD ("TASK_APPLY_DATETIME" DATE);
COMMENT ON COLUMN CRH_AC.TASKREASONRECORD.TASK_APPLY_DATETIME IS '见证任务申请时间';
