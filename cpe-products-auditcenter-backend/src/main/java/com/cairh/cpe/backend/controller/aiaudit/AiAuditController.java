package com.cairh.cpe.backend.controller.aiaudit;

import cn.hutool.core.collection.CollectionUtil;
import com.cairh.cpe.aiaudit.query.AiAuditQueryDubboService;
import com.cairh.cpe.aiaudit.query.req.AuditTaskQueryRequest;
import com.cairh.cpe.aiaudit.query.resp.AuditTaskQueryResponse;
import com.cairh.cpe.api.aiaudit.ACAiAuditDubboService;
import com.cairh.cpe.api.aiaudit.resp.MaterialInfo;
import com.cairh.cpe.backend.form.req.AuditQueryRecordReq;
import com.cairh.cpe.backend.form.req.BaseForm;
import com.cairh.cpe.backend.form.req.HisAuditForm;
import com.cairh.cpe.backend.service.IAiAuditService;
import com.cairh.cpe.common.constant.ErrorEnum;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.service.ac.form.RepulsetReasonForm;
import com.cairh.cpe.service.ac.form.RepulsetReasonResp;
import com.cairh.cpe.service.ac.service.IRepulsetReasonConfigHandleService;
import com.cairh.cpe.service.aiaudit.request.AuditRuleQueryReq;
import com.cairh.cpe.service.aiaudit.response.AuditRuleQueryResp;
import com.cairh.cpe.service.aiaudit.response.QueryAuditBusinRecordResp;
import com.cairh.cpe.service.aiaudit.service.IAiAuditAchieveService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;

/**
 * 智能审核功能
 */
@Slf4j
@RestController
@RequestMapping("aiaudit")
public class AiAuditController {

    @Autowired
    private IAiAuditService aiAuditService;
    @Autowired
    private IAiAuditAchieveService aiAuditAchieveService;
    @Autowired
    private IRepulsetReasonConfigHandleService repulsetReasonConfigHandleService;
    @DubboReference(check = false)
    private AiAuditQueryDubboService auditQueryDubboService;
    @DubboReference(check = false)
    private ACAiAuditDubboService acAiAuditDubboService;

    /**
     * 查询智能审核结果
     */
    @PostMapping("queryAuditBusinRecord")
    public Result<QueryAuditBusinRecordResp> queryAuditBusinRecord(@Validated @RequestBody AuditQueryRecordReq auditQueryRecordReq) {
        String notes = "controller";
        QueryAuditBusinRecordResp queryAuditBusinRecordResp = aiAuditService.queryAuditBusinRecordV1(auditQueryRecordReq, notes);
        return Result.success(queryAuditBusinRecordResp);
    }

    /**
     * 响应式全异步审核
     *
     * @param baseForm {@link BaseForm}
     */
    @PostMapping("asyncAiauditAllListenable")
    public Result<String> asyncAiauditAllListenable(@Validated @RequestBody BaseForm baseForm) {
        aiAuditAchieveService.asyncAiauditAll(baseForm.getRequest_no());
        return Result.success();
    }

    /**
     * 审核详情报告素材
     */
    @PostMapping("getTaskQueryResult")
    public Result<AuditTaskQueryResponse> getTaskQueryResult(@Validated @RequestBody AuditTaskQueryRequest auditTaskQueryRequest) {
        List<AuditTaskQueryResponse> taskQueryResponses = auditQueryDubboService.queryAuditTaskResult(auditTaskQueryRequest);
        if (CollectionUtil.isNotEmpty(taskQueryResponses)) {
            return Result.success(taskQueryResponses.stream()
                    .sorted(Comparator.comparing(AuditTaskQueryResponse::getCreate_datetime).reversed())
                    .findFirst().get());
        }
        return Result.fail(ErrorEnum.AUDIT_INTELLIGENT_REPORT_GENERATE.getValue(), ErrorEnum.AUDIT_INTELLIGENT_REPORT_GENERATE.getDesc());
    }

    /**
     * 审核报告素材
     */
    @PostMapping("getAuditReportMaterial")
    public Result<MaterialInfo> getAuditReportMaterial(@Validated @RequestBody BaseForm baseForm) {
        MaterialInfo materialInfo = acAiAuditDubboService.getUserMaterialInfo(baseForm.getRequest_no());
        return Result.success(materialInfo);
    }

    @PostMapping("queryAuditRule")
    public Result<List<AuditRuleQueryResp>> queryAuditRule(@Validated @RequestBody AuditRuleQueryReq auditRuleQueryReq) {
        List<AuditRuleQueryResp> auditRuleQueryResps = aiAuditAchieveService.queryAuditRule(auditRuleQueryReq);
        return Result.success(auditRuleQueryResps);
    }


    @PostMapping("getHisAuditInfo")
    public Result<MaterialInfo> getHisAuditInfo(@Validated @RequestBody HisAuditForm hisAuditForm) {
        MaterialInfo materialInfo = aiAuditService.getHisAuditInfo(hisAuditForm);
        return Result.success(materialInfo);
    }

    @PostMapping("getRepulsetReason")
    public Result<List<RepulsetReasonResp>> getRepulsetReason(@RequestBody RepulsetReasonForm repulsetReasonForm) {
        return Result.success(repulsetReasonConfigHandleService.getRepulsetReason(repulsetReasonForm));
    }

}
