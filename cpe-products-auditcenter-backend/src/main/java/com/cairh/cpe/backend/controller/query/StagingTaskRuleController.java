package com.cairh.cpe.backend.controller.query;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.backend.form.req.*;
import com.cairh.cpe.backend.form.resp.StagingTaskRuleDetailResponse;
import com.cairh.cpe.backend.form.resp.StagingTaskRuleListResponse;
import com.cairh.cpe.backend.form.resp.StagingTaskRulePageResponse;
import com.cairh.cpe.backend.service.IAuditStagingTaskRuleService;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.PropKeyConstant;
import com.cairh.cpe.common.constant.StagingRuleTypeEnum;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 暂存任务规则控制层
 *
 * <AUTHOR>
 * @since 2025/3/11 17:33
 */
@RestController
@RequestMapping("stagingTaskRule")
public class StagingTaskRuleController {

    @Resource
    private IAuditStagingTaskRuleService auditStagingTaskRuleService;

    /**
     * 任务暂存规则分页查询
     *
     * @param request
     * @return
     */
    @RequestMapping("page")
    public Result<Page<StagingTaskRulePageResponse>> page(@RequestBody StagingTaskRulePageReq request) {
        return Result.success(auditStagingTaskRuleService.page(request));
    }

    /**
     * 查询任务暂存规则集合
     *
     * @return
     */
    @RequestMapping("list")
    public Result<List<StagingTaskRuleListResponse>> list(@RequestBody StagingTaskRuleListReq request) {
        return Result.success(auditStagingTaskRuleService.list(request));
    }

    /**
     * 详情
     *
     * @param serial_id
     * @return
     */
    @GetMapping("detail/{serial_id}")
    public Result<StagingTaskRuleDetailResponse> detail(@PathVariable("serial_id") String serial_id) {
        return Result.success(auditStagingTaskRuleService.detail(serial_id));
    }

    /**
     * 新增
     *
     * @param request
     * @return
     */
    @PostMapping("insert")
    public Result<String> addRule(@AuthenticationPrincipal BaseUser baseUser, @RequestBody StagingTaskRuleAddReq request) {
        Result<String> checkResult = checkTaskStagingSwitch(request.getRule_type(), request.getStatus());
        if (checkResult != null) {
            return checkResult;
        }
        auditStagingTaskRuleService.addRule(baseUser, request);
        return Result.success();
    }

    /**
     * 编辑
     *
     * @param request
     * @return
     */
    @PostMapping("edit")
    public Result<String> editRule(@AuthenticationPrincipal BaseUser baseUser, @RequestBody StagingTaskRuleEditReq request) {
        Result<String> checkResult = checkTaskStagingSwitch(request.getRule_type(), request.getStatus());
        if (checkResult != null) {
            return checkResult;
        }
        auditStagingTaskRuleService.editRule(baseUser, request);
        return Result.success();
    }

    /**
     * 启用-禁用
     *
     * @param baseUser
     * @param request
     * @return
     */
    @PostMapping("updateStatus")
    public Result<String> updateStatus(@AuthenticationPrincipal BaseUser baseUser, @RequestBody StagingTaskRuleStatusReq request) {
        auditStagingTaskRuleService.updateStatus(baseUser, request);
        return Result.success();
    }

    /**
     * 删除
     *
     * @param serial_id
     * @return
     */
    @GetMapping("delete/{serial_id}")
    public Result<String> delete(@PathVariable("serial_id") String serial_id) {
        auditStagingTaskRuleService.deleteRule(serial_id);
        return Result.success();
    }


    /**
     * 校验状态
     */
    private Result<String> checkTaskStagingSwitch(String ruleType, String status) {
        // 自动驳回，不校验暂存开关
        if (StringUtils.equals(StagingRuleTypeEnum.AUTO_REJECT.getCode(), ruleType)) {
            return null;
        }
        if (StringUtils.equals(WskhConstant.AVAILABLE_STATUS, status)) {
            // 任务暂存开关
            boolean open_result = PropertySource.getBoolean(PropKeyConstant.WSKH_TASK_STAGING_SWITCH);
            if (!open_result) {
                return Result.fail("暂存任务开关未开启");
            }
        }
        return null;
    }

}
