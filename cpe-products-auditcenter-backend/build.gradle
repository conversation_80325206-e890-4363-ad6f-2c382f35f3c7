plugins {
    id 'war'
    id 'org.springframework.boot'
}

archivesBaseName = 'cpe-products-auditcenter-backend'

dependencies {
    api(project(":cpe-products-auditcenter-common"))
    api(project(":cpe-products-auditcenter-cache"))
    api(project(":cpe-products-auditcenter-businflow:cpe-products-auditcenter-businflow-core"))
    api(project(":cpe-products-auditcenter-businflow:cpe-products-auditcenter-businflow-service"))
    api(project(":cpe-products-auditcenter-service:cpe-products-auditcenter-service-core"))
    api(project(":cpe-products-auditcenter-service:cpe-products-auditcenter-service-service"))
    api(project(':cpe-products-auditcenter-backend:cpe-products-auditcenter-backend-core'))
    api(project(':cpe-products-auditcenter-backend:cpe-products-auditcenter-backend-service'))

    api("com.cairh:cpe-common-backend")
    api("com.cairh:cpe-trace")

    api('org.springframework.boot:spring-boot-starter-web')
    api('org.springframework.boot:spring-boot-starter-actuator')

    api('com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery')
    api('com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config')
    api('org.springframework.cloud:spring-cloud-starter-consul-discovery')
    api('org.springframework.cloud:spring-cloud-starter-consul-config')

    api('com.cairh:cpe-job-core')
    api('org.apache.commons:commons-dbcp2')
    api('com.alibaba.spring:spring-context-support')
}
