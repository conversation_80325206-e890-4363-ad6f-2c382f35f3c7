package com.cairh.cpe.backend.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.cairh.cpe.backend.form.req.GetAuditResultForm;
import com.cairh.cpe.backend.service.IOpenapiService;
import com.cairh.cpe.businflow.entity.CustModifyRecord;
import com.cairh.cpe.businflow.service.ICustModifyRecordService;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.constant.flow.FlowRecordEnum;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowRecord;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.UserQueryExtInfo;
import com.cairh.cpe.common.entity.clob.AuditReason;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.clob.IDCardInfo;
import com.cairh.cpe.common.entity.clob.UserBaseInfo;
import com.cairh.cpe.common.entity.response.AuditResultResp;
import com.cairh.cpe.common.entity.response.CreateTaskResult;
import com.cairh.cpe.common.entity.response.SubmitAuditResp;
import com.cairh.cpe.common.entity.support.*;
import com.cairh.cpe.common.mapper.BusinFlowTaskMapper;
import com.cairh.cpe.common.service.*;
import com.cairh.cpe.common.util.KHDateUtil;
import com.cairh.cpe.common.util.ParamsSavingUtils;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.esb.component.elect.IEsbComponentElectDubboService;
import com.cairh.cpe.esb.component.elect.dto.req.ElectDownloadFileRequest;
import com.cairh.cpe.esb.component.elect.dto.resp.ElectDownloadFileResponse;
import com.cairh.cpe.service.auditcenter.service.IBidirectionalVideoHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OpenapiServiceImpl implements IOpenapiService {

    @Autowired
    IRequestFlowService requestFlowService;
    @Autowired
    IBusinFlowRequestService businFlowRequestService;
    @Autowired
    IRequestService requestService;
    @Autowired
    IBusinFlowRecordService businFlowRecordService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private ICustModifyRecordService custModifyRecordService;
    @DubboReference(check = false)
    private IEsbComponentElectDubboService componentElectDubboService;
    @Autowired
    private IUserQueryExtInfoService userQueryExtInfoService;
    @Autowired
    private IBusinFlowTaskService businFlowTaskService;
    @Autowired
    private BusinFlowTaskMapper businFlowTaskMapper;
    @Autowired
    private IBidirectionalVideoHandlerService bidirectionalVideoHandlerService;

    /**
     * 开户申请提交
     *
     * @param params 提交审核的所有参数
     * @return
     */
    @Transactional
    @Override
    public SubmitAuditResp openapiSubmitAudit(Map<String, Object> params) {
        String request_no = MapUtils.getString(params, Fields.REQUEST_NO, StrUtil.EMPTY);
        String busi_serial_no = String.valueOf(params.get(WskhFields.BUSI_SERIAL_NO));
        String form_spjz_id = MapUtils.getString(params, WskhFields.SPJZ_ID, StrUtil.EMPTY);
        String video_type = MapUtils.getString(params, Fields.VIDEO_TYPE, StrUtil.EMPTY);
        LambdaQueryWrapper<BusinFlowRequest> businFlowRequestQueryWrapper = new LambdaQueryWrapper<>();
        businFlowRequestQueryWrapper.select(BusinFlowRequest::getRequest_status, BusinFlowRequest::getRequest_no, BusinFlowRequest::getBusin_type)
                .eq(BusinFlowRequest::getBusi_serial_no, busi_serial_no)
                .orderByDesc(BusinFlowRequest::getRequest_no);
        List<BusinFlowRequest> oldBusinFlowRequestList = businFlowRequestService.list(businFlowRequestQueryWrapper);

        if (CollectionUtils.isNotEmpty(oldBusinFlowRequestList)) {
            BusinFlowRequest oldBusinFlowRequest = oldBusinFlowRequestList.get(0);
            request_no = oldBusinFlowRequest.getRequest_no();
            //查询历史快照
//            List<String> taskStatusList = new ArrayList<>();
//            taskStatusList.add(FlowStatusConst.AUDIT_PASS);
//            taskStatusList.add(FlowStatusConst.AUDIT_NO_PASS);
//            taskStatusList.add(FlowStatusConst.AUDIT_INVALIDATE);
            QueryWrapper<BusinFlowTask> businFlowTaskQueryWrapper = new QueryWrapper<>();
            businFlowTaskQueryWrapper.eq("request_no", request_no);
//            businFlowTaskQueryWrapper.in("task_status", taskStatusList);
            List<BusinFlowTask> businFlowTasks = businFlowTaskMapper.selectList(businFlowTaskQueryWrapper);
            //根据task_id分组
            Map<String, List<BusinFlowTask>> taskMap = businFlowTasks.stream().collect(Collectors.groupingBy(BusinFlowTask::getTask_id));
            List<BusinFlowTask> businFlowTaskList = new ArrayList<>();
            //遍历Map
            for (Map.Entry<String, List<BusinFlowTask>> entry : taskMap.entrySet()) {
                List<BusinFlowTask> value = entry.getValue();
                businFlowTaskList.add(value.get(0));
            }
            List<String> spjzList = new ArrayList<>();
            Map<String, BusinFlowTask> spjzTaskMap = new HashMap<>();
            for (BusinFlowTask businFlowTask : businFlowTaskList) {
                String serial_id = businFlowTask.getSerial_id();
                if (!StringUtils.equalsAny(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_PASS, FlowStatusConst.AUDIT_NO_PASS, FlowStatusConst.AUDIT_INVALIDATE)) {
                    serial_id = businFlowTask.getRequest_no();
                }
                ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNoV1(serial_id);
                String spjz_id = clobContentInfo.getSpjz_id();
                if (StringUtils.isNotBlank(spjz_id)) {
                    spjzList.add(spjz_id);
                    spjzTaskMap.put(spjz_id, businFlowTask);
                }
            }
            if (spjzList.contains(form_spjz_id)) {
                BusinFlowTask task = spjzTaskMap.getOrDefault(form_spjz_id, new BusinFlowTask());
                if (null == task) {
                    throw new BizException(ErrorEnum.AUDIT_TASK_APPLY_DATA_ERROR.getValue(), ErrorEnum.AUDIT_TASK_APPLY_DATA_ERROR.getDesc());
                }
                SubmitAuditResp submitAuditResp = new SubmitAuditResp();
                submitAuditResp.setRequest_no(task.getRequest_no());
                submitAuditResp.setFlowtask_id(task.getSerial_id());
                submitAuditResp.setTask_id(task.getTask_id());
                submitAuditResp.setRequest_no(request_no);
                return submitAuditResp;
            }
            // 100058-双向视频
            if (StringUtils.equals(oldBusinFlowRequest.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL) &&
                    StringUtils.equals(video_type, WskhConstant.VIDEO_TYPE_2)) {
                // 双向视频再次发起，任务状态是1or2，都直接进行任务作废处理
                clearOldBidirectionalTask(request_no);
            }

            // 历史申请信息为已完成，则重新创建开户申请
            if (StringUtils.equals(oldBusinFlowRequest.getRequest_status(), FlowStatusConst.AUDIT_PASS)) {
                request_no = Constants.EMPTY;
            }

        }
        // 1.保存参数
        log.info("开始保存参数 request_no={}", request_no);
        BusinFlowRequest businFlowRequest = requestFlowService.submitParams(request_no, params);
        log.info("结束保存参数 request_no={}", request_no);

        request_no = businFlowRequest.getRequest_no();
        // 2.提交开户申请
        log.info("开始提交开户申请 request_no={}", request_no);
        requestFlowService.applyAudit(request_no);
        log.info("结束提交开户申请 request_no={}", request_no);
        String submit_request_no = request_no;
        if (StringUtils.equalsAny(businFlowRequest.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL)) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    redisTemplate.opsForList().leftPush(QueueConstant.SUBMIT_AIAUDIT_DISPATCH_QUEUE, submit_request_no);
                }
            });
        }
        /*// 3.执行rpa定时查询任务
        createRpaTask(submit_request_no, params);*/
        SubmitAuditResp submitAuditResp = new SubmitAuditResp();
        submitAuditResp.setRequest_no(request_no);
        QueryWrapper<BusinFlowTask> businFlowTaskQueryWrapper = new QueryWrapper<>();
        businFlowTaskQueryWrapper.eq("request_no", request_no);
        businFlowTaskQueryWrapper.select("serial_id", "task_id");
        businFlowTaskQueryWrapper.orderByDesc("create_datetime");
        List<BusinFlowTask> businFlowTasks = businFlowTaskService.getBaseMapper().selectList(businFlowTaskQueryWrapper);
        if (CollectionUtils.isNotEmpty(businFlowTasks)) {
            submitAuditResp.setFlowtask_id(businFlowTasks.get(0).getSerial_id());
            submitAuditResp.setTask_id(businFlowTasks.get(0).getTask_id());
        }
        //更新客户信息表
        UpdateWrapper<UserQueryExtInfo> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("AUDIT_OPERATOR_NO", " ");
        updateWrapper.set("AUDIT_OPERATOR_NAME", " ");
        updateWrapper.set("audit_finish_datetime", null);
        updateWrapper.set("REVIEW_OPERATOR_NO", " ");
        updateWrapper.set("REVIEW_OPERATOR_NAME", " ");
        updateWrapper.set("REVIEW_FINISH_DATETIME", null);
        updateWrapper.set("DOUBLE_OPERATOR_NO", " ");
        updateWrapper.set("DOUBLE_OPERATOR_NAME", " ");
        updateWrapper.set("DOUBLE_FINISH_DATETIME", null);
        updateWrapper.eq("request_no", request_no);
        userQueryExtInfoService.update(updateWrapper);
        return submitAuditResp;
    }

    /**
     * 获取用户的审核结果
     */
    @Override
    public AuditResultResp openapiGetAuditResult(GetAuditResultForm baseForm) {
        String request_no = "";
        String flowtask_id = "";
        //单笔提交任务id
        String task_id = baseForm.getTask_id();
        //查询task_id是否存在,并且获取request_no
        QueryWrapper<BusinFlowTask> businFlowTaskQueryWrapper = new QueryWrapper<>();
        businFlowTaskQueryWrapper.eq("task_id", task_id);
        businFlowTaskQueryWrapper.orderByDesc("create_datetime");
        List<BusinFlowTask> businFlowTaskList = businFlowTaskService.getBaseMapper().selectList(businFlowTaskQueryWrapper);
        if (CollectionUtils.isEmpty(businFlowTaskList)) {
            log.error("BusinFlowTask 查询不到 task_id={}", baseForm.getTask_id());
            throw new BizException(ErrorEnum.AUDIT_TASK_NOT_EXIST.getValue(), ErrorEnum.AUDIT_TASK_NOT_EXIST.getDesc());
        }
        request_no = businFlowTaskList.get(0).getRequest_no();
        BusinFlowTask lastTask = businFlowTaskList.get(0);
        String task_status = lastTask.getTask_status();
        BusinFlowRequest businFlowRequest = businFlowRequestService.getById(request_no);

        if (businFlowTaskList.size() == 1 && !StringUtils.equalsAny(task_status, FlowStatusConst.AUDIT_PASS, FlowStatusConst.AUDIT_NO_PASS, FlowStatusConst.AUDIT_INVALIDATE)) {
            log.error("用户状态不正常，无法获取审核结果  businFlowTaskList ==1 task_id={}", baseForm.getTask_id());
            return new AuditResultResp(FlowStatusConst.REQUEST_STATUS_AUDITING);
        }
        BusinFlowTask currentTask = new BusinFlowTask();
        //当前状态为通过或者驳回
        if (task_status.equals(FlowStatusConst.AUDIT_PASS) || task_status.equals(FlowStatusConst.AUDIT_NO_PASS)
                || task_status.equals(FlowStatusConst.AUDIT_INVALIDATE)) {
            flowtask_id = lastTask.getSerial_id();
            currentTask = lastTask;
        } else {
            //上一节点
            if (businFlowTaskList.size() > 1) {
                flowtask_id = businFlowTaskList.get(1).getSerial_id();
                currentTask = businFlowTaskList.get(1);
            } else {
                flowtask_id = lastTask.getSerial_id();
                currentTask = lastTask;
            }
        }
        String currentTaskStatus = currentTask.getTask_status();
        String task_type = currentTask.getTask_type();
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNoV1(flowtask_id);
        AuditResultResp auditResultResp = new AuditResultResp();
        auditResultResp.setAudit_status(currentTaskStatus);
        auditResultResp.setAudit_info(new AuditInfo());
        auditResultResp.setFace_score("");
        auditResultResp.setOperator_info(new OperatorInfo());
        auditResultResp.setBack_code("");
        auditResultResp.setBack_reason(new ArrayList<AuditReason>());
        if (FlowStatusConst.AUDIT_NO_PASS.equals(currentTaskStatus)) {
            List<AuditReason> rectification_item = clobContentInfo.getRectification_item();
            auditResultResp.setBack_code(task_type);
            auditResultResp.setBack_reason(rectification_item);
        }
        // 见证作废
        if (FlowStatusConst.AUDIT_INVALIDATE.equals(currentTaskStatus)) {
            auditResultResp.setOp_content(currentTask.getOp_content());
        }
        auditResultResp.setUpdate_info_flag("");
        auditResultResp.setFace_score_82_80("");
        auditResultResp.setFace_score_desc("");
        auditResultResp.setExist_double_review("");
        AuditInfo auditInfo = new AuditInfo();
        ArchfileinfoResp archfile_info = new ArchfileinfoResp();
        archfile_info.setFile_6A(getFilePath(clobContentInfo.getFile_6A()));
        archfile_info.setFile_6B(getFilePath(clobContentInfo.getFile_6B()));
        archfile_info.setFile_80(getFilePath(clobContentInfo.getFile_80()));
        archfile_info.setFile_8A(getFilePath(clobContentInfo.getFile_8A()));
        archfile_info.setFile_82(getFilePath(clobContentInfo.getFile_82()));
        archfile_info.setFile_7C(getFilePath(clobContentInfo.getFile_7C()));
        archfile_info.setFile_7D(getFilePath(clobContentInfo.getFile_7D()));
        archfile_info.setFile_Bm(getFilePath(clobContentInfo.getFile_Bm()));
        archfile_info.setFile_7W(getFilePath(clobContentInfo.getFile_7W()));
        archfile_info.setFile_7X(getFilePath(clobContentInfo.getFile_7X()));
        // 主流程
        auditResultResp.setFinish_flag("0");

        auditInfo.setArchfile_info(archfile_info);
        IDCardInfo idCardInfo = clobContentInfo.getId_card_info();
        UserBaseInfo userBaseInfo = clobContentInfo.getUser_base_info();
        IdCardInfoResp id_card_info = new IdCardInfoResp();
        id_card_info.setId_address(StringUtils.isNotBlank(idCardInfo.getId_address()) ? idCardInfo.getId_address() : "");
        id_card_info.setId_begindate(StringUtils.isNotBlank(idCardInfo.getId_begindate()) ? idCardInfo.getId_begindate() : "");
        id_card_info.setId_enddate(StringUtils.isNotBlank(idCardInfo.getId_enddate()) ? idCardInfo.getId_enddate() : "");
        id_card_info.setAuxiliary_id_address(StringUtils.isNotBlank(idCardInfo.getAuxiliary_id_address()) ? idCardInfo.getAuxiliary_id_address() : "");
        id_card_info.setAuxiliary_id_begindate(StringUtils.isNotBlank(idCardInfo.getAuxiliary_id_begindate()) ? idCardInfo.getAuxiliary_id_begindate() : "");
        id_card_info.setAuxiliary_id_enddate(StringUtils.isNotBlank(idCardInfo.getAuxiliary_id_enddate()) ? idCardInfo.getAuxiliary_id_enddate() : "");
        id_card_info.setAuxiliary_id_kind(StringUtils.isNotBlank(idCardInfo.getAuxiliary_id_kind()) ? idCardInfo.getAuxiliary_id_kind() : "");
        id_card_info.setAuxiliary_client_name(StringUtils.isNotBlank(idCardInfo.getAuxiliary_client_name()) ? idCardInfo.getAuxiliary_client_name() : "");
        id_card_info.setAuxiliary_type(StringUtils.isNotBlank(idCardInfo.getAuxiliary_type()) ? idCardInfo.getAuxiliary_type() : "");
        id_card_info.setClient_name(StringUtils.isNotBlank(idCardInfo.getClient_name()) ? idCardInfo.getClient_name() : "");
        id_card_info.setEnglish_name(StringUtils.isNotBlank(idCardInfo.getEnglish_name()) ? idCardInfo.getEnglish_name() : "");
        id_card_info.setNationality(StringUtils.isNotBlank(idCardInfo.getNationality()) ? idCardInfo.getNationality() : "");
        id_card_info.setPermit_no(StringUtils.isNotBlank(idCardInfo.getPermit_no()) ? idCardInfo.getPermit_no() : "");
        auditInfo.setId_card_info(id_card_info);
        UserBaseInfoResp user_base_info = new UserBaseInfoResp();
        user_base_info.setDishonest_record(StringUtils.isNotBlank(clobContentInfo.getDishonest_record()) ? clobContentInfo.getDishonest_record() : "");
        user_base_info.setDishonest_record_remark(StringUtils.isNotBlank(clobContentInfo.getDishonest_record_remark()) ? clobContentInfo.getDishonest_record_remark() : "");
        user_base_info.setChoose_branch_reason(StringUtils.isNotBlank(userBaseInfo.getChoose_branch_reason()) ? userBaseInfo.getChoose_branch_reason() : "");
        user_base_info.setChoose_profession_reason(StringUtils.isNotBlank(userBaseInfo.getChoose_profession_reason()) ? userBaseInfo.getChoose_profession_reason() : "");
        user_base_info.setProfession_other(StringUtils.isNotBlank(userBaseInfo.getProfession_other()) ? userBaseInfo.getProfession_other() : "");
        user_base_info.setAddress(StringUtils.isNotBlank(userBaseInfo.getAddress()) ? userBaseInfo.getAddress() : "");
        user_base_info.setWork_unit(StringUtils.isNotBlank(userBaseInfo.getWork_unit()) ? userBaseInfo.getWork_unit() : "");
        user_base_info.setProfession_code(StringUtils.isNotBlank(userBaseInfo.getProfession_code()) ? userBaseInfo.getProfession_code() : "");
        user_base_info.setDishonest_content(StringUtils.isNotBlank(clobContentInfo.getDishonest_content()) ? getFilePath(clobContentInfo.getDishonest_content()) : "");
        user_base_info.setEnglish_name(StringUtils.isNotBlank(idCardInfo.getEnglish_name()) ? idCardInfo.getEnglish_name() : "");
        user_base_info.setPrev_id_number(StringUtils.isNotBlank(idCardInfo.getPrev_id_number()) ? idCardInfo.getPrev_id_number() : "");
        user_base_info.setInitial_investment_amount(StringUtils.isNotBlank(clobContentInfo.getInitial_investment_amount()) ? clobContentInfo.getInitial_investment_amount() : "");
        user_base_info.setBirthday(StringUtils.isNotBlank(idCardInfo.getBirthday()) ? idCardInfo.getBirthday() : "");
        auditInfo.setUser_base_info(user_base_info);
        auditResultResp.setAudit_info(auditInfo);
        // 是否修改用户信息update_info_flag
        //
        BusinFlowTask fisrtTask = businFlowTaskList.get(businFlowTaskList.size() - 1);
        Date first_create_datetime = fisrtTask.getCreate_datetime();
        QueryWrapper<CustModifyRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("request_no", request_no);
        queryWrapper.ge("modify_datetime", first_create_datetime);
        if (lastTask.getSerial_id().equals(flowtask_id)) {
            //判断是否为终态
            String finish_flag = clobContentInfo.getFinish_flag();
            if ("1".equals(finish_flag)) {
                queryWrapper.le("modify_datetime", lastTask.getCreate_datetime());
                auditResultResp.setFinish_flag("1");
            }
        }
        List<CustModifyRecord> list = custModifyRecordService.getBaseMapper().selectList(queryWrapper);
        List<CustModifyRecord> custModifyRecordList = list.stream().filter(cust -> !StringUtils.equals(cust.getModify_item(), "profession_other") && !StringUtils.equals(cust.getModify_item(), "file_80")).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(custModifyRecordList)) {
            auditResultResp.setUpdate_info_flag("1");// 更改
        } else {
            //再判断是否存在截图
            List<CustModifyRecord> file_80_list = list.stream().filter(cust -> StringUtils.equals(cust.getModify_item(), "file_80")).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(file_80_list)) {
                QueryWrapper<BusinFlowRecord> recordWrapper = new QueryWrapper<>();
                recordWrapper.eq("request_no", request_no);
                recordWrapper.eq("business_flag", FlowRecordEnum.B1017.getValue());
                queryWrapper.ge("create_datetime", first_create_datetime);
                if (lastTask.getSerial_id().equals(flowtask_id)) {
                    //判断是否为终态
                    String finish_flag = clobContentInfo.getFinish_flag();
                    if ("1".equals(finish_flag)) {
                        recordWrapper.le("create_datetime", lastTask.getCreate_datetime());
                    }
                }
                recordWrapper.orderByDesc("create_datetime");
                List<BusinFlowRecord> records = businFlowRecordService.list(recordWrapper);
                for (BusinFlowRecord record : records) {//最后一次的记录是上传还是截图
                    if (StringUtils.contains(record.getBusi_content(), "\"upload_80_type\":\"upload\"")) {//上传
                        auditResultResp.setUpdate_info_flag("1");// 更改
                        break;
                    } else if (StringUtils.contains(record.getBusi_content(), "\"upload_80_type\":\"screenshot\"")) {//截图
                        auditResultResp.setUpdate_info_flag("0");// 未更改
                        break;
                    }
                }
            } else {
                auditResultResp.setUpdate_info_flag("0");// 未更改
            }
        }
        //见证信息（避免打回数据的影响）
        OperatorInfo operatorInfo = new OperatorInfo();
        getOperatorInfoV1(operatorInfo, flowtask_id, task_id, businFlowRequest.getAnode_id());

        auditResultResp.setOperator_info(operatorInfo);
        // 是否存在二次复核exist_double_review
        if (clobContentInfo.getFinish_node().contains(FlowNodeConst.SECONDARY_REVIEW)) {
            auditResultResp.setExist_double_review("1");// 存在
        } else {
            auditResultResp.setExist_double_review("0");// 不存在
        }
        String finish_node = clobContentInfo.getFinish_node();
        if (StringUtils.isNotBlank(finish_node)) {
            String[] finish_nodes = finish_node.split(",");
            auditResultResp.setAnode_id(finish_nodes[finish_nodes.length - 1]);// 当前节点
        }
        // 人脸对比分数（公安照和大头照）face_score_82_80
        if (StringUtils.isNotBlank(clobContentInfo.getFace_score_82_80())) {
            auditResultResp.setFace_score_82_80(clobContentInfo.getFace_score_82_80());
        }
        // 证通分数和证通分数描述结果
        auditResultResp.setFace_score(StringUtils.isNotBlank(clobContentInfo.getFace_score()) ? clobContentInfo.getFace_score() : "");
        auditResultResp.setFace_score_desc(StringUtils.isNotBlank(clobContentInfo.getFace_score_desc()) ? clobContentInfo.getFace_score_desc() : "");
        log.info("查询业务编号[{}],返回结果为[{}]", request_no, JSON.toJSONString(auditResultResp));
        return auditResultResp;
    }


    @Transactional
    @Override
    public Result openapiSubmitBidirectionalAudit(String taskId) {
        BusinFlowTask businFlowTask =
                businFlowTaskService.getCurrTaskTypeByTaskId(taskId, TaskTypeEnum.AUDIT.getCode());

        if (!StringUtils.equalsAny(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_PASS)) {
            return Result.fail("见证任务未通过，无法创建复核任务！");
        }

        log.info("双向视频开始提交复核申请，request_no={} task_id={} serial_id={}", businFlowTask.getRequest_no(), businFlowTask.getTask_id(), businFlowTask.getSerial_id());
        String requestNo = businFlowTask.getRequest_no();
        BusinFlowRequest businFlowRequest = businFlowRequestService.getById(requestNo);
        // 创建复审任务
        CreateTaskResult taskResult = requestFlowService.autoCreateTask(businFlowRequest, businFlowTask.getTask_type(), businFlowTask.getOperator_no(), taskId);
        //更新申请信息
        LambdaUpdateWrapper<BusinFlowRequest> requestWrapper = new LambdaUpdateWrapper<>();
        requestWrapper.set(BusinFlowRequest::getUpdate_datetime, new Date());
        requestWrapper.set(BusinFlowRequest::getRequest_status, taskResult.getRequest_status());
        requestWrapper.set(BusinFlowRequest::getAnode_id, taskResult.getAnode_id());
        requestWrapper.eq(BusinFlowRequest::getRequest_no, businFlowTask.getRequest_no());
        businFlowRequestService.update(requestWrapper);

        Map<String, Object> params = new HashMap<>();
        params.put(Fields.REQUEST_STATUS, FlowStatusConst.REQUEST_STATUS_PENDING_AUDIT);
        params.put(Fields.REVIEW_SUBMIT_DATETIME, DateUtil.formatDateTime(new Date()));
        params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22401);
        params.put(Fields.OPERATOR_NO, WskhConstant.SUPER_USER);
        params.put(Fields.OPERATOR_NAME, WskhConstant.SUPER_USER);
        params.put(Fields.BUSINESS_REMARK, "用户提交双向视频复核申请");
        requestFlowService.saveParamsRecord(requestNo, params);

        log.info("双向视频结束提交复核申请，request_no={} task_id={} serial_id={}", businFlowTask.getRequest_no(), businFlowTask.getTask_id(), businFlowTask.getSerial_id());
        return Result.success();
    }


    private String getFilePath(String filerecord_id) {
        if (StringUtils.isNotBlank(filerecord_id)) {
            ElectDownloadFileRequest electDownloadFileRequest = new ElectDownloadFileRequest();
            electDownloadFileRequest.setFilerecord_id(filerecord_id);
            electDownloadFileRequest.setNot_download(true);

            ElectDownloadFileResponse electDownloadFileResponse = componentElectDubboService.electDownloadFile(electDownloadFileRequest);
            return electDownloadFileResponse.getHttp_url();
        }
        return "";

    }


    public void getOperatorInfoV1(OperatorInfo operatorInfo, String flowtask_id, String task_id, String anode_id) {
        ParamsSavingUtils.noNullStringAttr(operatorInfo);
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNoV1(flowtask_id);
        // 审核信息
        BusinFlowTask auditTask = businFlowTaskService.getCurrTaskTypeByTaskId(task_id, FlowNodeConst.AUDIT);
        if (auditTask != null && auditTask.getCreate_datetime().compareTo(clobContentInfo.getRequest_datetime()) != -1) {
            operatorInfo.setAudit_operator_no(auditTask.getOperator_no());
            operatorInfo.setAudit_datetime(KHDateUtil.formatDate(auditTask.getFinish_datetime(), KHDateUtil.DATE_TIME_FORMAT) == null ? "" : KHDateUtil.formatDate(auditTask.getFinish_datetime(), KHDateUtil.DATE_TIME_FORMAT));
        }
        // 复核信息
        if (StringUtils.equals(FlowNodeConst.REVIEW, anode_id) || StringUtils.equals(FlowNodeConst.SECONDARY_REVIEW, anode_id) || StringUtils.equals(FlowNodeConst.END, anode_id)) {
            BusinFlowTask reviewTask = businFlowTaskService.getCurrTaskTypeByTaskId(task_id, FlowNodeConst.REVIEW);
            if (reviewTask != null && reviewTask.getCreate_datetime().compareTo(clobContentInfo.getRequest_datetime()) != -1) {
                operatorInfo.setReview_operator_no(reviewTask.getOperator_no());
                operatorInfo.setReview_datetime(KHDateUtil.formatDate(reviewTask.getFinish_datetime(), KHDateUtil.DATE_TIME_FORMAT) == null ? "" : KHDateUtil.formatDate(reviewTask.getFinish_datetime(), KHDateUtil.DATE_TIME_FORMAT));
            }
            if (StringUtils.equals(FlowNodeConst.SECONDARY_REVIEW, anode_id) || StringUtils.equals(FlowNodeConst.END, anode_id)) {
                // 二次复核信息
                BusinFlowTask secondaryReviewTask = businFlowTaskService.getCurrTaskTypeByTaskId(task_id, FlowNodeConst.SECONDARY_REVIEW);
                if (secondaryReviewTask != null && secondaryReviewTask.getCreate_datetime().compareTo(clobContentInfo.getRequest_datetime()) != -1) {
                    operatorInfo.setDouble_operator_no(secondaryReviewTask.getOperator_no());
                    operatorInfo.setDouble_datetime(KHDateUtil.formatDate(secondaryReviewTask.getFinish_datetime(), KHDateUtil.DATE_TIME_FORMAT) == null ? "" : KHDateUtil.formatDate(secondaryReviewTask.getFinish_datetime(), KHDateUtil.DATE_TIME_FORMAT));
                }
            }
        }
    }

    /**
     * 双向视频同一个客户再次发起，清理之前生成的任务
     *
     * @param requestNo
     */
    private void clearOldBidirectionalTask(String requestNo) {
        log.info("[任务作废标记]clearOldBidirectionalTask开户申请提交同一个客户再次发起，清理之前生成的任务 requestNo = {}", requestNo);
        if (StringUtils.isEmpty(requestNo)) {
            log.error("[任务作废标记]clearOldBidirectionalTask参数异常，requestNo为空");
            return;
        }
        // 查询之前任务
        LambdaQueryWrapper<BusinFlowTask> taskWrapper = new LambdaQueryWrapper<>();
        taskWrapper.eq(BusinFlowTask::getRequest_no, requestNo)
                .in(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_PENDING, FlowStatusConst.AUDIT_AUDITING, FlowStatusConst.AUDIT_TRANSFER);
        businFlowTaskService.list(taskWrapper).forEach(businFlowTask -> {
            // 任务作废处理
            bidirectionalVideoHandlerService.processVideoInterruption(businFlowTask, VideoConstant.BIDIRECTIONAL_CLEAR_REPEAT_SUBMIT);
        });
    }

}
