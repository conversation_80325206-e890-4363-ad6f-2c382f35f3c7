package com.cairh.cpe.backend.form.resp;


import com.baomidou.mybatisplus.annotation.TableId;
import com.cairh.cpe.cache.annotation.DataConvert;
import com.cairh.cpe.common.entity.RepulsetReasonConfig;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class RepulsetReasonConfigResult {

    /**
     * 流水号
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 任务类别
     */
    private String audit_type;

    /**
     * 证件类型
     */
    @DataConvert(code_dict = "id_kind")
    private String id_kind;

    /**
     * 前端节点
     */
    @DataConvert(code_dict = "reject_reason_anode")
    private String anode_id;

    /**
     * 整改原因名称
     */
    private String cause_name;

    /**
     * 整改原因内容
     */
    private String cause_content;

    /**
     * 整改原因分组
     */
    @DataConvert(code_dict = "reject_reason_group")
    private String cause_group;

    /**
     * 顺序号
     */
    private Integer order_no;

    /**
     * 创建人
     */
    private String create_by;

    /**
     * 创建日期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;

    /**
     * 修改人
     */
    private String update_by;

    /**
     * 更新日期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date update_datetime;

    //字节点的配置项
    private List<RepulsetReasonConfig> children_config;
}
