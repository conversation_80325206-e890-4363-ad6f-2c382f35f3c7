package com.cairh.cpe.backend.task;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.cairh.cpe.backend.task.call.CallBackMqOperator;
import com.cairh.cpe.backend.task.call.CallBackMqOperator.CallBackMsg;
import com.cairh.cpe.common.constant.CallConstant;
import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.common.constant.ErrorEnum;
import com.cairh.cpe.common.constant.LockKeyConstant;
import com.cairh.cpe.common.entity.CallDetails;
import com.cairh.cpe.common.service.ICallDetailsService;
import com.cairh.cpe.common.util.DateUtils;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.esb.component.notice.IEsbComponentSmsDubboService;
import com.cairh.cpe.esb.component.notice.dto.req.NoticeSendSmsRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MQCallBackHandle implements ApplicationListener<ApplicationReadyEvent> {


    @Resource
    private CallBackMqOperator callBackMqOperator;

    @Resource
    private RedissonUtil redissonUtil;

    @Resource
    private ICallDetailsService iCallDetailsService;

    @DubboReference
    private IEsbComponentSmsDubboService noticeDubboService;

    @Autowired
    protected CompositePropertySources compositePropertySources;


    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        callBackMqOperator.receive(item -> {
            try {
                consumerMessage(item);
            } catch (Exception e) {
                log.debug("一键外呼获取kafka消息异常", e);
            }
        });
    }

    public void consumerMessage(CallBackMsg data) {
        log.info("kafka接收到消息 一键外呼回传信息,CallBackMsg= {}", JSON.toJSONString(data));
        //非电销不处理
        if (!StringUtils.equals(data.getMSG_SUBTYPE(),CallConstant.CALL_MSG_SUBTYPE)) {
            return;
        }
        if (!StringUtils.equals(data.getMSG_TYPE(),CallConstant.CALL_MSG_TYPE)) {
            return;
        }
        if (ObjectUtil.isNull(data.getCONTENT())) {
            return;
        }
        if (!(data.getCONTENT() instanceof JSONObject)) {
            log.info("content JSON 格式不是JSONObject需过滤掉");
            return;
        }

        CallBackMsg.MsgContent msgContent = JSON.parseObject(data.getCONTENT().toString(), CallBackMsg.MsgContent.class);
        if (null == msgContent.getDeptId() || msgContent.getDeptId().intValue() != CallConstant.CALL_DEPT_ID) {
            return;
        }
        String lockKey = String.format(LockKeyConstant.CALL_DETAILS_CALL_ID, msgContent.getCallId());
        try {
            boolean isLock = redissonUtil.tryLock(lockKey, 1, 20, TimeUnit.SECONDS);
            if (!isLock) {
                log.error("执行consumerMessage方法长时间未获取锁，【{}】", msgContent.getCallId());
                throw new BizException(ErrorEnum.AUDIT_TASK_MQ_ERROR_TIP.getValue(), ErrorEnum.AUDIT_TASK_MQ_ERROR_TIP.getDesc());
            }
            //更新记录信息
            Date date = new Date();
            CallDetails callDetails = new CallDetails();
            callDetails.setModify_datetime(date);
            callDetails.setCall_number(msgContent.getCallNumber());
            if (StringUtils.isNotBlank(msgContent.getCalleeCreatedEpoch())) {
                callDetails.setCalleeCreatedEpoch(DateUtils.parseDate(msgContent.getCalleeCreatedEpoch(), DateUtils.DATE_TIME_FORMAT));
            }
            if (StringUtils.isNotBlank(msgContent.getCalleeAnswerEpoch())) {
                callDetails.setCalleeAnswerEpoch(DateUtils.parseDate(msgContent.getCalleeAnswerEpoch(), DateUtils.DATE_TIME_FORMAT));
            }
            if (StringUtils.isNotBlank(msgContent.getCalleeRingingEpoch())) {
                callDetails.setCalleeRingingEpoch(DateUtils.parseDate(msgContent.getCalleeRingingEpoch(), DateUtils.DATE_TIME_FORMAT));
            }
            if (StringUtils.isNotBlank(msgContent.getCalleeHangupEpoch())) {
                callDetails.setCalleeHangupEpoch(DateUtils.parseDate(msgContent.getCalleeHangupEpoch(), DateUtils.DATE_TIME_FORMAT));
            }
            callDetails.setRecordingFileName(msgContent.getRecordingFileName());
            callDetails.setOutboundCalleeNumber(msgContent.getOutboundCalleeNumber());
            callDetails.setCustomerid(msgContent.getCustomerid());
            callDetails.setHangUpSide(msgContent.getHangUpSide());
            callDetails.setIsAnswer(msgContent.getIsAnswer());
            callDetails.setCallDuration(msgContent.getCallDuration());
            callDetails.setDeptId(msgContent.getDeptId() + "");
            callDetails.setCallId(msgContent.getCallId());
            callDetails.setFinish_datetime(date);
            callDetails.setCall_status(CallConstant.CALL_STATUS_FINISH);
            callDetails.setSerial_id(msgContent.getCallbizid());
            callDetails.setCallbizkey(msgContent.getCallbizkey());
            boolean bool = iCallDetailsService.updateById(callDetails);
            if (!bool) {
                log.error("一键外呼更新记录信息失败,serial_id={}", msgContent.getCallbizid());
            }
            // 若接通失败则发送短信
            if (callDetails.getIsAnswer().equals(CallConstant.IS_ANSWER) && CallConstant.OPEN.equals(compositePropertySources.getProperty(CallConstant.MESSAGE_SWITCH))) {
                NoticeSendSmsRequest noticeSendSmsRequest = new NoticeSendSmsRequest();
                noticeSendSmsRequest.setSend_type(Constant.SEND_TYPE);
                noticeSendSmsRequest.setChannel_type(Constant.CHANNEL_TYPE);
                noticeSendSmsRequest.setMobile_tel(msgContent.getCalleeNumber());
                noticeSendSmsRequest.setMsg_content(compositePropertySources.getProperty(CallConstant.MESSAGE_CONTENT));
                noticeSendSmsRequest.setService_vender(Constant.KAFKA_SERVICE_VENDER);
                log.info("一件外呼发送短信的参数：{}", JSON.toJSONString(noticeSendSmsRequest));
                noticeDubboService.noticeSendSms(noticeSendSmsRequest);
            }

        } catch (Exception e) {
            log.error("一键外呼消费kafka失败", e);
            throw new BizException("-1", e.getMessage());
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }
}
