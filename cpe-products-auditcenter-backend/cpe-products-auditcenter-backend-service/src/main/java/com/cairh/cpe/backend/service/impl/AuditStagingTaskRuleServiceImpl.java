package com.cairh.cpe.backend.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.aiaudit.query.AiAuditQueryDubboService;
import com.cairh.cpe.aiaudit.query.req.AuditRuleQueryRequest;
import com.cairh.cpe.aiaudit.query.resp.AuditRuleQueryResponse;
import com.cairh.cpe.backend.form.req.*;
import com.cairh.cpe.backend.form.resp.StagingTaskRuleDetailResponse;
import com.cairh.cpe.backend.form.resp.StagingTaskRuleListResponse;
import com.cairh.cpe.backend.form.resp.StagingTaskRulePageResponse;
import com.cairh.cpe.backend.service.IAuditStagingTaskRuleService;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.dto.AutoRejectReasonInfo;
import com.cairh.cpe.common.entity.StagingTaskRule;
import com.cairh.cpe.common.service.IStagingTaskRuleService;
import com.cairh.cpe.common.util.DateUtils;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.db.config.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/3/12 09:51
 */
@Slf4j
@Service
public class AuditStagingTaskRuleServiceImpl implements IAuditStagingTaskRuleService {

    @Resource
    private IStagingTaskRuleService stagingTaskRuleService;
    @Resource
    private IdGenerator idGenerator;
    @DubboReference(check = false)
    private AiAuditQueryDubboService aiAuditQueryDubboService;

    @Override
    public Page<StagingTaskRulePageResponse> page(StagingTaskRulePageReq request) {
        Page<StagingTaskRule> page = new Page<>(request.getCur_page(), request.getPage_size());
        LambdaQueryWrapper<StagingTaskRule> wrapper = setQueryWrapper(request);
        Page<StagingTaskRule> rulePage = stagingTaskRuleService.page(page, wrapper);
        List<StagingTaskRulePageResponse> ruleList = rulePage
                .getRecords()
                .stream()
                .map(this::convertData)
                .collect(Collectors.toList());
        Page<StagingTaskRulePageResponse> responsePage = new Page<>(request.getCur_page(), request.getPage_size());
        responsePage.setRecords(ruleList);
        responsePage.setTotal(rulePage.getTotal());
        return responsePage;
    }

    @Override
    public List<StagingTaskRuleListResponse> list(StagingTaskRuleListReq request) {
        LambdaQueryWrapper<StagingTaskRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(StagingTaskRule::getRule_name, StagingTaskRule::getRule_type, StagingTaskRule::getSerial_id);
        wrapper.eq(StagingTaskRule::getStatus, "1");
        wrapper.eq(StringUtils.isNotBlank(request.getRule_type()), StagingTaskRule::getRule_type, request.getRule_type());
        wrapper.orderByAsc(StagingTaskRule::getOrder_no);
        return stagingTaskRuleService.list(wrapper)
                .stream()
                .map(item -> new StagingTaskRuleListResponse().setSerial_id(item.getSerial_id()).setRule_name(item.getRule_name()))
                .collect(Collectors.toList());
    }

    @Override
    public StagingTaskRuleDetailResponse detail(String serial_id) {
        StagingTaskRule rule = stagingTaskRuleService.getById(serial_id);
        if (Objects.isNull(rule)) {
            log.error("[detail]任务规则不存在，serial_id={}", serial_id);
            throw new BizException(ErrorEnum.DATA_NOT_EXIST.getValue(), ErrorEnum.DATA_NOT_EXIST.getDesc());
        }
        StagingTaskRuleDetailResponse response = new StagingTaskRuleDetailResponse();
        BeanUtils.copyProperties(rule, response);
        if (StringUtils.equals(rule.getRule_type(), StagingRuleTypeEnum.AUTO_REJECT.getCode())
                && StringUtils.equals(rule.getReject_type(), AutoRejectSourceTypeEnum.AI_AUDIT.getCode())) {
            List<AutoRejectReasonInfo> autoRejectReasonInfos = JSON.parseArray(rule.getAiaudit_reject_reason(), AutoRejectReasonInfo.class);
            response.setAiaudit_reject_reason(autoRejectReasonInfos.stream().map(AutoRejectReasonInfo::getReject_reason).collect(Collectors.joining(",")));
        }
        return response;
    }

    @Override
    public void addRule(BaseUser baseUser, StagingTaskRuleAddReq request) {
        // 校验规则类型
        if (!StringUtils.equalsAny(request.getRule_type(), StagingRuleTypeEnum.STAGING_TASK.getCode(), StagingRuleTypeEnum.AUTO_REJECT.getCode())) {
            throw new BizException(ErrorEnum.DATA_NOT_EXIST.getValue(), "任务规则类型不存在");
        }
        stagingTaskRuleService.clearCache();
        StagingTaskRule stagingTaskRule = new StagingTaskRule();
        BeanUtils.copyProperties(request, stagingTaskRule);
        // 数据处理
        dataHandle(stagingTaskRule, request);
        Date date = new Date();
        stagingTaskRule.setCreate_by(baseUser.getStaff_no());
        stagingTaskRule.setUpdate_by(baseUser.getStaff_no());
        stagingTaskRule.setCreate_datetime(date);
        stagingTaskRule.setUpdate_datetime(date);
        stagingTaskRule.setStatus(request.getStatus());
        stagingTaskRule.setSerial_id(idGenerator.nextUUID(null));
        stagingTaskRuleService.save(stagingTaskRule);
    }

    @Override
    public void editRule(BaseUser baseUser, StagingTaskRuleEditReq request) {
        StagingTaskRule stagingTaskRule = stagingTaskRuleService.getById(request.getSerial_id());
        if (Objects.isNull(stagingTaskRule)) {
            log.error("[editRule]任务规则不存在，serial_id={}", request.getSerial_id());
            throw new BizException(ErrorEnum.DATA_NOT_EXIST.getValue(), ErrorEnum.DATA_NOT_EXIST.getDesc());
        }
        // 校验规则类型
        if (!StringUtils.equalsAny(request.getRule_type(), StagingRuleTypeEnum.STAGING_TASK.getCode(), StagingRuleTypeEnum.AUTO_REJECT.getCode())) {
            throw new BizException(ErrorEnum.DATA_NOT_EXIST.getValue(), "任务规则类型不存在");
        }
        stagingTaskRuleService.clearCache();
        StagingTaskRule rule = new StagingTaskRule();
        BeanUtils.copyProperties(request, rule);
        // 数据处理
        dataHandle(rule, request);
        LambdaUpdateWrapper<StagingTaskRule> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(StagingTaskRule::getSerial_id, request.getSerial_id());
        rule.setUpdate_by(baseUser.getStaff_no());
        rule.setUpdate_datetime(new Date());
        stagingTaskRuleService.update(rule, updateWrapper);
    }

    @Override
    public void updateStatus(BaseUser baseUser, StagingTaskRuleStatusReq request) {
        StagingTaskRule stagingTaskRule = stagingTaskRuleService.getById(request.getSerial_id());
        if (Objects.isNull(stagingTaskRule)) {
            log.error("[updateStatus]任务规则不存在，serial_id={}", request.getSerial_id());
            throw new BizException(ErrorEnum.DATA_NOT_EXIST.getValue(), ErrorEnum.DATA_NOT_EXIST.getDesc());
        }
        stagingTaskRuleService.clearCache();
        LambdaUpdateWrapper<StagingTaskRule> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(StagingTaskRule::getSerial_id, request.getSerial_id());
        updateWrapper.set(StagingTaskRule::getStatus, request.getStatus());
        updateWrapper.set(StagingTaskRule::getUpdate_by, baseUser.getStaff_no());
        updateWrapper.set(StagingTaskRule::getUpdate_datetime, new Date());
        stagingTaskRuleService.update(updateWrapper);
    }

    @Override
    public void deleteRule(String serial_id) {
        StagingTaskRule stagingTaskRule = stagingTaskRuleService.getById(serial_id);
        if (Objects.isNull(stagingTaskRule)) {
            log.error("[deleteRule]任务规则不存在，serial_id={}", serial_id);
            throw new BizException(ErrorEnum.DATA_NOT_EXIST.getValue(), ErrorEnum.DATA_NOT_EXIST.getDesc());
        }
        if (StringUtils.equals(stagingTaskRule.getStatus(), WskhConstant.AVAILABLE_STATUS)) {
            throw new BizException(ErrorEnum.DATA_STATUS_ERROR.getValue(), ErrorEnum.DATA_STATUS_ERROR.getDesc());
        }
        LambdaQueryWrapper<StagingTaskRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StagingTaskRule::getSerial_id, serial_id);
        stagingTaskRuleService.clearCache();
        stagingTaskRuleService.remove(wrapper);
    }


    private StagingTaskRulePageResponse convertData(StagingTaskRule rule) {
        StagingTaskRulePageResponse response = new StagingTaskRulePageResponse();
        BeanUtils.copyProperties(rule, response);
        return response;
    }

    private LambdaQueryWrapper<StagingTaskRule> setQueryWrapper(StagingTaskRulePageReq request) {
        LambdaQueryWrapper<StagingTaskRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(request.getRule_name()), StagingTaskRule::getRule_name, request.getRule_name());
        wrapper.eq(StringUtils.isNotBlank(request.getRule_type()), StagingTaskRule::getRule_type, request.getRule_type());
        wrapper.eq(StringUtils.isNotBlank(request.getStatus()), StagingTaskRule::getStatus, request.getStatus());
        wrapper.ge(StringUtils.isNotBlank(request.getUpdate_datetime_start()), StagingTaskRule::getUpdate_datetime, DateUtils.parseDateStrictly(request.getUpdate_datetime_start(), DateUtils.DATE_TIME_FORMAT2));
        wrapper.le(StringUtils.isNotBlank(request.getUpdate_datetime_end()), StagingTaskRule::getUpdate_datetime, DateUtils.parseDateStrictly(request.getUpdate_datetime_end(), DateUtils.DATE_TIME_FORMAT2));
        return wrapper;
    }

    /**
     * 新增规则以及编辑规则数据处理
     */
    private void dataHandle(StagingTaskRule stagingTaskRule, StagingTaskRuleAddReq request) {
        // 暂存任务
        if (StringUtils.equals(StagingRuleTypeEnum.STAGING_TASK.getCode(), stagingTaskRule.getRule_type())) {
            // 校验规则开始时间
            if (StringUtils.isBlank(request.getRule_datetime_start())) {
                throw new BizException(ErrorEnum.DATA_NOT_NULL_ERROR.getValue(), "不参与派单开始时间不能为空");
            }
            // 校验规则结束时间
            if (StringUtils.isBlank(request.getRule_datetime_end())) {
                throw new BizException(ErrorEnum.DATA_NOT_NULL_ERROR.getValue(), "不参与派单结束时间不能为空");
            }
            // 校验规则条件
            if (StringUtils.isBlank(request.getExpression())) {
                throw new BizException(ErrorEnum.DATA_NOT_NULL_ERROR.getValue(), "不参与派单条件不能为空");
            }
            stagingTaskRule.setRule_datetime_start(DateUtils.parseDateStrictly(request.getRule_datetime_start(), DateUtils.DATE_TIME_FORMAT));
            stagingTaskRule.setRule_datetime_end(DateUtils.parseDateStrictly(request.getRule_datetime_end(), DateUtils.DATE_TIME_FORMAT));
        } else if (StringUtils.equals(StagingRuleTypeEnum.AUTO_REJECT.getCode(), request.getRule_type())) {
            // 校验驳回类型
            if (!StringUtils.equalsAny(request.getReject_type(), AutoRejectSourceTypeEnum.BASE_INFO.getCode(), AutoRejectSourceTypeEnum.AI_AUDIT.getCode())) {
                throw new BizException(ErrorEnum.DATA_NOT_EXIST.getValue(), "任务规则类型不存在");
            }
            // 校验业务类型
            if (StringUtils.isBlank(request.getBusin_type())) {
                throw new BizException(ErrorEnum.DATA_NOT_NULL_ERROR.getValue(), "业务类型不能为空");
            }
            // 校验任务类型
            if (StringUtils.isBlank(request.getTask_type())) {
                throw new BizException(ErrorEnum.DATA_NOT_NULL_ERROR.getValue(), "任务类型不能为空");
            }
            // 驳回类型为基础信息
            if (StringUtils.equals(AutoRejectSourceTypeEnum.BASE_INFO.getCode(), request.getReject_type())) {
                // 校验条件
                if (StringUtils.isBlank(request.getExpression())) {
                    throw new BizException(ErrorEnum.DATA_NOT_NULL_ERROR.getValue(), "条件不能为空");
                }
                // 校验驳回原因
                if (StringUtils.isBlank(request.getReject_reason())) {
                    throw new BizException(ErrorEnum.DATA_NOT_NULL_ERROR.getValue(), "驳回原因不能为空");
                }
            } else {
                // 关联智能审核项
                if (StringUtils.isBlank(request.getAiaudit_items())) {
                    throw new BizException(ErrorEnum.DATA_NOT_NULL_ERROR.getValue(), "关联智能审核项不能为空");
                }
                // 关联智能审核驳回原因
                if (StringUtils.isBlank(request.getAiaudit_reject_reason())) {
                    throw new BizException(ErrorEnum.DATA_NOT_NULL_ERROR.getValue(), "关联智能审核驳回原因不能为空");
                }
                AuditRuleQueryRequest req = new AuditRuleQueryRequest();
                req.setSubsys_id(Integer.parseInt(WskhConstant.SUBSYS_ID));
                req.setBusin_id(Integer.parseInt(request.getBusin_type()));
                List<AuditRuleQueryResponse> respList = aiAuditQueryDubboService.queryAuditRule(req);
                if (CollectionUtil.isEmpty(respList)) {
                    throw new BizException(ErrorEnum.DATA_NOT_EXIST.getValue(), "该业务类型没有配置智能审核规则");
                }
                // 构建智能审核驳回原因信息
                List<AutoRejectReasonInfo> autoRejectReasonInfoList = buildAutoRejectReasonInfoList(
                        respList, request.getAiaudit_items(), request.getTask_type());
                stagingTaskRule.setAiaudit_reject_reason(JSON.toJSONString(autoRejectReasonInfoList));
            }
        }
    }

    /**
     * 构建智能审核驳回原因信息列表
     */
    private List<AutoRejectReasonInfo> buildAutoRejectReasonInfoList(List<AuditRuleQueryResponse> respList,
                                                                     String aiauditItems, String taskType) {
        // 构建规则映射，过滤掉没有驳回原因的规则
        Map<String, AuditRuleQueryResponse> ruleMap = respList.stream()
                .filter(response -> StringUtils.isNotBlank(response.getReject_reason()))
                .collect(Collectors.toMap(AuditRuleQueryResponse::getDrl_rule_name, Function.identity()));

        // 解析智能审核项目列表
        List<String> aiauditItemsList = Arrays.asList(aiauditItems.split(","));

        // 构建驳回原因信息列表
        return aiauditItemsList.stream()
                .map(item -> createAutoRejectReasonInfo(ruleMap.get(item), taskType))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
    }

    /**
     * 创建自动驳回原因信息
     */
    private Optional<AutoRejectReasonInfo> createAutoRejectReasonInfo(AuditRuleQueryResponse response, String taskType) {
        if (response == null) {
            return Optional.empty();
        }

        // 解析驳回原因列表
        List<String> rejectReasonList = Arrays.asList(response.getReject_reason().split("\\^"));
        if (CollectionUtil.isEmpty(rejectReasonList)) {
            return Optional.empty();
        }

        // 根据任务类型获取对应的驳回原因 audit：第一个 review：第二个 secondary_review：第三个
        Map<String, Integer> taskTypeIndexMap = new HashMap<>();
        taskTypeIndexMap.put(TaskTypeEnum.AUDIT.getCode(), 0);
        taskTypeIndexMap.put(TaskTypeEnum.REVIEW.getCode(), 1);
        taskTypeIndexMap.put(TaskTypeEnum.SECONDARY_REVIEW.getCode(), 2);

        Integer index = taskTypeIndexMap.get(taskType);
        if (index == null || index >= rejectReasonList.size()) {
            return Optional.empty();
        }

        String reason = rejectReasonList.get(index);
        Optional<String> rejectReason = StringUtils.isNotBlank(reason) ? Optional.of(reason) : Optional.empty();
        if (!rejectReason.isPresent()) {
            return Optional.empty();
        }

        // 构建驳回原因信息对象
        AutoRejectReasonInfo autoRejectReasonInfo = new AutoRejectReasonInfo();
        autoRejectReasonInfo.setDrl_rule_name(response.getDrl_rule_name());
        autoRejectReasonInfo.setReject_reason(rejectReason.get());

        return Optional.of(autoRejectReasonInfo);
    }
}
