package com.cairh.cpe.backend.service.impl;

import com.alibaba.fastjson2.JSON;
import com.cairh.cpe.backend.form.resp.VerificationResult;
import com.cairh.cpe.backend.form.support.VerifyPoliceInfo;
import com.cairh.cpe.backend.service.VerificationStrategy;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.request.VerifyPoliceReq;
import com.cairh.cpe.common.entity.response.VerifyPoliceResp;
import com.cairh.cpe.service.idverify.IComponentIdVerifyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 标准业务策略
 * T2接口：11804280
 *
 * <AUTHOR>
 * @since 2025/5/7 10:45
 */
@Slf4j
@Service
public class VerifyPoliceVerificationStrategy implements VerificationStrategy {

    @Resource
    private IComponentIdVerifyService componentIdVerifyService;

    @Override
    public VerificationResult verify(ClobContentInfo clob, VerifyPoliceInfo info,
                                     boolean isNewDataSign, boolean workTime) {
        VerifyPoliceReq req = new VerifyPoliceReq();
        req.setFull_name(info.getClient_name());
        req.setId_no(info.getId_no());
        req.setId_kind(info.getId_kind());
        req.setOrgan_flag(WskhConstant.ORGAN_FLAG);
        req.setReal_branch_no(clob.getReal_branch_no());
        req.setRealtime_flag("1");
        req.setBranch_no(StringUtils.isBlank(clob.getReal_branch_no()) ? clob.getBranch_no() : clob.getReal_branch_no());
        req.setOp_branch_no(req.getBranch_no());
        req.setService_vender(PropertySource.get("comp.id.verify.service.provider"));
        log.info("【VerifyPoliceVerificationStrategy】三要素公安认证请求入参：{}", JSON.toJSON(req));
        VerifyPoliceResp resp = componentIdVerifyService.verifyPolice(req);
        log.info("【VerifyPoliceVerificationStrategy】三要素公安认证请求出参：{}", JSON.toJSON(resp));
        return new VerificationResult(resp, isNewDataSign, workTime);
    }

}
