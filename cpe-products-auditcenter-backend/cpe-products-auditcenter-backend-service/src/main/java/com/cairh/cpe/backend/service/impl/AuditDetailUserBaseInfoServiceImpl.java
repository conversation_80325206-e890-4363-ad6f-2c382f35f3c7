package com.cairh.cpe.backend.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cairh.cpe.backend.form.req.BaseInfoForm;
import com.cairh.cpe.backend.form.resp.AuditDetailUserBaseInfo;
import com.cairh.cpe.backend.service.IAuditDetailUserBaseInfoService;
import com.cairh.cpe.cache.component.ComponentWorkTimeService;
import com.cairh.cpe.cache.service.CacheChannel;
import com.cairh.cpe.common.constant.ErrorEnum;
import com.cairh.cpe.common.constant.LabelTypeConst;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.Label;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.response.MobileLocationQryResp;
import com.cairh.cpe.common.mapper.BusinFlowTaskMapper;
import com.cairh.cpe.common.service.IBusinFlowRequestService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.service.idverify.IComponentIdVerifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class AuditDetailUserBaseInfoServiceImpl implements IAuditDetailUserBaseInfoService {

    @Autowired
    private IRequestService requestService;
    @Autowired
    private CacheChannel cacheChannel;
    @Autowired
    private IComponentIdVerifyService componentIdVerifyService;
    @Autowired
    private IBusinFlowRequestService businFlowRequestService;
    @Resource
    private BusinFlowTaskMapper businFlowTaskMapper;
    @Autowired
    private ComponentWorkTimeService componentWorkTimeService;


    @Override
    public Result<AuditDetailUserBaseInfo> getUserSource(BaseInfoForm baseInfoForm) {
        String request_no = baseInfoForm.getRequest_no();
        String flowtask_id = baseInfoForm.getFlowtask_id();
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(request_no);
        BusinFlowRequest byId = businFlowRequestService.getById(request_no);
        AuditDetailUserBaseInfo auditDetailUserBaseInfo = new AuditDetailUserBaseInfo();
        auditDetailUserBaseInfo.setChannel_name(clobContentInfo.getChannel_name());
        auditDetailUserBaseInfo.setChannel_code(clobContentInfo.getChannel_code());
        auditDetailUserBaseInfo.setOpen_channel(clobContentInfo.getOpen_channel());
        auditDetailUserBaseInfo.setBusin_type(byId.getBusin_type());
        auditDetailUserBaseInfo.setBusin_type_code(byId.getBusin_type());
        auditDetailUserBaseInfo.setActivity_name(clobContentInfo.getActivity_name());
        auditDetailUserBaseInfo.setMarketing_team(clobContentInfo.getMarketing_team());
        auditDetailUserBaseInfo.setMobile_tel(clobContentInfo.getMobile_tel());

        if (StrUtil.isNotBlank(clobContentInfo.getMobile_location())) {
            auditDetailUserBaseInfo.setMobile_location(clobContentInfo.getMobile_location());
        } else {
            MobileLocationQryResp mobileLocationQryResp = componentIdVerifyService.baseDataQryMobileLocation(clobContentInfo.getMobile_tel());
            if (mobileLocationQryResp != null) {
                auditDetailUserBaseInfo.setMobile_location(mobileLocationQryResp.getProvince_name() + mobileLocationQryResp.getCity_name() + mobileLocationQryResp.getOperator());
            }
        }
        auditDetailUserBaseInfo.setAge(clobContentInfo.getUser_base_info().getAge());
        BusinFlowTask businFlowTask = businFlowTaskMapper.selectById(flowtask_id);
        if (businFlowTask == null) {
            return Result.fail(ErrorEnum.AUDIT_TASK_NOT_EXIST.getDesc());
        }
        auditDetailUserBaseInfo.setTask_id(businFlowTask.getTask_id());
        auditDetailUserBaseInfo.setTask_type(businFlowTask.getTask_type());
        auditDetailUserBaseInfo.setWhite_flag(businFlowTask.getWhite_flag());
        auditDetailUserBaseInfo.setPush_flag(businFlowTask.getPush_flag());
        LambdaQueryWrapper<Label> labelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        labelLambdaQueryWrapper.eq(Label::getLabel_type, LabelTypeConst.LABEL_TYPE_VIP);
        labelLambdaQueryWrapper.select(Label::getSerial_id);
        auditDetailUserBaseInfo.setBroker_name(clobContentInfo.getBroker_name());
        auditDetailUserBaseInfo.setVideo_type(clobContentInfo.getVideo_type());
        auditDetailUserBaseInfo.setId_kind(clobContentInfo.getId_kind());
        auditDetailUserBaseInfo.setVideo_speech_id(clobContentInfo.getVideo_speech_id());
        auditDetailUserBaseInfo.setWork_time(componentWorkTimeService.isWorkTime(WskhConstant.WORK_TIME_ZD_GA) ? "1" : "2");
        return Result.success(auditDetailUserBaseInfo);
    }
}
