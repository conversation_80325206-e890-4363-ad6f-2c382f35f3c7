package com.cairh.cpe.backend.form.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/3/11 18:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StagingTaskRulePageResponse {

    /**
     * 主键ID
     */
    private String serial_id;

    /**
     * 规则名称
     */
    private String rule_name;

    /**
     * 规则类型（1-暂存任务 2-自动驳回）
     */
    private String rule_type;

    /**
     * 规则开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date rule_datetime_start;

    /**
     * 规则结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date rule_datetime_end;

    /**
     * 业务类型
     */
    private String busin_type;

    /**
     * 任务类型
     */
    private String task_type;

    /**
     * 自动驳回类型
     * 1-基础数据 2-智能审核
     */
    private String reject_type;

    /**
     * 驳回原因
     */
    private String reject_reason;

    /**
     * 状态（1-可用，0-禁用）
     */
    private String status;

    /**
     * 顺序，优先级
     */
    private Integer order_no;

    /**
     * 创建人
     */
    private String create_by;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;

    /**
     * 更新人
     */
    private String update_by;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date update_datetime;

}
