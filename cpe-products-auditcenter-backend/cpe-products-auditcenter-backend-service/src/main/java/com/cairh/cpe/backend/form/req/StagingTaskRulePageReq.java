package com.cairh.cpe.backend.form.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 规则分页请求
 *
 * <AUTHOR>
 * @since 2025/3/11 17:50
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StagingTaskRulePageReq extends PageBaseRequest {

    /**
     * 规则名称
     */
    private String rule_name;

    /**
     * 规则类型
     */
    private String rule_type;

    /**
     * 规则开始时间
     */
    private String update_datetime_start;

    /**
     * 规则结束时间
     */
    private String update_datetime_end;

    /**
     * 状态（1-可用，0-禁用）
     */
    private String status;

}
