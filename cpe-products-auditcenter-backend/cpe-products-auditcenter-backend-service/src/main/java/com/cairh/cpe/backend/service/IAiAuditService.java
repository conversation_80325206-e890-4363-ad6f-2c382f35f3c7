package com.cairh.cpe.backend.service;

import com.cairh.cpe.api.aiaudit.resp.MaterialInfo;
import com.cairh.cpe.backend.form.req.AuditQueryRecordReq;
import com.cairh.cpe.backend.form.req.HisAuditForm;
import com.cairh.cpe.service.aiaudit.response.QueryAuditBusinRecordResp;

public interface IAiAuditService {

    QueryAuditBusinRecordResp queryAuditBusinRecordV1(AuditQueryRecordReq auditQueryRecordReq, String notes);


    /**
     * 获取历史快照信息
     *
     * @param hisAuditForm
     * @return
     */
    MaterialInfo getHisAuditInfo(HisAuditForm hisAuditForm);

}
