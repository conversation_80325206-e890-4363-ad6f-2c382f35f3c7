package com.cairh.cpe.backend.task.mq;

import com.cairh.cpe.backend.task.mq.DispatchNoticeMqOperator.DispatchNoticeMqMsg;
import com.cairh.cpe.mq.operator.AbstractP2PMQOperator;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Date;

/**
 * 新派单任务通知
 *
 * <AUTHOR>
 * @since 2025/6/16 16:11
 */
@Component
public class DispatchNoticeMqOperator extends AbstractP2PMQOperator<DispatchNoticeMqMsg> {
    @Getter
    @Value("${cpe.topic.dispatchNoticeBroadcastMqOperator:G_P_USER_JZXT2MSG}")
    private String destinationName;

    @Data
    @AllArgsConstructor
    public static class DispatchNoticeMqMsg implements Serializable {
        // 数据处理状态
        private NoticeMqTypeEnum noticeType;
        // 通知数据
        private DispatchMqMsg dispatchMsg;

        /**
         * 派单通知类型
         *
         * <AUTHOR>
         * @since 2025/6/16 16:11
         */
        public enum NoticeMqTypeEnum {
            /**
             * 分配
             */
            DISTRIBUTED,
            /**
             * 指派
             */
            APPOINT,
            /**
             * 转派
             */
            TRANSFER,
            /**
             * 处理
             */
            HANDLE,
            /**
             * 取消
             */
            CANCEL,
            /**
             * 释放
             */
            RELEASE,
            /**
             * 回收
             */
            RECOVERY
        }

        /**
         * 派单消息
         *
         * <AUTHOR>
         * @since 2025/6/16 16:11
         */
        @Getter
        @Setter
        public static class DispatchMqMsg implements Serializable {
            /**
             * 任务id
             */
            private String task_id;
            /**
             * 任务类型
             */
            private String task_type;
            /**
             * 操作员
             */
            private String operator_no;
            /**
             * 业务方流水号
             */
            private String busi_serial_no;
            /**
             * 当前处理操作员
             */
            private String current_operator_no;
            /**
             * 白名单客户标志
             */
            private String white_listed_flag;
            /**
             * 绿通时间
             */
            private Date white_datetime;
        }
    }
}
