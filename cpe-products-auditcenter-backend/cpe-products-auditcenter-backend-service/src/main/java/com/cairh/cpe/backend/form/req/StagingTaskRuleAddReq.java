package com.cairh.cpe.backend.form.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 规则新增请求
 *
 * <AUTHOR>
 * @since 2025/3/12 09:44
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StagingTaskRuleAddReq {

    /**
     * 规则名称
     */
    @NotBlank(message = "规则名称不能为空")
    private String rule_name;

    /**
     * 规则类型（1-暂存任务 2-自动驳回）
     */
    @NotBlank(message = "规则类型不能为空")
    private String rule_type;

    /**
     * 规则开始时间
     */
    private String rule_datetime_start;

    /**
     * 规则结束时间
     */
    private String rule_datetime_end;

    /**
     * 业务类型
     */
    private String busin_type;

    /**
     * 任务类型
     */
    private String task_type;

    /**
     * 证件类型
     */
    private String id_kind;

    /**
     * 自动驳回类型
     * 1-基础数据 2-智能审核
     */
    private String reject_type;

    /**
     * 驳回原因
     */
    private String reject_reason;

    /**
     * 顺序，优先级
     */
    @NotBlank(message = "优先级不能为空")
    private Integer order_no;

    /**
     * 可选规则
     */
    private String expression;

    /**
     * 状态（1-可用，0-禁用）
     */
    @NotBlank(message = "状态不能为空")
    private String status;

    /**
     * 描述
     */
    private String description;

    /**
     * 关联智能审核项
     */
    private String aiaudit_items;

    /**
     * 关联智能审核驳回原因
     */
    private String aiaudit_reject_reason;

}
