package com.cairh.cpe.backend.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.api.aiaudit.resp.MaterialInfo;
import com.cairh.cpe.backend.converter.ConvertUtil;
import com.cairh.cpe.backend.form.req.*;
import com.cairh.cpe.backend.form.resp.*;
import com.cairh.cpe.backend.service.IAuditDetailHisInfoService;
import com.cairh.cpe.cache.component.ComponentWorkTimeService;
import com.cairh.cpe.cache.service.CacheBackendUser;
import com.cairh.cpe.cache.service.CacheBranch;
import com.cairh.cpe.cache.service.CacheDict;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.constant.flow.FlowRecordEnum;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.dto.ProfessionReasonConfig;
import com.cairh.cpe.common.entity.*;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.clob.IDCardInfo;
import com.cairh.cpe.common.entity.clob.UserBaseInfo;
import com.cairh.cpe.common.entity.request.BusinFlowRecordForm;
import com.cairh.cpe.common.entity.response.*;
import com.cairh.cpe.common.entity.response.BusinFlowRecordResp;
import com.cairh.cpe.common.entity.support.BackendUser;
import com.cairh.cpe.common.mapper.*;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.common.util.*;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.esb.component.video.IEsbComponentVideoDubboService;
import com.cairh.cpe.esb.component.video.dto.req.QueryVideowordsReq;
import com.cairh.cpe.esb.component.video.dto.resp.QueryVideowordsRes;
import com.cairh.cpe.esb.component.video.dto.resp.support.VideowordsConfig;
import com.cairh.cpe.esb.component.video.dto.resp.support.VideowordsModel;
import com.cairh.cpe.service.aiaudit.request.AuditBusinRecordQueryReq;
import com.cairh.cpe.service.aiaudit.request.SpeechSourceReq;
import com.cairh.cpe.service.aiaudit.response.AuditBusinRecordQueryResp;
import com.cairh.cpe.service.aiaudit.response.QueryAuditBusinRecordResp;
import com.cairh.cpe.service.aiaudit.service.IAiAuditService;
import com.cairh.cpe.service.idverify.IComponentIdVerifyService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description：归档数据查询服务
 * Author： slx
 * Date： 2024/10/18 14:15
 */
@Slf4j
@Service
public class AuditDetailHisInfoServiceImpl implements IAuditDetailHisInfoService {

    public final static String USER_GENDER_MAN = "先生";
    public final static String USER_GENDER_WOMAN = "女士";
    private static final String CLIENT_NAME = "client_name";
    private static final String USERNAME = "userName";
    private static final String USER_NAME = "user_name";
    private static final String GENDER_NAME = "gender_name";
    private static final String USER_GENDER = "user_gender";
    private static final String CLIENT_GENDER = "client_gender";
    private static final String BIZ_NAME = "biz_name";
    private static final String BIZ_NAME_VALUE = "集中审核";

    @Autowired
    private IComponentIdVerifyService componentIdVerifyService;
    @Autowired
    private HisBusinFlowRequestMapper hisBusinFlowRequestMapper;
    @Autowired
    private HisBusinFlowTaskMapper hisBusinFlowTaskMapper;
    @Autowired
    private HisBusinFlowRecordMapper hisBusinFlowRecordMapper;
    @Autowired
    private HisUserQueryExtInfoMapper hisUserQueryExtInfoMapper;
    @Resource
    private LabelMapper labelMapper;
    @Autowired
    private CacheDict cacheDict;
    @Autowired
    private CacheBranch cacheBranch;
    @Autowired
    private CacheBackendUser cacheBackendUser;
    @DubboReference
    private IEsbComponentVideoDubboService componentVideoDubboService;
    @Autowired
    private IAiAuditService aiAuditService;
    @Autowired
    private IRequestService requestService;
    @Autowired
    private IBusinFlowTaskService businFlowTaskService;
    @Autowired
    private ComponentWorkTimeService componentWorkTimeService;

    @Value("${cpe.ac.max-export:50000}")
    private Integer max_export;


    @Override
    public Page<HistoryTaskInfoResp> requestPage(List<String> branchNos, HistoryDataRequest request) {
        request.setIs_snapshot("0");
        Page<His_UserQueryExtInfo> page = new Page<>(request.getCur_page(), request.getPage_size());
        LambdaQueryWrapper<His_UserQueryExtInfo> queryWrapper = queryEncapsulation(branchNos, request);
        Page<His_UserQueryExtInfo> extInfoPage = hisUserQueryExtInfoMapper.selectPage(page, queryWrapper);

        Page<HistoryTaskInfoResp> resultPage = getCommonPage(extInfoPage);
        if (CollectionUtils.isEmpty(extInfoPage.getRecords())) {
            resultPage.setRecords(Lists.newArrayList());
            return resultPage;
        }

        LambdaQueryWrapper<His_BusinFlowTask> taskWrapper = new LambdaQueryWrapper<>();
        taskWrapper.select(His_BusinFlowTask::getRequest_no, His_BusinFlowTask::getSerial_id, His_BusinFlowTask::getCreate_datetime, His_BusinFlowTask::getTask_status, His_BusinFlowTask::getTask_type);
        taskWrapper.in(His_BusinFlowTask::getRequest_no, extInfoPage.getRecords().stream().map(His_UserQueryExtInfo::getRequest_no).collect(Collectors.toList()));
        List<His_BusinFlowTask> taskList = hisBusinFlowTaskMapper.selectList(taskWrapper);
        //获取最新一条数据
        Map<String, His_BusinFlowTask> taskMap = taskList.stream()
                .collect(Collectors.toMap(
                        His_BusinFlowTask::getRequest_no,
                        task -> task,
                        (existing, replacement) -> {
                            return existing.getCreate_datetime().before(replacement.getCreate_datetime()) ? replacement : existing;
                        }
                ));
        Map<String, String> taskStatusMap = cacheDict.getDictListByDictCode(DicConstant.DICT_TASK_STATUS).stream().collect(Collectors.toMap(DictInfo::getSub_code,
                DictInfo::getSub_name));
        Map<String, BranchInfo> branchInfoMap = cacheBranch.getAllBranchList().stream().collect(Collectors.toMap(BranchInfo::getBranch_no, x -> x));
        List<HistoryTaskInfoResp> infoResps = extInfoPage.getRecords().stream()
                .map(item -> ConvertUtil.convertToApplyVO(item, branchInfoMap, taskMap, taskStatusMap, Boolean.TRUE))
                .collect(Collectors.toList());
        resultPage.setRecords(infoResps);
        return resultPage;
    }

    @Override
    public List<HistoryTaskInfoResp> export(List<String> branchNos, HistoryDataRequest request, boolean applyList) {
        LambdaQueryWrapper<His_UserQueryExtInfo> wrapper = queryEncapsulation(branchNos, request);
        long total = hisUserQueryExtInfoMapper.selectCount(wrapper);
        if (total > max_export) {
            throw new BizException("-9997", ErrorEnum.TOO_MUCH_DATA_MESSAGE.getDesc());
        }
        if (total == 0L) {
            return Lists.newArrayList();
        }
        // 遍历
        Map<String, BranchInfo> branchInfoMap = cacheBranch.getAllBranchList().stream().collect(Collectors.toMap(BranchInfo::getBranch_no, x -> x));
        Map<String, String> taskStatusMap = cacheDict.getDictListByDictCode(DicConstant.DICT_TASK_STATUS).stream().collect(Collectors.toMap(DictInfo::getSub_code,
                DictInfo::getSub_name));
        Page<His_UserQueryExtInfo> page = new Page<>(1, 2000, Boolean.FALSE);
        List<HistoryTaskInfoResp> responseList = Lists.newArrayList();
        String serial_id = " ";
        while (true) {
            wrapper.gt(His_UserQueryExtInfo::getRequest_no, serial_id);
            List<His_UserQueryExtInfo> userExtList = hisUserQueryExtInfoMapper.selectPage(page, wrapper).getRecords();
            if (CollectionUtils.isEmpty(userExtList)) {
                break;
            }
            serial_id = userExtList.get(userExtList.size() - 1).getRequest_no();
            responseList.addAll(userExtList.stream()
                    .map(item -> ConvertUtil.convertToApplyVO(item, branchInfoMap, Maps.newHashMap(), taskStatusMap, applyList))
                    .collect(Collectors.toList()));
        }
        return responseList;
    }


    @Override
    public Page<HistoryTaskInfoResp> taskPage(List<String> branchNos, HistoryDataRequest request) {
        request.setIs_snapshot("1");
        Page<His_UserQueryExtInfo> page = new Page<>(request.getCur_page(), request.getPage_size());
        LambdaQueryWrapper<His_UserQueryExtInfo> queryWrapper = queryEncapsulation(branchNos, request);
        Page<His_UserQueryExtInfo> extInfoPage = hisUserQueryExtInfoMapper.selectPage(page, queryWrapper);
        Page<HistoryTaskInfoResp> resultPage = getCommonPage(extInfoPage);
        if (extInfoPage.getRecords().isEmpty()) {
            resultPage.setRecords(Lists.newArrayList());
            return resultPage;
        }
        LambdaQueryWrapper<His_BusinFlowTask> taskWrapper = new LambdaQueryWrapper<>();
        taskWrapper.select(His_BusinFlowTask::getRequest_no, His_BusinFlowTask::getSerial_id, His_BusinFlowTask::getTask_status, His_BusinFlowTask::getTask_type);
        taskWrapper.in(His_BusinFlowTask::getSerial_id, extInfoPage.getRecords().stream().map(His_UserQueryExtInfo::getRequest_no).collect(Collectors.toList()));
        List<His_BusinFlowTask> taskList = hisBusinFlowTaskMapper.selectList(taskWrapper);
        //获取最新一条数据
        Map<String, His_BusinFlowTask> taskMap = taskList.stream()
                .collect(Collectors.toMap(His_BusinFlowTask::getSerial_id, Function.identity()));
        Map<String, BranchInfo> branchInfoMap = cacheBranch.getAllBranchList().stream().collect(Collectors.toMap(BranchInfo::getBranch_no, x -> x));
        Map<String, String> taskStatusMap = cacheDict.getDictListByDictCode(DicConstant.DICT_TASK_STATUS).stream().collect(Collectors.toMap(DictInfo::getSub_code,
                DictInfo::getSub_name));
        List<HistoryTaskInfoResp> infoResps = extInfoPage.getRecords().stream()
                .map(item -> ConvertUtil.convertToApplyVO(item, branchInfoMap, taskMap, taskStatusMap, Boolean.FALSE))
                .collect(Collectors.toList());
        resultPage.setRecords(infoResps);
        return resultPage;
    }


    @Override
    public Result<AuditDetailUserBaseInfo> getUserSource(BaseInfoForm baseInfoForm) {
        String request_no = baseInfoForm.getRequest_no();
        String flowtask_id = baseInfoForm.getFlowtask_id();
        ClobContentInfo clobContentInfo = requestService.getHisAllDataByRequestNo(request_no);
        His_BusinFlowRequest byId = hisBusinFlowRequestMapper.selectById(request_no);
        AuditDetailUserBaseInfo auditDetailUserBaseInfo = new AuditDetailUserBaseInfo();
        auditDetailUserBaseInfo.setChannel_name(clobContentInfo.getChannel_name());
        auditDetailUserBaseInfo.setChannel_code(clobContentInfo.getChannel_code());
        auditDetailUserBaseInfo.setOpen_channel(clobContentInfo.getOpen_channel());
        auditDetailUserBaseInfo.setBusin_type(byId.getBusin_type());
        auditDetailUserBaseInfo.setBusin_type_code(byId.getBusin_type());
        auditDetailUserBaseInfo.setActivity_name(clobContentInfo.getActivity_name());
        auditDetailUserBaseInfo.setMarketing_team(clobContentInfo.getMarketing_team());
        auditDetailUserBaseInfo.setMobile_tel(clobContentInfo.getMobile_tel());

        if (StrUtil.isNotBlank(clobContentInfo.getMobile_location())) {
            auditDetailUserBaseInfo.setMobile_location(clobContentInfo.getMobile_location());
        } else {
            MobileLocationQryResp mobileLocationQryResp = componentIdVerifyService.baseDataQryMobileLocation(clobContentInfo.getMobile_tel());
            if (mobileLocationQryResp != null) {
                auditDetailUserBaseInfo.setMobile_location(mobileLocationQryResp.getProvince_name() + mobileLocationQryResp.getCity_name() + mobileLocationQryResp.getOperator());
            }
        }
        auditDetailUserBaseInfo.setAge(clobContentInfo.getUser_base_info().getAge());
        His_BusinFlowTask businFlowTask = hisBusinFlowTaskMapper.selectById(flowtask_id);
        if (businFlowTask == null) {
            return Result.fail(ErrorEnum.AUDIT_TASK_NOT_EXIST.getDesc());
        }
        auditDetailUserBaseInfo.setWhite_flag(businFlowTask.getWhite_flag());
        auditDetailUserBaseInfo.setTask_id(businFlowTask.getTask_id());
        auditDetailUserBaseInfo.setTask_type(businFlowTask.getTask_type());
        LambdaQueryWrapper<Label> labelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        labelLambdaQueryWrapper.eq(Label::getLabel_type, LabelTypeConst.LABEL_TYPE_VIP);
        labelLambdaQueryWrapper.select(Label::getSerial_id);
        auditDetailUserBaseInfo.setBroker_name(clobContentInfo.getBroker_name());
        auditDetailUserBaseInfo.setVideo_type(clobContentInfo.getVideo_type());
        auditDetailUserBaseInfo.setId_kind(clobContentInfo.getId_kind());
        auditDetailUserBaseInfo.setVideo_speech_id(clobContentInfo.getVideo_speech_id());
        return Result.success(auditDetailUserBaseInfo);
    }

    @Override
    public RecentRejectReasonResp getLastRejectReason(BaseInfoForm baseInfoForm) {
        RecentRejectReasonResp recentRejectReasonResp = new RecentRejectReasonResp();
        String request_no = baseInfoForm.getRequest_no();
        String flowtask_id = baseInfoForm.getFlowtask_id();
        His_BusinFlowTask businFlowTask = hisBusinFlowTaskMapper.selectById(flowtask_id);
        recentRejectReasonResp.setBranch_repeated(businFlowTask.getBranch_repeated());
        recentRejectReasonResp.setAddress_repeated(businFlowTask.getAddress_repeated());
        //task_id单笔流程id
        String task_id = businFlowTask.getTask_id();
        if (StringUtils.isNotBlank(task_id)) {
            QueryWrapper<His_BusinFlowTask> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("task_id", task_id);
            List<His_BusinFlowTask> businFlowTaskList = hisBusinFlowTaskMapper.selectList(queryWrapper);
            if (CollectionUtils.isNotEmpty(businFlowTaskList)) {
                List<His_BusinFlowTask> auditTasks = businFlowTaskList.stream().filter(item -> FlowNodeConst.AUDIT.equals(item.getTask_type())).sorted(Comparator.comparing(His_BusinFlowTask::getCreate_datetime).reversed()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(auditTasks)) {
                    His_BusinFlowTask auditTask = auditTasks.get(0);
                    recentRejectReasonResp.setAudit_operator_no(auditTask.getOperator_no());
                    recentRejectReasonResp.setAudit_operator_name(auditTask.getOperator_name());
                }
                List<His_BusinFlowTask> reviewTasks = businFlowTaskList.stream().filter(item -> FlowNodeConst.REVIEW.equals(item.getTask_type())).sorted(Comparator.comparing(His_BusinFlowTask::getCreate_datetime).reversed()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(reviewTasks)) {
                    His_BusinFlowTask reviewTask = reviewTasks.get(0);
                    recentRejectReasonResp.setReview_operator_no(reviewTask.getOperator_no());
                    recentRejectReasonResp.setReview_operator_name(reviewTask.getOperator_name());
                }
                List<His_BusinFlowTask> doubleTasks = businFlowTaskList.stream().filter(item -> FlowNodeConst.SECONDARY_REVIEW.equals(item.getTask_type())).sorted(Comparator.comparing(His_BusinFlowTask::getCreate_datetime).reversed()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(doubleTasks)) {
                    His_BusinFlowTask doubleTask = doubleTasks.get(0);
                    recentRejectReasonResp.setDouble_operator_no(doubleTask.getOperator_no());
                    recentRejectReasonResp.setDouble_operator_name(doubleTask.getOperator_name());
                }
            } else {
                getOperatorInfoByUserQueryExtInfo(recentRejectReasonResp, request_no);
            }
        } else {
            getOperatorInfoByUserQueryExtInfo(recentRejectReasonResp, request_no);
        }
        ClobContentInfo clobContentInfo = requestService.getHisAllDataByRequestNo(request_no);
        String task_type = businFlowTask.getTask_type();
        String task_status = businFlowTask.getTask_status();
        recentRejectReasonResp.setTask_type(task_type);
        String task_type_status = task_type + "-" + task_status;
        recentRejectReasonResp.setTask_type_status(task_type_status);
        String subName = cacheDict.getDictDesc(WskhConstant.DICT_TASK_STATUS, task_type_status);
        recentRejectReasonResp.setTask_type_status_str(subName);
        recentRejectReasonResp.setApp_id(clobContentInfo.getApp_id());
        //获取流水记录
        LambdaQueryWrapper<His_BusinFlowRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(His_BusinFlowRecord::getRequest_no, request_no)
                .in(His_BusinFlowRecord::getBusiness_flag, FlowRecordEnum.B1003.getValue(), FlowRecordEnum.B1103.getValue(), FlowRecordEnum.B1203.getValue())
                .orderByDesc(His_BusinFlowRecord::getCreate_datetime);
        List<His_BusinFlowRecord> list = hisBusinFlowRecordMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            recentRejectReasonResp.setReject_num(Integer.toString(list.size()));
            recentRejectReasonResp.setLast_reject_reason(list.get(0).getBusiness_remark());
        }
        //增加任务id的出参
        recentRejectReasonResp.setTask_id(businFlowTask.getSerial_id());
        //增加标签出参
        String match_labels = businFlowTask.getMatch_labels();
        if (StringUtils.isNotBlank(match_labels)) {
            String[] split = match_labels.split(",");
            List<String> labelList = new ArrayList<>(Arrays.asList(split));
            QueryWrapper<Label> queryWrapper1 = new QueryWrapper<>();
            queryWrapper1.in("serial_id", labelList);
            List<Label> labels = labelMapper.selectList(queryWrapper1);
            recentRejectReasonResp.setLabels(labels);
        }
        return recentRejectReasonResp;
    }

    @Override
    public List<BusinFlowRecordResult> getHisRejectReason(String request_no) {
        BusinFlowRecordForm businFlowRecordForm = new BusinFlowRecordForm();
        businFlowRecordForm.setRequest_no(request_no);
        List<String> business_flags = new ArrayList<>();
        business_flags.add(FlowRecordEnum.B1003.getValue());
        business_flags.add(FlowRecordEnum.B1103.getValue());
        business_flags.add(FlowRecordEnum.B1203.getValue());
        businFlowRecordForm.setBusiness_flags(business_flags);
        List<BusinFlowRecordResp> businFlowRecordResps = hisBusinFlowRecordMapper.qryUserApplyRecord(businFlowRecordForm);
        His_BusinFlowRequest byId = hisBusinFlowRequestMapper.selectById(request_no);
        return businFlowRecordResps.stream().map((businFlowRecordResp) -> {
            BusinFlowRecordResult businFlowRecordResult = new BusinFlowRecordResult();
            businFlowRecordResp.setRequest_status(byId.getRequest_status());
            BaseBeanUtil.copyProperties(businFlowRecordResp, businFlowRecordResult);
            return businFlowRecordResult;
        }).collect(Collectors.toList());
    }

    @Override
    public VideoInfoResp getVideoInfo(String request_no) {
        ClobContentInfo clobContentInfo = requestService.getHisAllDataByRequestNo(request_no);
        VideoInfoResp videoInfoResp = new VideoInfoResp();
        videoInfoResp.setFile_8A(clobContentInfo.getFile_8A());
        List<SpeechSourceReq> speechSource = getSpeechSource(request_no);
        videoInfoResp.setVideo_script(speechSource);
        videoInfoResp.setVip(isVip(request_no));
        return videoInfoResp;
    }

    @Override
    public String getIdPhotoFront(String request_no) {
        ClobContentInfo clobContentInfo = requestService.getHisAllDataByRequestNo(request_no);
        return clobContentInfo.getFile_6A();
    }

    @Override
    public String getIdPhotoBack(String request_no) {
        ClobContentInfo clobContentInfo = requestService.getHisAllDataByRequestNo(request_no);
        return clobContentInfo.getFile_6B();
    }

    @Override
    public ProfileImageResp getProfileImageAndScore(String request_no) {
        ClobContentInfo clobContentInfo = requestService.getHisAllDataByRequestNo(request_no);
        ProfileImageResp profileImageResp = buildBaseProfileImageResp(clobContentInfo);

        // 计算年龄
        String businType = StringUtils.defaultIfBlank(clobContentInfo.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL);

        if (StringUtils.equals(businType, WskhConstant.BUSIN_TYPE_NORMAL)
                && !StringUtils.equals(IdKindEnum.FOREIGN_PREV_PERMIT.getCode(), clobContentInfo.getId_kind())) {
            // 港澳居民来往内地通行证or台湾居民来往大陆通行证
            if (StringUtils.equalsAny(clobContentInfo.getId_kind(), IdKindEnum.TAIWAN_PASS_CARD.getCode(), IdKindEnum.GA_PASS_CARD.getCode())) {
                profileImageResp.setAge(AgeUtil.ageUtil(StringUtils.isNotBlank(clobContentInfo.getBirthday()) ? clobContentInfo.getBirthday() :
                        IdentifyUtils.getIdCardBirthDay(clobContentInfo.getId_card_info().getId_no())));
            } else {
                profileImageResp.setAge(AgeUtil.ageUtil(IdentifyUtils.getIdCardBirthDay(clobContentInfo.getId_card_info().getId_no())));
            }
        } else if (StringUtils.equals(businType, WskhConstant.CROSS_BUSIN_TYPE) &&
                StringUtils.isNotBlank(clobContentInfo.getBirthday())) {
            profileImageResp.setAge(AgeUtil.ageUtil(clobContentInfo.getBirthday()));
        }

        // 新数据标签
        boolean isHandleNewData = StringUtils.equals(clobContentInfo.getData_sign(), WskhConstant.DATA_SIGN_STATUS);
        // 普通开户100058&港澳台居住证&外国人永居证&港澳台通行证
        boolean isPermit = StringUtils.equals(WskhConstant.BUSIN_TYPE_NORMAL, clobContentInfo.getBusin_type()) &&
                StringUtils.equalsAny(clobContentInfo.getId_kind(),
                        IdKindEnum.GAT_RESIDENCE_PERMIT.getCode(),
                        IdKindEnum.GA_PASS_CARD.getCode(),
                        IdKindEnum.TAIWAN_PASS_CARD.getCode(),
                        IdKindEnum.FOREIGN_PREV_PERMIT.getCode());
        // 跨境理财通业务100100
        boolean isCross = StringUtils.equals(WskhConstant.CROSS_BUSIN_TYPE, clobContentInfo.getBusin_type());

        // 是否需要特殊处理人脸分数
        if (!isHandleNewData && !isPermit && !isCross) {
            return profileImageResp;
        }

        // 设置分数相关信息
        String faceScore = clobContentInfo.getFace_score();
        // 处理分数类型为1的情况
        if (StringUtils.equals(clobContentInfo.getScore_type(), "1")) {
            faceScore = clobContentInfo.getFace_score_82_80();
        }
        // 设置基本信息
        profileImageResp.setScore_type(clobContentInfo.getScore_type());
        profileImageResp.setWork_time(componentWorkTimeService.isWorkTime(WskhConstant.WORK_TIME_ZD_GA) ? "1" : "2");
        profileImageResp.setFace_score(faceScore);
        // 设置结果类型
        profileImageResp.setResult_type(ResultTypeEnum.NOTCERTIFIED.getCode());
        // 检查是否有有效的分数
        if (!(StringUtils.isNotBlank(clobContentInfo.getFace_score()) ||
                (StringUtils.isNotBlank(clobContentInfo.getFace_score_82_80()) &&
                        !StringUtils.equals(String.valueOf(clobContentInfo.getFace_score_82_80()), "0.0")))) {
            return profileImageResp;
        }
        if (isPermit || StringUtils.isNotBlank(clobContentInfo.getFace_score())) {
            setZhengTongScore(clobContentInfo.getFace_score(), profileImageResp);
        } else if (StringUtils.isNotBlank(clobContentInfo.getFace_score_82_80())) {
            setFaceScore(clobContentInfo.getFace_score_82_80(), profileImageResp);
        }
        return profileImageResp;
    }

    @Override
    public IdCardInfoResult getIdInfo(IdCardInfoForm idCardInfoForm) {
        ClobContentInfo clobContentInfo = requestService.getHisAllDataByRequestNo(idCardInfoForm.getRequest_no());
        IDCardInfo id_card_info = clobContentInfo.getId_card_info();
        if (null == id_card_info) {
            return new IdCardInfoResult();
        }
        IdCardInfoResult idCardInfoResult = new IdCardInfoResult();
        BeanUtils.copyProperties(id_card_info, idCardInfoResult);
        String id_kind = clobContentInfo.getId_kind();
        idCardInfoResult.setId_kind_code(id_kind);
        String idCardBirthDay = "";
        if (IdKindEnum.ID_CARD.getCode().equals(id_kind)) {
            idCardBirthDay = IdentifyUtils.getIdCardBirthDay(id_card_info.getId_no());
        } else {
            idCardBirthDay = clobContentInfo.getBirthday();
        }
        idCardInfoResult.setBirthday(idCardBirthDay);
        idCardInfoResult.setUser_gender(clobContentInfo.getUser_gender());
        if (StringUtils.equalsAny(StringUtils.defaultIfBlank(clobContentInfo.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL), WskhConstant.BUSIN_TYPE_NORMAL)) {
            // 外国人永居证不做年龄处理
            if (!StringUtils.equals(IdKindEnum.FOREIGN_PREV_PERMIT.getCode(), id_kind)) {
                Map<String, String> checkEffectDateMap = IdCardUtils.checkEffectDateMap(id_card_info.getId_begindate(), id_card_info.getId_enddate(), idCardBirthDay);
                idCardInfoResult.setIssued_age_expiration_date(checkEffectDateMap.get(Fields.ERROR_INFO));
            }
        }
        idCardInfoResult.setInitial_address_modify_flag(clobContentInfo.getInitial_address_modify_flag());
        return idCardInfoResult;
    }

    @Override
    public MoneyAntiInfo getMoneyAntiInfo(String request_no) {
        ClobContentInfo clobContentInfo = requestService.getHisAllDataByRequestNo(request_no);
        MoneyAntiInfo moneyAntiInfo = new MoneyAntiInfo();
        String client_gender = "";
        if (StringUtils.isNotBlank(clobContentInfo.getBranch_no())) {
            BranchInfo branchInfo = cacheBranch.getBranchByNo(clobContentInfo.getBranch_no());
            if (branchInfo != null) {
                moneyAntiInfo.setBranch_name(branchInfo.getBranch_name());
                moneyAntiInfo.setBranch_province_code(branchInfo.getProvince_code());
            }
        }

        if (clobContentInfo.getId_card_info() != null) {
            IDCardInfo idCardInfo = clobContentInfo.getId_card_info();
            client_gender = idCardInfo.getClient_gender();
            moneyAntiInfo.setClient_gender(client_gender);
            if (StringUtils.equalsAny(StringUtils.defaultIfBlank(clobContentInfo.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL), WskhConstant.BUSIN_TYPE_NORMAL)
                    && !StringUtils.equals(IdKindEnum.FOREIGN_PREV_PERMIT.getCode(), clobContentInfo.getId_kind())) {
                // 港澳居民来往内地通行证or台湾居民来往大陆通行证
                if (StringUtils.equalsAny(clobContentInfo.getId_kind(), IdKindEnum.TAIWAN_PASS_CARD.getCode(), IdKindEnum.GA_PASS_CARD.getCode())) {
                    moneyAntiInfo.setAge(AgeUtil.ageUtil(StringUtils.isNotBlank(clobContentInfo.getBirthday()) ? clobContentInfo.getBirthday() :
                            IdentifyUtils.getIdCardBirthDay(clobContentInfo.getId_card_info().getId_no())));
                } else {
                    moneyAntiInfo.setAge(AgeUtil.ageUtil(IdentifyUtils.getIdCardBirthDay(idCardInfo.getId_no())));
                }
            } else if (StringUtils.isNotBlank(clobContentInfo.getBirthday())) {
                moneyAntiInfo.setAge(AgeUtil.ageUtil(clobContentInfo.getBirthday()));
            }
        }

        if (clobContentInfo.getUser_base_info() != null) {
            UserBaseInfo userBaseInfo = clobContentInfo.getUser_base_info();
            moneyAntiInfo.setAddress(userBaseInfo.getAddress());
            moneyAntiInfo.setInitial_address(userBaseInfo.getInitial_address());//原始地址
            moneyAntiInfo.setProfession_code(userBaseInfo.getProfession_code());
            moneyAntiInfo.setChoose_branch_reason(StringUtils.isNotBlank(userBaseInfo.getChoose_branch_reason()) ? userBaseInfo.getChoose_branch_reason() : "");
            moneyAntiInfo.setChoose_profession_reason(userBaseInfo.getChoose_profession_reason());
            moneyAntiInfo.setWork_unit(userBaseInfo.getWork_unit());
            moneyAntiInfo.setProfession_other(userBaseInfo.getProfession_other());
            moneyAntiInfo.setChoose_profession_reason_content(StringUtils.isNotBlank(clobContentInfo.getChoose_profession_reason_content()) ? clobContentInfo.getChoose_profession_reason_content() : "");
            moneyAntiInfo.setChoose_branch_reason_content(clobContentInfo.getChoose_branch_reason_content());
        }

        if (StringUtils.isBlank(moneyAntiInfo.getChoose_profession_reason())) {
            moneyAntiInfo.setChoose_profession_reason(clobContentInfo.getChoose_profession_reason_content());
        }
        if (StringUtils.isBlank(moneyAntiInfo.getChoose_branch_reason())) {
            moneyAntiInfo.setChoose_branch_reason(clobContentInfo.getChoose_branch_reason_content());
        }
        //设置选择职业理由的内容
        if (StringUtils.equalsAny(StringUtils.defaultIfBlank(clobContentInfo.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL), WskhConstant.BUSIN_TYPE_NORMAL)
                && !StringUtils.equals(IdKindEnum.FOREIGN_PREV_PERMIT.getCode(), clobContentInfo.getId_kind())) {
            List<String> list = new ArrayList<>();
            int age = 0;
            if (clobContentInfo.getId_card_info() != null) {
                IDCardInfo idCardInfo = clobContentInfo.getId_card_info();
                client_gender = idCardInfo.getClient_gender();
                String birthday = StringUtils.isEmpty(idCardInfo.getBirthday())
                        ? IdentifyUtils.getIdCardBirthDay(idCardInfo.getId_no()) : idCardInfo.getBirthday();
                age = AgeUtil.ageUtil(birthday);
            }
            // 获取配置信息
            String ageProfessionReasonCodeConfig = PropertySource.get(PropKeyConstant.WSKH_AGE_PROFESSION_REASON_CODE_CONFIG, "");
            if (StringUtils.isNotBlank(ageProfessionReasonCodeConfig)) {
                List<ProfessionReasonConfig> configList = PropertiesUtils.parseConfig(ageProfessionReasonCodeConfig);
                list = PropertiesUtils.evaluateConditions(age, moneyAntiInfo.getProfession_code(), client_gender, configList);
            }
            moneyAntiInfo.setChoose_profession_reason_list(list);
        }

        return moneyAntiInfo;
    }

    @Override
    public MoneyAntiInfo getProfessionReason(GetProfessionReasonForm getProfessionReasonForm) {
        ClobContentInfo clobContentInfo = requestService.getHisAllDataByRequestNo(getProfessionReasonForm.getRequest_no());
        List<String> list = new ArrayList<>();
        if (StringUtils.equalsAny(StringUtils.defaultIfBlank(clobContentInfo.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL), WskhConstant.BUSIN_TYPE_NORMAL)) {
            String client_gender = "";
            int age = 0;
            if (clobContentInfo.getId_card_info() != null) {
                IDCardInfo idCardInfo = clobContentInfo.getId_card_info();
                client_gender = idCardInfo.getClient_gender();
                String birthday = StringUtils.isEmpty(idCardInfo.getBirthday())
                        ? IdentifyUtils.getIdCardBirthDay(idCardInfo.getId_no()) : idCardInfo.getBirthday();
                age = AgeUtil.ageUtil(birthday);
            }

            // 获取配置信息
            String ageProfessionReasonCodeConfig = PropertySource.get(PropKeyConstant.WSKH_AGE_PROFESSION_REASON_CODE_CONFIG, "");
            if (StringUtils.isNotBlank(ageProfessionReasonCodeConfig)) {
                List<ProfessionReasonConfig> configList = PropertiesUtils.parseConfig(ageProfessionReasonCodeConfig);
                list = PropertiesUtils.evaluateConditions(age, getProfessionReasonForm.getProfession_code(), client_gender, configList);
            }
        }
        MoneyAntiInfo moneyAntiInfo = new MoneyAntiInfo();
        moneyAntiInfo.setChoose_profession_reason_list(list);
        return moneyAntiInfo;
    }

    @Override
    public List<AuditOperateRecord> getOperatorRecord(String request_no) {
        List<AuditOperateRecord> resList = new ArrayList<>();
        List<His_BusinFlowRecord> list = hisBusinFlowRecordMapper.selectList(new LambdaQueryWrapper<>(His_BusinFlowRecord.class)
                .eq(His_BusinFlowRecord::getRequest_no, request_no)
                //.eq(BusinFlowRecord::getRecord_type, FlowStatusConst.AUDIT_RECORD_TYPE)
                .ne(His_BusinFlowRecord::getBusiness_flag, "0")
                .orderByDesc(His_BusinFlowRecord::getSerial_id));

        if (list != null && list.size() > 0) {
            list = list.stream().filter(li -> !StringUtils.equals(li.getBusiness_flag(), WskhConstant.BUSINESS_FLAG_22385)
                    && !StringUtils.equals(li.getBusiness_flag(), WskhConstant.BUSINESS_FLAG_22393)
                    && !StringUtils.equals(li.getBusiness_flag(), WskhConstant.BUSINESS_FLAG_22383)
                    && !StringUtils.equals(li.getBusiness_flag(), WskhConstant.BUSINESS_FLAG_22384)).collect(Collectors.toList());
            list.stream().forEach(record -> {
                AuditOperateRecord auditOperateRecord = new AuditOperateRecord();
                auditOperateRecord.setCreate_datetime(KHDateUtil.formatDate(record.getCreate_datetime(), KHDateUtil.DATE_TIME_FORMAT));
                auditOperateRecord.setBusiness_flag(record.getBusiness_flag());
                auditOperateRecord.setBusiness_remark(record.getBusiness_remark());
                auditOperateRecord.setOperator_no(record.getOperator_no());
                if (StringUtils.isNotBlank(record.getOperator_no())) {
                    if (StringUtils.equals(record.getOperator_no(), WskhConstant.SUPER_USER)) {
                        auditOperateRecord.setOperator_no(WskhConstant.SUPER_USER);
                        auditOperateRecord.setOperator_name(WskhConstant.SUPER_USER);
                        auditOperateRecord.setBranch_name(WskhConstant.SUPER_BRANCH);
                    }
                    BackendUser backendUser = cacheBackendUser.getBackendUserByStaffNo(record.getOperator_no());
                    if (backendUser != null) {
                        auditOperateRecord.setOperator_name(backendUser.getUser_name());
                        auditOperateRecord.setBranch_no(backendUser.getBranch_no());
                        BranchInfo branchInfo = cacheBranch.getBranchByNo(backendUser.getBranch_no());
                        if (branchInfo != null) {
                            auditOperateRecord.setBranch_name(branchInfo.getBranch_name());
                        }
                    }
                } else {
                    auditOperateRecord.setOperator_no(WskhConstant.SUPER_USER);
                    auditOperateRecord.setOperator_name(WskhConstant.SUPER_USER);
                    auditOperateRecord.setBranch_name(WskhConstant.SUPER_BRANCH);
                }
                resList.add(auditOperateRecord);
            });
        }

        return resList;
    }

    @Override
    public DishonestResp getDishonestRecord(String request_no) {
        ClobContentInfo clobContentInfo = requestService.getHisAllDataByRequestNo(request_no);
        DishonestResp dishonestResp = new DishonestResp();
        dishonestResp.setDishonest_id(clobContentInfo.getDishonest_id());
        dishonestResp.setDishonest_record(clobContentInfo.getDishonest_record());
        dishonestResp.setDishonest_content(clobContentInfo.getDishonest_content());
        dishonestResp.setDishonest_record_remark(clobContentInfo.getDishonest_record_remark());
        dishonestResp.setRpc_file_id(clobContentInfo.getRpc_file_id());
        dishonestResp.setRpc_remark(clobContentInfo.getRpc_remark());
        dishonestResp.setRpc_option(clobContentInfo.getRpc_option());
        if (StringUtils.isNotBlank(clobContentInfo.getDishonest_content())) {
            List<DictInfo> dictListByDictCode = cacheDict.getDictListByDictCode(DicConstant.DISHONEST_RECORD);
            List<String> collect = dictListByDictCode.stream().map(DictInfo::getSub_code).collect(Collectors.toList());
            dishonestResp.setDishonest_record_list(collect);
        }
        List<DictInfo> dictListByDictCode = cacheDict.getDictListByDictCode(DicConstant.INITIAL_INVESTMENT_AMOUNT);
        String initial_investment_amount = clobContentInfo.getInitial_investment_amount();
        if (StringUtils.isNotEmpty(initial_investment_amount)) {
            for (DictInfo dictInfo : dictListByDictCode) {
                if (dictInfo.getSub_code().equals(initial_investment_amount)) {
                    initial_investment_amount = dictInfo.getSub_name();
                    break;
                }
            }
        }
        dishonestResp.setInitial_investment_amount(initial_investment_amount);
        return dishonestResp;
    }

    @Override
    public QueryAuditBusinRecordResp queryAuditBusinRecordV1(AuditQueryRecordReq auditQueryRecordReq) {
        List<String> item_identity = auditQueryRecordReq.getItem_identity();
        List<AuditBusinRecordQueryResp> auditBusinRecordQueryResps = queryAuditBusinRecord(auditQueryRecordReq.getRequest_no(), item_identity);
        ClobContentInfo clobContentInfo = requestService.getHisAllDataByRequestNo(auditQueryRecordReq.getRequest_no());
        QueryAuditBusinRecordResp queryAuditBusinRecordResp = new QueryAuditBusinRecordResp();
        // 港澳台居民居住证 && 港澳台通行证
        boolean isPermit = StringUtils.equals(WskhConstant.BUSIN_TYPE_NORMAL, clobContentInfo.getBusin_type()) &&
                StringUtils.equalsAny(clobContentInfo.getId_kind(), IdKindEnum.GAT_RESIDENCE_PERMIT.getCode(), IdKindEnum.GA_PASS_CARD.getCode(),
                        IdKindEnum.TAIWAN_PASS_CARD.getCode());
        boolean bidirectional_busin_type = StringUtils.equalsAny(clobContentInfo.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL) &&
                StringUtils.equals(clobContentInfo.getVideo_type(), WskhConstant.VIDEO_TYPE_2);
        // 双向视频不验证分数
        if (!bidirectional_busin_type
                && !ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, clobContentInfo.getBusin_type()) && !isPermit) {
            if (StringUtils.isNotBlank(clobContentInfo.getFile_82())) {
                if (StringUtils.isBlank(clobContentInfo.getFace_score_82_80())
                        && (StringUtils.isBlank(clobContentInfo.getKh_face_score_82_80()) || Double.parseDouble(clobContentInfo.getKh_face_score_82_80()) < 0)) {
                    queryAuditBusinRecordResp.setWeak_fail_message("本次人脸识别失败，请重试或人工审核！");
                }
            }
        }
        String scores_zt = PropertySource.get(PropKeyConstant.WSKH_ZT_SCORE_CONFIDENCE_RANGE, "40,60");
        int minScore_zt = Integer.parseInt(scores_zt.split(",")[0]);
        int face_score = StringUtils.isNotBlank(clobContentInfo.getFace_score()) ? Double.valueOf(clobContentInfo.getFace_score()).intValue() : 0;
        if (CollectionUtils.isNotEmpty(auditBusinRecordQueryResps)) {
            if (StringUtils.isBlank(clobContentInfo.getFile_82()) && face_score >= minScore_zt) {
                auditBusinRecordQueryResps = auditBusinRecordQueryResps.stream()
                        .filter(item -> {
                            //face_contrast_card_police -客户头部正面照与公民身份信息核查照一致 face_contrast_face_police-身份证正面头像与公民身份信息核查照一致
                            return !StringUtils.equalsAny(item.getDrl_rule_name(), "face_contrast_card_police", "face_contrast_face_police");
                        }).collect(Collectors.toList());
            }
        }
        queryAuditBusinRecordResp.setAuditBusinRecordQueryRespList(auditBusinRecordQueryResps);
        return queryAuditBusinRecordResp;
    }

    @Override
    public BusinFlowTaskPositionResp queryBusinFlowTaskPosition(String flowtask_id) {
        Assert.notBlank(flowtask_id, "flowtask_id不能为空");

        BusinFlowTask businFlowTask = businFlowTaskService.getById(flowtask_id);
        if (businFlowTask != null) {
            return createBusinFlowTaskPositionResp(businFlowTask.getRequest_no(), businFlowTask.getSerial_id(), "1");
        }

        His_BusinFlowTask oldBusinFlowTask = hisBusinFlowTaskMapper.selectById(flowtask_id);
        if (oldBusinFlowTask != null) {
            return createBusinFlowTaskPositionResp(oldBusinFlowTask.getRequest_no(), oldBusinFlowTask.getSerial_id(), "2");
        }
        log.warn("未找到flowtask_id相关任务数据: {}", flowtask_id);
        return null;
    }

    @Override
    public MaterialInfo getUserMaterialInfo(String request_no) {
        Assert.notBlank(request_no, "业务编号不能为空");

        ClobContentInfo clobContentInfo = requestService.getHisAllDataByRequestNo(request_no);
        MaterialInfo materialInfo = new MaterialInfo();

        materialInfo.setId_kind(clobContentInfo.getId_kind());
        materialInfo.setId_no(clobContentInfo.getId_no());
        materialInfo.setClient_name(clobContentInfo.getClient_name());

        // 网厅业务办理
        if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, clobContentInfo.getBusin_type())) {
            if (StringUtils.equals(clobContentInfo.getClient_category(), ClientCategoryEnum.CLIENT_PERSON.getCode())) {
                materialInfo.setFile_6A(clobContentInfo.getPhoto_front());
                materialInfo.setFile_6B(clobContentInfo.getPhoto_back());
            } else {
                materialInfo.setFile_6A(clobContentInfo.getAgent_photo_front());
                materialInfo.setFile_6B(clobContentInfo.getAgent_photo_back());
            }
        } else {
            materialInfo.setFile_6A(clobContentInfo.getFile_6A());
            materialInfo.setFile_6B(clobContentInfo.getFile_6B());
        }
        materialInfo.setFile_80(clobContentInfo.getFile_80());
        materialInfo.setFile_82(clobContentInfo.getFile_82());
        materialInfo.setFile_8A(clobContentInfo.getFile_8A());

        return materialInfo;
    }

    @Override
    public IdPhotoResp getAllIdPhoto(String request_no) {
        IdPhotoResp idPhotoResp = new IdPhotoResp();
        ClobContentInfo clobContentInfo = requestService.getHisAllDataByRequestNo(request_no);
        idPhotoResp.setFile_6A(clobContentInfo.getFile_6A());
        idPhotoResp.setFile_6B(clobContentInfo.getFile_6B());
        idPhotoResp.setFile_7C(clobContentInfo.getFile_7C());
        idPhotoResp.setFile_7D(clobContentInfo.getFile_7D());
        return idPhotoResp;
    }

    private BusinFlowTaskPositionResp createBusinFlowTaskPositionResp(String request_no, String task_id, String positionType) {
        BusinFlowTaskPositionResp resp = new BusinFlowTaskPositionResp();
        resp.setRequest_no(request_no);
        resp.setTask_id(task_id);
        resp.setPosition_type(positionType);
        return resp;
    }


    private void getOperatorInfoByUserQueryExtInfo(RecentRejectReasonResp recentRejectReasonResp, String request_no) {
        //查询扩展表
        QueryWrapper<His_UserQueryExtInfo> userQueryExtInfoQueryWrapper = new QueryWrapper<>();
        userQueryExtInfoQueryWrapper.eq("request_no", request_no);
        List<His_UserQueryExtInfo> userQueryExtInfos = hisUserQueryExtInfoMapper.selectList(userQueryExtInfoQueryWrapper);
        if (CollectionUtils.isNotEmpty(userQueryExtInfos)) {
            His_UserQueryExtInfo userQueryExtInfo = userQueryExtInfos.get(0);
            recentRejectReasonResp.setAudit_operator_no(userQueryExtInfo.getAudit_operator_no());
            recentRejectReasonResp.setAudit_operator_name(userQueryExtInfo.getAudit_operator_name());
            recentRejectReasonResp.setReview_operator_no(userQueryExtInfo.getReview_operator_no());
            recentRejectReasonResp.setReview_operator_name(userQueryExtInfo.getReview_operator_name());
            recentRejectReasonResp.setDouble_operator_no(userQueryExtInfo.getDouble_operator_no());
            recentRejectReasonResp.setDouble_operator_name(userQueryExtInfo.getDouble_operator_name());
        }
    }

    private List<SpeechSourceReq> getSpeechSource(String request_no) {
        ClobContentInfo clob = requestService.getHisAllDataByRequestNo(request_no);
        Map<String, Object> clobMap = BaseBeanUtil.beanToMap(clob);
        Map<String, String> replaceMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(clobMap)) {
            replaceMap = clobMap.entrySet().stream()
                    .collect(Collectors.toMap(e -> String.valueOf(e.getKey()), e -> String.valueOf(e.getValue())));
        }
        His_BusinFlowRequest request = hisBusinFlowRequestMapper.selectById(request_no);
        List<SpeechSourceReq> list = new ArrayList<>();

        QueryVideowordsReq queryVideowordsReq = new QueryVideowordsReq();
        queryVideowordsReq.setSubsys_no(Integer.valueOf(WskhConstant.SUBSYS_ID));
        queryVideowordsReq.setBusin_type(Integer.valueOf(request.getBusin_type()));
        queryVideowordsReq.setVideo_type(clob.getVideo_type());
        // 网厅业务办理增加规则表达式匹配
        if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, request.getBusin_type())) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(WskhFields.HAND_CLIENT_CATEGORY, clob.getClient_category());
            queryVideowordsReq.setRegular_data(jsonObject.toJSONString());
        }

        // 100058集中见证双向视频&单向视频增加规则表达式匹配
        if (StringUtils.equals(clob.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL) &&
                StringUtils.equalsAny(clob.getVideo_type(), WskhConstant.VIDEO_TYPE_2, WskhConstant.VIDEO_TYPE_1)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(Fields.ID_KIND, clob.getId_kind());
            jsonObject.put(Fields.VIDEO_SPEECH_ID, clob.getVideo_speech_id());
            queryVideowordsReq.setRegular_data(jsonObject.toJSONString());
        }

        QueryVideowordsRes queryVideowordsRes = componentVideoDubboService.compvideoQueryVideowords(queryVideowordsReq);
        if (Objects.nonNull(queryVideowordsRes) && CollectionUtil.isNotEmpty(queryVideowordsRes.getVideomodel_resultlist())) {
            // 获取视频话术模版
            List<VideowordsModel> videowordsModels = queryVideowordsRes.getVideomodel_resultlist();
            if (CollectionUtil.isNotEmpty(videowordsModels)) {
                VideowordsModel videowordsModel = videowordsModels.get(0);
                if (CollectionUtil.isNotEmpty(queryVideowordsRes.getVideoconfig_resultlist())) {
                    // 通过视频话术模版id获取对应的视频话术配置表数据
                    List<VideowordsConfig> videowordsConfigs = queryVideowordsRes.getVideoconfig_resultlist()
                            .stream()
                            .filter(videoConfig -> StrUtil.equals(videoConfig.getVideowordsmodel_id(), videowordsModel.getSerial_id()))
                            .collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(videowordsConfigs)) {
                        CollectionUtil.sort(videowordsConfigs, (o1, o2) -> (int) (o1.getOrder_no() - o2.getOrder_no()));
                        // 遍历获取对应的问答内容
                        for (VideowordsConfig videowordsConfig : videowordsConfigs) {
                            SpeechSourceReq speechSourceReq = new SpeechSourceReq();
                            if (StringUtils.equals(videowordsConfig.getWords_type(), "1")) {
                                String words_content = videowordsConfig.getWords_content();
                                words_content = renderData(words_content, replaceMap);
                                speechSourceReq.setQuestion(words_content);
                                Map<String, String> finalReplaceMap = replaceMap;
                                //黑名单
                                if (StringUtils.isNotBlank(videowordsConfig.getError_answer())) {
                                    List<String> errorAnswerList = Arrays.asList(videowordsConfig.getError_answer().split(","));
                                    List<String> collect = errorAnswerList.stream().map(answer -> renderData(answer, finalReplaceMap)).collect(Collectors.toList());
                                    speechSourceReq.setAnswer_black(collect);
                                }
                                //白名单
                                if (StringUtils.isNotBlank(videowordsConfig.getCorrect_answer())) {
                                    List<String> correctAnswerList = Arrays.asList(videowordsConfig.getCorrect_answer().split(","));
                                    List<String> collect = correctAnswerList.stream().map(answer -> renderData(answer, finalReplaceMap)).collect(Collectors.toList());
                                    speechSourceReq.setAnswer_white(collect);
                                }
                                speechSourceReq.setAnswer("这是答案");
                            } else { //其他类型
                                String words_content = videowordsConfig.getWords_content();
                                words_content = renderData(words_content, replaceMap);
                                speechSourceReq.setQuestion(words_content);
                            }
                            list.add(speechSourceReq);
                        }
                    }
                }
            }
        }
        log.info("request_no={}，话述生成: {} ", request_no, list);
        return list;
    }

    private boolean isVip(String request_no) {
        String vipChannels = PropertySource.get(PropKeyConstant.CHANNEL_VIP_LIST);

        if (StrUtil.isNotBlank(vipChannels)) {
            List<String> vipChannelList = StrUtil.split(vipChannels, StrPool.COMMA);

            His_UserQueryExtInfo userQueryExtInfo = hisUserQueryExtInfoMapper.selectById(request_no);
            return CollectionUtil.contains(vipChannelList, userQueryExtInfo.getOpen_channel());
        }

        log.warn("未指定用户vip渠道");
        return false;
    }

    private String renderData(String context, Map<String, String> params) {
        if (StringUtils.isBlank(context) || CollectionUtil.isEmpty(params)) {
            return context;
        }
        if (context.contains(USERNAME) && !params.containsKey(USERNAME)) {
            params.put(USERNAME, params.get(CLIENT_NAME));
        }
        if (context.contains(USER_NAME) && !params.containsKey(USER_NAME)) {
            params.put(USER_NAME, params.get(CLIENT_NAME));
        }
        if (context.contains(GENDER_NAME) && !params.containsKey(GENDER_NAME) ||
                context.contains(USER_GENDER) && !params.containsKey(USER_GENDER)) {
            String sexGender = "";
            if ("1".equals(params.get(CLIENT_GENDER))) {
                sexGender = USER_GENDER_MAN;
            } else if ("2".equals(params.get(CLIENT_GENDER))) {
                sexGender = USER_GENDER_WOMAN;
            }
            params.put(GENDER_NAME, sexGender);
            params.put(USER_GENDER, sexGender);
        }
        params.put(BIZ_NAME, BIZ_NAME_VALUE);
        if (StringUtils.isNotBlank(context) && CollectionUtil.isNotEmpty(params)) {
            context = MessageUtil.getMessage(context, params);
        }
        return context;
    }

    private List<AuditBusinRecordQueryResp> queryAuditBusinRecord(String request_no, List<String> item_identitys) {
        ClobContentInfo clob = requestService.getHisAllDataByRequestNo(request_no);
        AuditBusinRecordQueryReq req = new AuditBusinRecordQueryReq();
        req.setAudit_code(clob.getAi_audit_code());
        if (CollectionUtils.isNotEmpty(item_identitys)) {
            req.setItem_identity(item_identitys);
        }
        return aiAuditService.queryAuditBusinRecord(req);
    }

    /**
     * Page 转换
     *
     * @param extInfoPage
     * @return
     */
    private Page<HistoryTaskInfoResp> getCommonPage(Page<His_UserQueryExtInfo> extInfoPage) {
        Page<HistoryTaskInfoResp> resultPage = new Page<>();
        resultPage.setTotal(extInfoPage.getTotal());
        resultPage.setCurrent(extInfoPage.getCurrent());
        resultPage.setSize(extInfoPage.getSize());
        resultPage.setPages(extInfoPage.getPages());
        resultPage.setSearchCount(extInfoPage.isSearchCount());
        resultPage.setHitCount(extInfoPage.isHitCount());
        resultPage.setOrders(extInfoPage.getOrders());
        return resultPage;
    }

    /**
     * 参数拼装
     *
     * @param request
     * @return
     */
    private LambdaQueryWrapper<His_UserQueryExtInfo> queryEncapsulation(List<String> branchNos, HistoryDataRequest request) {
        LambdaQueryWrapper<His_UserQueryExtInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(request.getAudit_operator_no()), His_UserQueryExtInfo::getAudit_operator_no, request.getAudit_operator_no());
        queryWrapper.eq(StringUtils.isNotBlank(request.getAudit_operator_name()), His_UserQueryExtInfo::getAudit_operator_name, request.getAudit_operator_name());
        queryWrapper.eq(StringUtils.isNotBlank(request.getReview_operator_no()), His_UserQueryExtInfo::getReview_operator_no, request.getReview_operator_no());
        queryWrapper.eq(StringUtils.isNotBlank(request.getReview_operator_name()), His_UserQueryExtInfo::getReview_operator_name, request.getReview_operator_name());
        queryWrapper.eq(StringUtils.isNotBlank(request.getDouble_operator_no()), His_UserQueryExtInfo::getDouble_operator_no, request.getDouble_operator_no());
        queryWrapper.eq(StringUtils.isNotBlank(request.getDouble_operator_name()), His_UserQueryExtInfo::getDouble_operator_name, request.getDouble_operator_name());
        queryWrapper.eq(StringUtils.isNotBlank(request.getClient_name()), His_UserQueryExtInfo::getClient_name, request.getClient_name());
        queryWrapper.eq(StringUtils.isNotBlank(request.getMobile_tel()), His_UserQueryExtInfo::getMobile_tel, request.getMobile_tel());
        queryWrapper.eq(StringUtils.isNotBlank(request.getId_kind()), His_UserQueryExtInfo::getId_kind, request.getId_kind());
        queryWrapper.eq(StringUtils.isNotBlank(request.getId_no()), His_UserQueryExtInfo::getId_no, request.getId_no());
        queryWrapper.eq(StringUtils.isNotBlank(request.getChannel_name()), His_UserQueryExtInfo::getChannel_name, request.getChannel_name());
        queryWrapper.eq(StringUtils.isNotBlank(request.getActivity_no()), His_UserQueryExtInfo::getActivity_no, request.getActivity_no());
        queryWrapper.eq(StringUtils.isNotBlank(request.getMarketing_team()), His_UserQueryExtInfo::getMarketing_team, request.getMarketing_team());
        queryWrapper.eq(StringUtils.isNotBlank(request.getVideo_type()), His_UserQueryExtInfo::getVideo_type, request.getVideo_type());
        queryWrapper.eq(StringUtils.isNotBlank(request.getBusin_type()), His_UserQueryExtInfo::getBusin_type, request.getBusin_type());
        queryWrapper.ge(StringUtils.isNotBlank(request.getReview_datetime_start()), His_UserQueryExtInfo::getReview_finish_datetime, SqlDateUtil.getParseDateStartDetail(request.getReview_datetime_start()));
        queryWrapper.le(StringUtils.isNotBlank(request.getReview_datetime_end()), His_UserQueryExtInfo::getReview_finish_datetime, SqlDateUtil.getParseDateEndDetail(request.getReview_datetime_end()));
        queryWrapper.ge(StringUtils.isNotBlank(request.getAudit_datetime_start()), His_UserQueryExtInfo::getAudit_finish_datetime, SqlDateUtil.getParseDateStartDetail(request.getAudit_datetime_start()));
        queryWrapper.le(StringUtils.isNotBlank(request.getAudit_datetime_end()), His_UserQueryExtInfo::getAudit_finish_datetime, SqlDateUtil.getParseDateEndDetail(request.getAudit_datetime_end()));
        queryWrapper.ge(StringUtils.isNotBlank(request.getDouble_datetime_start()), His_UserQueryExtInfo::getDouble_finish_datetime, SqlDateUtil.getParseDateStartDetail(request.getDouble_datetime_start()));
        queryWrapper.le(StringUtils.isNotBlank(request.getDouble_datetime_end()), His_UserQueryExtInfo::getDouble_finish_datetime, SqlDateUtil.getParseDateEndDetail(request.getDouble_datetime_end()));
        queryWrapper.le(StringUtils.isNotBlank(request.getRequest_datetime_end()), His_UserQueryExtInfo::getRequest_datetime, SqlDateUtil.getParseDateEndDetail(request.getRequest_datetime_end()));
        queryWrapper.ge(StringUtils.isNotBlank(request.getRequest_datetime_start()), His_UserQueryExtInfo::getRequest_datetime, SqlDateUtil.getParseDateStartDetail(request.getRequest_datetime_start()));
        queryWrapper.eq(StringUtils.isNotBlank(request.getIs_snapshot()), His_UserQueryExtInfo::getIs_snapshot, request.getIs_snapshot());
        if (StringUtils.isEmpty(request.getBranch_no())) {
            queryWrapper.in(His_UserQueryExtInfo::getBranch_no, branchNos);
        } else {
            queryWrapper.in(His_UserQueryExtInfo::getBranch_no, branchNos.stream().filter(item -> request.getBranch_no().contains(item)).collect(Collectors.toList()));
        }
        if (StringUtils.isBlank(request.getRequest_status())) {
            return queryWrapper;
        }
        String[] arrTaskStatus = request.getRequest_status().split("-");
        if (arrTaskStatus.length != 2) {
            return queryWrapper;
        }
        String type = arrTaskStatus[0];
        String status = arrTaskStatus[1];
        queryWrapper.eq(StringUtils.isNotBlank(request.getRequest_status()), His_UserQueryExtInfo::getRequest_status, status);
        if (request.getIs_snapshot().equals("0") && FlowStatusConst.REQUEST_STATUS_AUDIT_PASS.equals(status)) {
            queryWrapper.eq(His_UserQueryExtInfo::getEnd_node, type);
        } else {
            queryWrapper.eq(His_UserQueryExtInfo::getAnode_id, type);
        }
        return queryWrapper;
    }

    /**
     * 构建基础的ProfileImageResp对象
     */
    private ProfileImageResp buildBaseProfileImageResp(ClobContentInfo clobContentInfo) {
        ProfileImageResp resp = new ProfileImageResp();
        resp.setFile_82(clobContentInfo.getFile_82());
        resp.setFile_80(clobContentInfo.getFile_80());
        resp.setFace_score_desc(clobContentInfo.getFace_score_desc());
        resp.setFace_score(clobContentInfo.getFace_score());
        resp.setData_sign(clobContentInfo.getData_sign());
        return resp;
    }

    /**
     * 设置人脸比对分数结果
     */
    private void setFaceScore(String faceScore, ProfileImageResp profileImageResp) {
        if (StringUtils.isBlank(faceScore)) {
            return;
        }

        try {
            int score = Integer.parseInt(faceScore);
            profileImageResp.setResult_type(score <= 1 ? ResultTypeEnum.INCONSISTENCY.getCode() :
                    score >= 100 ? ResultTypeEnum.CONSISTENCY.getCode() :
                            profileImageResp.getResult_type());
        } catch (NumberFormatException e) {
            log.warn("人脸比对分数转换异常, faceScore:{}", faceScore);
            throw new BizException("-1", "人脸比对分数转换异常");
        }
    }

    /**
     * 设置证通分数结果
     */
    private void setZhengTongScore(String faceScore, ProfileImageResp profileImageResp) {
        try {
            String[] scoreRange = PropertySource.get(PropKeyConstant.WSKH_ZT_SCORE_CONFIDENCE_RANGE, "40,60").split(",");
            int score = new BigDecimal(faceScore).intValue();
            int minScore = Integer.parseInt(scoreRange[0]);
            int maxScore = Integer.parseInt(scoreRange[1]);

            profileImageResp.setResult_type(score < minScore ? ResultTypeEnum.INCONSISTENCY.getCode() :
                            score >= maxScore ? ResultTypeEnum.CONSISTENCY.getCode() :
                                    ResultTypeEnum.TOBECONFIRMED.getCode())
                    .setScore_type("2");
        } catch (Exception e) {
            log.warn("证通分数转换异常, faceScore:{}", faceScore);
            throw new BizException("-1", "证通分数转换异常");
        }
    }
}
