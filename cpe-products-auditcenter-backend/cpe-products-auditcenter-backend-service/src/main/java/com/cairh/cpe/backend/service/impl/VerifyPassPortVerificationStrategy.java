package com.cairh.cpe.backend.service.impl;

import com.alibaba.fastjson2.JSON;
import com.cairh.cpe.backend.form.resp.VerificationResult;
import com.cairh.cpe.backend.form.support.VerifyPoliceInfo;
import com.cairh.cpe.backend.service.VerificationStrategy;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.clob.IDCardInfo;
import com.cairh.cpe.common.entity.request.VerifyPassPortRequest;
import com.cairh.cpe.common.entity.response.VerifyPassPortResult;
import com.cairh.cpe.common.entity.response.VerifyPoliceResp;
import com.cairh.cpe.service.idverify.IComponentIdVerifyService;
import com.cairh.cpe.service.third.IElectThirdFileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 外国人永居证 or 港澳台通行证策略
 * T2接口：11804446、11804447
 *
 * <AUTHOR>
 * @since 2025/5/7 10:39
 */
@Slf4j
@Service
public class VerifyPassPortVerificationStrategy implements VerificationStrategy {

    @Resource
    private IComponentIdVerifyService componentIdVerifyService;
    @Resource
    private IElectThirdFileService electThirdFileService;

    @Override
    public VerificationResult verify(ClobContentInfo clob, VerifyPoliceInfo info,
                                     boolean isNewDataSign, boolean workTime) {
        IDCardInfo id_card_info = clob.getId_card_info();

        // 构建请求参数
        VerifyPassPortRequest verifyPassPortRequest = new VerifyPassPortRequest();
        verifyPassPortRequest.setFull_name(id_card_info.getClient_name());
        verifyPassPortRequest.setId_kind(id_card_info.getId_kind());
        verifyPassPortRequest.setId_no(id_card_info.getId_no());
        verifyPassPortRequest.setBranch_no(clob.getBranch_no());
        verifyPassPortRequest.setOp_branch_no(verifyPassPortRequest.getBranch_no());
        verifyPassPortRequest.setArea_code(id_card_info.getNationality());
        verifyPassPortRequest.setBirthday(id_card_info.getBirthday());
        verifyPassPortRequest.setId_begindate(id_card_info.getId_begindate());
        verifyPassPortRequest.setId_enddate(id_card_info.getId_enddate());
        verifyPassPortRequest.setBase64_image(electThirdFileService.getFileThirdId(clob.getFile_80(), id_card_info.getId_no()));
        verifyPassPortRequest.setRealtime_flag("1");
        // 是否中登时间
        if (workTime) {
            verifyPassPortRequest.setGt_cxlx("13");
            verifyPassPortRequest.setGt_hclb("11");
        } else {
            verifyPassPortRequest.setGt_cxlx("14");
        }

        log.info("【VerifyPassPortVerificationStrategy】三要素请求入参：{}", JSON.toJSON(verifyPassPortRequest));
        // 调用三要素校验
        VerifyPassPortResult verifyPassPortResult = componentIdVerifyService.verifyPassPort(verifyPassPortRequest);
        log.info("【VerifyPassPortVerificationStrategy】三要素校验结果：{}", JSON.toJSON(verifyPassPortResult));

        if (Objects.isNull(verifyPassPortResult)) {
            return new VerificationResult(null, true, false);
        }
        VerifyPoliceResp resp = new VerifyPoliceResp();
        resp.setStatus(verifyPassPortResult.getStatus());
        resp.setResult_info(verifyPassPortResult.getResult_info());
        resp.setIdverifyrecord_id(verifyPassPortResult.getIdverifyrecord_id());
        resp.setImage_data(verifyPassPortResult.getImage_data());
        resp.setFilerecord_id(verifyPassPortResult.getFilerecord_id());

        // 根据校验状态设置分数和结果
        boolean isSuccess = StringUtils.equals(verifyPassPortResult.getStatus(), WskhConstant.STATUS_SUCCESS);
        clob.setPermanent_id_info_check_result(isSuccess ? "1" : "0");
        resp.setScore(isSuccess ? WskhConstant.FACE_SCORE_SUCCESS : WskhConstant.FACE_SCORE_FAIL);
        return new VerificationResult(resp, true, false);

    }
}
