package com.cairh.cpe.backend.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.backend.converter.ConvertUtil;
import com.cairh.cpe.backend.form.req.*;
import com.cairh.cpe.backend.form.resp.*;
import com.cairh.cpe.backend.mapper.CenterBusinFlowTaskMapper;
import com.cairh.cpe.backend.mapper.MybusinFlowTaskMapper;
import com.cairh.cpe.backend.service.IWorkBusinFlowTaskService;
import com.cairh.cpe.cache.service.CacheBackendUser;
import com.cairh.cpe.cache.service.CacheBranch;
import com.cairh.cpe.cache.service.CacheDict;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.*;
import com.cairh.cpe.common.entity.response.BranchInfo;
import com.cairh.cpe.common.entity.response.DictInfo;
import com.cairh.cpe.common.entity.support.RecyleTaskMessage;
import com.cairh.cpe.common.service.*;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.db.config.IdGenerator;
import com.cairh.cpe.service.aidispatch.service.IAiDispatchAchieveService;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorkBusinFLowTaskServiceImpl extends AbstractAuditDetailExport<TaskDetailRequest, TaskDetailInfoExport, BusinFlowTask> implements IWorkBusinFlowTaskService {

    private final Map<String, DictInfo> dictInfoMap = new ConcurrentHashMap<>(128);
    private final Map<String, BranchInfo> branchInfoMap = new ConcurrentHashMap<>(128);
    @Autowired
    private RedissonUtil redissonUtil;
    @Autowired
    private IAiDispatchAchieveService aiDispatchAchieveService;
    @Autowired
    private IBusinFlowTaskService businFlowTaskService;
    @Autowired
    private CacheBackendUser backendUser;
    @Autowired
    private CacheBranch cacheBranch;
    @Autowired
    private MybusinFlowTaskMapper mybusinFlowTaskMapper;
    @Autowired
    private RedisTemplate redisTemplate;
    @Resource
    private ILabelService labelService;
    @Resource
    private IBusinFlowRecordService businFlowRecordService;
    @Autowired
    private CenterBusinFlowTaskMapper centerBusinFlowTaskMapper;
    @Autowired
    private IBusinFlowRequestService businFlowRequestService;
    @Resource
    private IUserQueryExtInfoService userQueryExtInfoService;
    @Resource
    private IStagingTaskRecordService stagingTaskRecordService;
    @Resource
    private IdGenerator idGenerator;
    @Resource
    private IHandupDetailsService handupDetailsService;
    @Resource
    private CacheDict cacheDict;

    @Override
    public void dealOperatorDispatchStatus(OperatorDispatchStatus operatorDispatchStatus) {
        //通知派单系统。该操作员已上线
        aiDispatchAchieveService.changeOperatorStatus(operatorDispatchStatus.getDispatchStatus(), operatorDispatchStatus.getOperator_no());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchRecoveryTask(BusinFlowTask businFlowTask) {
        LambdaUpdateWrapper<BusinFlowTask> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_PENDING);
        updateWrapper.set(BusinFlowTask::getOperator_no, " ");
        updateWrapper.set(BusinFlowTask::getOperator_name, " ");
        updateWrapper.set(BusinFlowTask::getDeal_datetime, null);
        updateWrapper.set(BusinFlowTask::getTask_source, " ");
        updateWrapper.set(BusinFlowTask::getSuspend_remind_num, Integer.parseInt("0"));
        updateWrapper.set(BusinFlowTask::getAllow_auditor, " ");
        updateWrapper.set(BusinFlowTask::getPush_flag, WskhConstant.NO_PUSH_FLAG);
        updateWrapper.eq(BusinFlowTask::getSerial_id, businFlowTask.getSerial_id());
        businFlowTaskService.update(updateWrapper);

        LambdaUpdateWrapper<BusinFlowRequest> businFlowRequestLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        businFlowRequestLambdaUpdateWrapper.set(BusinFlowRequest::getOperator_no, " ")
                .set(BusinFlowRequest::getRequest_status, FlowStatusConst.AUDIT_PENDING)
                .eq(BusinFlowRequest::getRequest_no, businFlowTask.getRequest_no());
        businFlowRequestService.update(businFlowRequestLambdaUpdateWrapper);

        //更新user拓展表
        userQueryExtInfoService.updateOperatorInfo(businFlowTask.getRequest_no(), " ", " ", FlowStatusConst.AUDIT_PENDING, businFlowTask.getTask_type());

        //记录流水
        HashMap<String, Object> params = new HashMap<>();
        params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22392);
        params.put(Fields.BUSINESS_REMARK, "手动批量回收任务");
        params.put(Fields.REQUEST_STATUS, "1");
        params.put(Fields.RECORD_TYPE, FlowStatusConst.SYSTEM_RECORD_TYPE);
        businFlowRecordService.saveBusinFlowRecord(businFlowTask.getRequest_no(), params);
    }

    @Override
    public boolean queryOperatorStatus(String operator) {
        //查询该操作员是否已上线
        return aiDispatchAchieveService.getOperatorStatus(operator);
    }

    @Override
    public Result<String> batchRecovery(BatchRecoveryForm batchRecoveryForm) {
        String[] allowRecyleStatus = FlowStatusConst.ALLOW_RECYLE_STATUS;
        String operator_no = batchRecoveryForm.getOperator_no();
        QueryWrapper<BusinFlowTask> wrapper = new QueryWrapper<>();
        wrapper.eq("operator_no", operator_no);
        wrapper.in("task_status", Arrays.asList(allowRecyleStatus));
        List<BusinFlowTask> businFlowTasks = businFlowTaskService.getBaseMapper().selectList(wrapper);

        OperatorDispatchStatus operatorDispatchStatus = new OperatorDispatchStatus();
        operatorDispatchStatus.setOperator_no(operator_no);
        operatorDispatchStatus.setOperator_name(batchRecoveryForm.getOperator_name());
        operatorDispatchStatus.setDispatchStatus(WskhConstant.CLOSE_DISPATCH);
        if (CollectionUtils.isEmpty(businFlowTasks)) {
            //直接调用下线接口
            dealOperatorDispatchStatus(operatorDispatchStatus);
            return Result.success();
        }
        //下线接口
        dealOperatorDispatchStatus(operatorDispatchStatus);
        //回收任务
        for (BusinFlowTask businFlowTask : businFlowTasks) {

            String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, businFlowTask.getRequest_no());
            try {
                boolean isLock = redissonUtil.tryLock(lockKey, 5, 10, TimeUnit.SECONDS);
                if (!isLock) {
                    log.info("执行手动批量回收任务长时间未获取锁，【{}】", businFlowTask.getRequest_no());
                    continue;
                }

                BusinFlowTask newTask = businFlowTaskService.getById(businFlowTask.getSerial_id());
                if (FlowStatusConst.AUDIT_PASS.equals(newTask.getTask_status())
                        || FlowStatusConst.AUDIT_NO_PASS.equals(newTask.getTask_status())
                        || FlowStatusConst.AUDIT_INVALIDATE.equals(newTask.getTask_status())) {
                    continue;
                }
                // 通知智能派单作废任务
                aiDispatchAchieveService.cancelTask(newTask.getDispatch_task_id(), WskhConstant.AUTO_DISPATCH_OPERATOR_NO, "manual");
                //回收用户任务 websocket 弹框  该弹框只有任务详情才可以显示出来
                RecyleTaskMessage recyleTaskMessage = new RecyleTaskMessage();
                recyleTaskMessage.setTask_type(newTask.getTask_type());
                recyleTaskMessage.setRequest_no(newTask.getRequest_no());
                recyleTaskMessage.setOperator_no(newTask.getOperator_no());
                redisTemplate.convertAndSend(Constant.CHANNEL_TRANSFER_TASK, JSON.toJSONString(recyleTaskMessage));

                //修改数据
                IWorkBusinFlowTaskService taskService = SpringUtil.getBean(IWorkBusinFlowTaskService.class);
                taskService.batchRecoveryTask(newTask);
            } catch (Exception e) {
                log.error("批量回收任务异常 request_no={} ,serial_id={}", businFlowTask.getRequest_no(), businFlowTask.getSerial_id(), e);
            } finally {
                redissonUtil.unlock(lockKey);
            }
        }
        return Result.success();
    }


    @Override
    public Page<TaskDetailInfo> selectTaskListByPage(TaskDetailRequest taskDetailRequest) {
        Page<TaskDetailInfo> page = new Page<>(taskDetailRequest.getCur_page(), taskDetailRequest.getPage_size());

        List<String> userRoleAuditList = Lists.newArrayList();
        if (taskDetailRequest.getUser_role().contains(UserRoleConstant.audit_role)) {
            userRoleAuditList.add(FlowNodeConst.AUDIT);
        }
        if (taskDetailRequest.getUser_role().contains(UserRoleConstant.review_role)) {
            userRoleAuditList.add(FlowNodeConst.REVIEW);
        }
        if (taskDetailRequest.getUser_role().contains(UserRoleConstant.secondary_review_role)) {
            userRoleAuditList.add(FlowNodeConst.SECONDARY_REVIEW);
        }
        if (CollectionUtils.isEmpty(userRoleAuditList)) {
            return page;
        }
        List<String> operatorBranchs = getOperatorBranchList(taskDetailRequest.getOperator_no(), taskDetailRequest.getBranch_no());
        if (CollectionUtils.isEmpty(operatorBranchs)) {
            return page;
        }
        taskDetailRequest.setOperator_branch(operatorBranchs);

        taskDetailRequest.setIsOpenDispatch(PropertySource.get(PropKeyConstant.WSKH_ISOPEN_DISPATCH, ""));
        //选择任务状态
        if (StringUtils.isNotBlank(taskDetailRequest.getTask_status())) {
            List<String> filterList = Arrays.stream(taskDetailRequest.getTask_status().split(","))
                    .filter(e -> userRoleAuditList.contains(e.split("-")[0]))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterList)) {
                return page;
            }
            taskDetailRequest.setTask_status_type_list(filterList);
        } else {
            taskDetailRequest.setTask_types_role(userRoleAuditList);
        }

        page.setOptimizeCountSql(true);
        List<TaskDetailInfo> resData = null;
        if ("1".equals(taskDetailRequest.getManual_claim_data()) && "0".equals(taskDetailRequest.getIs_task_id_data())) {
            //查询待处理的任务(手动认领)
            Page<TaskDetailInfo> taskDetailInfoPage = mybusinFlowTaskMapper.selectTaskListByPage(page, taskDetailRequest);
            resData = taskDetailInfoPage.getRecords();
        }
        if (!"1".equals(taskDetailRequest.getManual_claim_data()) && "0".equals(taskDetailRequest.getIs_task_id_data())) {
            //查询待处理的任务(自动派发)
            page.setSearchCount(false);
            List<TaskDetailInfo> taskDetailInfos = mybusinFlowTaskMapper.selectTaskList(taskDetailRequest);
            page.setTotal(taskDetailInfos.size());
            int startIndex = (taskDetailRequest.getCur_page() - 1) * taskDetailRequest.getPage_size();
            resData = taskDetailInfos.stream().
                    sorted(byTaskStatusOrder()
                            .thenComparing(TaskDetailInfo::getVideo_type, Comparator.reverseOrder())
                            .thenComparing(TaskDetailInfo::getDeal_datetime, Comparator.nullsFirst(Comparator.naturalOrder())))
                    .skip(startIndex).limit(taskDetailRequest.getPage_size()).collect(Collectors.toList());
            page.setRecords(resData);
        }
        //已处理
        if ("1".equals(taskDetailRequest.getIs_task_id_data())) {
            Page<TaskDetailInfo> taskDetailInfoPage = mybusinFlowTaskMapper.selectTaskListByPageByTaskId(page, taskDetailRequest);
            resData = taskDetailInfoPage.getRecords();
        }
        Map<String, Label> labelMap = labelService.getMapLabels();
        Map<String, BranchInfo> branchInfoMap = cacheBranch.getAllBranchList().stream().collect(Collectors.toMap(BranchInfo::getBranch_no, x -> x));
        Date expireDate = DateUtils.addMinutes(new Date(), -Integer.parseInt(PropertySource.get(PropKeyConstant.WSKH_REMIND_TASK_TIME, "10")));
        resData.stream().peek(e -> ConvertUtil.convertToMyTaskDetailInfoVO(e, taskDetailRequest, branchInfoMap, labelMap, expireDate)).collect(Collectors.toList());
        return page;
    }

    @Override
    public List<TaskDetailInfoExport> exportTaskList(TaskDetailRequest taskDetailRequest) {
        Set<String> userRoleAuditSet = Sets.newHashSet();
        if (taskDetailRequest.getUser_role().contains(UserRoleConstant.audit_role)) {
            userRoleAuditSet.add(FlowNodeConst.AUDIT);
        }
        if (taskDetailRequest.getUser_role().contains(UserRoleConstant.review_role)) {
            userRoleAuditSet.add(FlowNodeConst.REVIEW);
        }
        if (taskDetailRequest.getUser_role().contains(UserRoleConstant.secondary_review_role)) {
            userRoleAuditSet.add(FlowNodeConst.SECONDARY_REVIEW);
        }
        if (CollectionUtils.isEmpty(userRoleAuditSet)) {
            return Collections.emptyList();
        }
        List<String> operatorBranchs = getOperatorBranchList(taskDetailRequest.getOperator_no(), taskDetailRequest.getBranch_no());
        if (CollectionUtils.isEmpty(operatorBranchs)) {
            return Collections.emptyList();
        }
        taskDetailRequest.setOperator_branch(operatorBranchs);

        taskDetailRequest.setIsOpenDispatch(PropertySource.get(PropKeyConstant.WSKH_ISOPEN_DISPATCH, ""));
        //选择任务状态
        if (StringUtils.isNotBlank(taskDetailRequest.getTask_status())) {
            taskDetailRequest.setTask_status_type_list(Arrays.stream(taskDetailRequest.getTask_status().split(","))
                    .filter(e -> userRoleAuditSet.contains(e.split("-")[0]))
                    .collect(Collectors.toList()));
        }
        return getTaskDetailInfoExportList(taskDetailRequest, "myTask");
    }

    private Comparator<TaskDetailInfo> byTaskStatusOrder() {
        return Comparator.comparingInt(task -> {
            String status = task.getTask_status();
            if (FlowStatusConst.AUDIT_TRANSFER.equals(status)) {
                return 0;
            } else if (FlowStatusConst.AUDIT_SUSPEND.equals(status)) {
                return 1;
            } else if (FlowStatusConst.AUDIT_AUDITING.equals(status)) {
                return 2;
            } else {
                return 3;
            }
        });
    }


    @Override
    public TaskCountNum countTaskNum(BaseUser baseUser) {
        CountTaskForm countTaskForm = new CountTaskForm();
        countTaskForm.setOperator_no(baseUser.getStaff_no());
        countTaskForm.setCurrent_time_start(LocalDate.now().toString());
        TaskCountNum taskCountNum = new TaskCountNum();
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        futures.add(CompletableFuture.runAsync(() -> {
            TaskFinishCountNum finishCountNum = mybusinFlowTaskMapper.countTaskNum(countTaskForm);
            BeanUtils.copyProperties(finishCountNum, taskCountNum);
        }));
        futures.add(CompletableFuture.runAsync(() -> {
            TaskPendingCountNum taskPendingCountNum = mybusinFlowTaskMapper.countAuditPendingNum(countTaskForm);
            BeanUtils.copyProperties(taskPendingCountNum, taskCountNum);
        }));
        futures.add(CompletableFuture.runAsync(() -> {
            taskCountNum.setOut_call_num(mybusinFlowTaskMapper.countCallsNum(countTaskForm) + "");
        }));
        futures.add(CompletableFuture.runAsync(() -> {
            LambdaQueryWrapper<HandupDetails> wrapper = new LambdaQueryWrapper<>();
            Date today = new Date();
            Date startDate = DateUtil.beginOfDay(today);
            Date endDate = DateUtil.endOfDay(today);
            wrapper.eq(HandupDetails::getOperator_no, baseUser.getStaff_no())
                    .eq(HandupDetails::getHandup_type, HandupTypeEnum.RECOVERY.getCode())
                    .ge(HandupDetails::getCreate_datetime, startDate)
                    .le(HandupDetails::getCreate_datetime, endDate);
            taskCountNum.setRecycle_num(handupDetailsService.count(wrapper) + "");
        }));
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        String user_role = baseUser.getUser_role();
        int filterTotal = 0;
        int filterPending = 0;
        if (!user_role.contains(UserRoleConstant.audit_role)) {
            taskCountNum.setAudit_num("0");
            taskCountNum.setAudit_notpass_num("0");
            taskCountNum.setAudit_pass_num("0");
            filterTotal = Integer.parseInt(taskCountNum.getAudit_num());
            filterPending = Integer.parseInt(taskCountNum.getAudit_pend_num());
        }
        if (!user_role.contains(UserRoleConstant.review_role)) {
            taskCountNum.setReview_num("0");
            taskCountNum.setReview_notpass_num("0");
            taskCountNum.setReview_pass_num("0");
            filterTotal += Integer.parseInt(taskCountNum.getReview_num());
            filterPending = Integer.parseInt(taskCountNum.getReview_pend_num());
        }
        if (!user_role.contains(UserRoleConstant.secondary_review_role)) {
            taskCountNum.setSecondary_review_num("0");
            taskCountNum.setSecondary_review_notpass_num("0");
            taskCountNum.setSecondary_review_pass_num("0");
            filterTotal += Integer.parseInt(taskCountNum.getSecondary_review_num());
            filterPending = Integer.parseInt(taskCountNum.getSecondary_review_pend_num());
        }
        if (filterTotal > 0) {
            int taskNum = Integer.parseInt(taskCountNum.getTask_num());
            taskCountNum.setTask_num((taskNum - filterTotal) + "");
        }
        if (filterPending > 0) {
            int pendingNum = Integer.parseInt(taskCountNum.getAudit_pending_num());
            taskCountNum.setAudit_pending_num((pendingNum - filterPending) + "");
        }
        return taskCountNum;
    }

    @Override
    public Page<TaskDetailInfo> claimTaskQuery(TaskDetailForm taskDetailForm) {
        Page<TaskDetailInfo> page = new Page<>(taskDetailForm.getCur_page(), taskDetailForm.getPage_size());
        TaskDetailRequest taskDetailRequest = new TaskDetailRequest();
        taskDetailRequest.setOperator_no(taskDetailForm.getOperator_no());
        taskDetailRequest.setId_no(taskDetailForm.getQueryKeyWord());
        taskDetailRequest.setClient_name(taskDetailForm.getQueryKeyWord());
        taskDetailRequest.setUser_role(taskDetailForm.getUser_role());
        taskDetailRequest.setMobile_tel(taskDetailForm.getQueryKeyWord());
        taskDetailRequest.setQueryType(null == taskDetailForm.getQueryType() ? 1 : taskDetailForm.getQueryType());
        //用户可操作营业部
        taskDetailRequest.setOperator_branch(backendUser.getEnBranchNo(taskDetailForm.getOperator_no()));
        //权限展示的任务类型
        List<String> task_types_role = new ArrayList<>();
        if (StringUtils.isNotBlank(taskDetailRequest.getUser_role())) {
            //判断任务类型是否为空
            String user_role = taskDetailRequest.getUser_role();
            if (user_role.contains(UserRoleConstant.audit_role)) {
                task_types_role.add(FlowNodeConst.AUDIT);
            }
            if (user_role.contains(UserRoleConstant.review_role)) {
                task_types_role.add(FlowNodeConst.REVIEW);
            }
            if (user_role.contains(UserRoleConstant.secondary_review_role)) {
                task_types_role.add(FlowNodeConst.SECONDARY_REVIEW);
            }
            taskDetailRequest.setTask_types_role(task_types_role);
        }

        Page<TaskDetailInfo> detailInfoPage = mybusinFlowTaskMapper.claimTaskQuery(page, taskDetailRequest);
        detailInfoPage.getRecords().stream().peek(e -> {
            e.setVideo_type_code(e.getVideo_type());
            // push_flag=8、不允许审核人不包含当前操作人
            if (WskhConstant.ALREADY_PUSH_FLAG.equals(e.getPush_flag())
                    && !e.getNot_allow_auditor().contains(taskDetailForm.getOperator_no())
            ) {
                // 任务类型为见证&复核&二次复核
                if (task_types_role.contains(e.getTask_type_str()) && StringUtils.isBlank(e.getOperator_no())
                        && !StringUtils.equals(e.getTask_status(), FlowStatusConst.AUDIT_INVALIDATE)) {
                    // 认领按钮
                    e.setShow_button("1");
                    // 审核人为当前操作人并且任务状态为1、2、a
                } else if (StringUtils.equals(e.getOperator_no(), taskDetailForm.getOperator_no())
                        && ArrayUtils.contains(FlowStatusConst.ALLOW_RECYLE_STATUS, e.getTask_status())) {
                    // 处理按钮
                    e.setShow_button("3");
                } else {
                    // 查询按钮
                    e.setShow_button("2");
                }
            } else {
                // 查询按钮
                e.setShow_button("2");
            }
        }).collect(Collectors.toList());
        return detailInfoPage;
    }


    /**
     * 认领
     *
     * @param claimTaskForm
     * @param currentOperator
     * @return
     */
    @Override
    public Result<String> claimTask(ClaimTaskForm claimTaskForm, String currentOperator) {
        BusinFlowTask businFlowTask = businFlowTaskService.getById(claimTaskForm.getSerial_id());
        if (!StringUtils.equals(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_PENDING)) {
            return Result.fail(ErrorEnum.AUDIT_NOT_PENDING_TASK.getValue(),
                    String.format(ErrorEnum.AUDIT_NOT_PENDING_TASK.getDesc()));
        }
        // 不允许的操作人无法操作
        if (StringUtils.contains(businFlowTask.getNot_allow_auditor(), currentOperator)) {
            return Result.fail(ErrorEnum.AUDIT_NOT_AUTHORIZED_CLAIM.getValue(), ErrorEnum.AUDIT_NOT_AUTHORIZED_CLAIM.getDesc());
        }

        if (StringUtils.equalsAny(businFlowTask.getPush_flag(), WskhConstant.NO_PUSH_FLAG, WskhConstant.STAGING_PUSH_FLAG)) {
            return Result.fail(ErrorEnum.AUDIT_NOT_PUSH_DISPATCH.getValue(), ErrorEnum.AUDIT_NOT_PUSH_DISPATCH.getDesc());
        }

        if (StringUtils.isNotBlank(businFlowTask.getOperator_no())) {
            if (StringUtils.equals(businFlowTask.getOperator_no(), currentOperator)) {
                return Result.success();
            }
            return Result.fail(ErrorEnum.AUDIT_DIS_SYNC_ERROR.getValue(), ErrorEnum.AUDIT_DIS_SYNC_ERROR.getDesc());
        }
        try {
            //通知派单认领任务
            if (!StringUtils.equals(businFlowTask.getPush_flag(), WskhConstant.NEED_PUSH_FLAG)) {
                aiDispatchAchieveService.claimTask(businFlowTask.getDispatch_task_id(), claimTaskForm.getOperator_no(), claimTaskForm.getPop_msg_model());
            }
            return Result.success();
        } catch (Exception e) {
            log.error("通知派单任务已被手动认领失败,原因如下serial_id ={}", claimTaskForm.getSerial_id(), e);
            throw new BizException(ErrorEnum.AUDIT_TASK_CLAIM_ERROR.getValue(), e.getMessage());
        }
    }


    @Override
    public Page<TaskDetailInfo> selectCenterTaskListByPage(TaskDetailRequest taskDetailRequest) {
        Page<TaskDetailInfo> page = new Page<>(taskDetailRequest.getCur_page(), taskDetailRequest.getPage_size());
        List<String> operatorBranchs = getOperatorBranchList(taskDetailRequest.getOperator_no(), taskDetailRequest.getBranch_no());
        if (CollectionUtils.isEmpty(operatorBranchs)) {
            return page;
        }
        taskDetailRequest.setOperator_branch(operatorBranchs);
        String tabFlagFinished = "1";
        String tabFlagStaging = "2";
        //选择任务状态类似audit-1
        if (StringUtils.isNotBlank(taskDetailRequest.getTask_status())) {
            taskDetailRequest.setTask_status_type_list(Arrays.stream(taskDetailRequest.getTask_status().split(",")).collect(Collectors.toList()));
        }
        Page<TaskDetailInfo> list;
        page.setOptimizeCountSql(true);
        if (tabFlagFinished.equals(taskDetailRequest.getComplete_flag())) {
            // 已完成
            list = centerBusinFlowTaskMapper.selectCompleteCenterTaskListByPage(page, taskDetailRequest);
        } else if (tabFlagStaging.equals(taskDetailRequest.getComplete_flag())) {
            // 暂存数据
            list = centerBusinFlowTaskMapper.selectStagingCenterTaskListByPage(page, taskDetailRequest);
        } else {
            list = centerBusinFlowTaskMapper.selectCenterTaskListByPage(page, taskDetailRequest);
        }
        Map<String, BranchInfo> branchInfoMap = cacheBranch.getAllBranchList().stream().collect(Collectors.toMap(BranchInfo::getBranch_no, x -> x));
        Map<String, Label> labelMap = labelService.getMapLabels();
        Date expireDate = DateUtils.addMinutes(new Date(), -Integer.parseInt(PropertySource.get(PropKeyConstant.WSKH_REMIND_TASK_TIME, "10")));
        list.getRecords().stream().peek(e -> ConvertUtil.convertToTaskDetailInfoVO(e, taskDetailRequest, branchInfoMap, labelMap, tabFlagFinished, expireDate)).collect(Collectors.toList());
        return list;
    }

    @Override
    public List<TaskDetailInfoExport> exportCenterTaskList(BaseUser baseUser, TaskDetailRequest taskDetailRequest) {
        List<String> operatorBranchs = this.getOperatorBranchList(taskDetailRequest.getOperator_no(), taskDetailRequest.getBranch_no());
        if (CollectionUtils.isEmpty(operatorBranchs)) {
            return Collections.emptyList();
        }
        this.setTaskDetailRequest(taskDetailRequest, operatorBranchs);
        return getTaskDetailInfoExportList(taskDetailRequest, "taskCenter");
    }


    @Override
    public TaskCountNum countCenterTaskNum(BaseUser baseUser) {
        List<String> branchNos = backendUser.getEnBranchNo(baseUser.getStaff_no());
        if (CollectionUtils.isEmpty(branchNos)) {
            return new TaskCountNum();
        }
        CountTaskForm countTaskForm = new CountTaskForm();
        countTaskForm.setOperator_no(baseUser.getStaff_no());
        countTaskForm.setOperator_branch(branchNos);

        TaskCountNum taskCountNum = new TaskCountNum();
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        futures.add(CompletableFuture.runAsync(() -> {
            TaskFinishCountNum finishCountNum = centerBusinFlowTaskMapper.countCenterTaskNum(countTaskForm);
            BeanUtils.copyProperties(finishCountNum, taskCountNum);
        }));
        futures.add(CompletableFuture.runAsync(() -> {
            TaskPendingCountNum taskPendingCountNum = centerBusinFlowTaskMapper.countAuditPendingNum(countTaskForm);
            BeanUtils.copyProperties(taskPendingCountNum, taskCountNum);
        }));
        futures.add(CompletableFuture.runAsync(() -> {
            taskCountNum.setOut_call_num(centerBusinFlowTaskMapper.countCallsNum(countTaskForm) + "");
        }));
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        return taskCountNum;
    }


    @Override
    public boolean updateTask(String serial_id, BusinFlowTaskEditReq req) {
        Assert.notNull(serial_id, "serial_id cannot be null");
        BusinFlowTask task = businFlowTaskService.getById(serial_id);
        Assert.notNull(task, "BusinFlowTask not find by serial_id");
        LambdaUpdateWrapper<BusinFlowTask> wrapper = new LambdaUpdateWrapper();
        wrapper.set(null != req.getTask_status(), BusinFlowTask::getTask_status, req.getTask_status());
        wrapper.set(null != req.getTask_type(), BusinFlowTask::getTask_type, req.getTask_type());
        wrapper.set(null != req.getOperator_no(), BusinFlowTask::getOperator_no, req.getOperator_no());
        wrapper.set(null != req.getOperator_name(), BusinFlowTask::getOperator_name, req.getOperator_name());
        wrapper.set(null != req.getPush_flag(), BusinFlowTask::getPush_flag, req.getPush_flag());
        wrapper.eq(BusinFlowTask::getSerial_id, serial_id);
        return businFlowTaskService.update(wrapper);
    }

    /**
     * 单个任务暂存
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void taskStaging(BaseUser baseUser, TaskStagingAndActivationForm form) {
        log.info("开始执行任务暂存, request_no: {} flow_task_id: {}, operator: {}", form.getRequest_no(), form.getFlow_task_id(), baseUser.getStaff_no());
        BusinFlowTask businFlowTask = businFlowTaskService.getById(form.getFlow_task_id());
        // 1. 验证任务信息
        if (businFlowTask == null) {
            throw new BizException(ErrorEnum.DATA_NOT_EXIST.getValue(), ErrorEnum.DATA_NOT_EXIST.getDesc());
        }
        if (!StringUtils.equals(WskhConstant.VIDEO_TYPE_1, businFlowTask.getVideo_type())) {
            throw new BizException(ErrorEnum.DATA_STATUS_ERROR.getValue(), ErrorEnum.DATA_STATUS_ERROR.getDesc());
        }
        if (!FlowStatusConst.AUDIT_PENDING.equals(businFlowTask.getTask_status())) {
            throw new BizException(ErrorEnum.DATA_STATUS_ERROR.getValue(), ErrorEnum.DATA_STATUS_ERROR.getDesc());
        }
        if (!StringUtils.equalsAny(businFlowTask.getPush_flag(), WskhConstant.ALREADY_PUSH_FLAG, WskhConstant.NO_PUSH_FLAG)) {
            throw new BizException(ErrorEnum.DATA_STATUS_ERROR.getValue(), ErrorEnum.DATA_STATUS_ERROR.getDesc());
        }

        // 2. 取消派单任务 push_flag为8并且dispatch_task_id不为空
        if (StringUtils.equals(businFlowTask.getPush_flag(), WskhConstant.ALREADY_PUSH_FLAG)
                && StringUtils.isNotBlank(businFlowTask.getDispatch_task_id())) {
            log.info("暂存取消任务派单, request_no: {} flow_task_id: {}", businFlowTask.getRequest_no(), businFlowTask.getSerial_id());
            aiDispatchAchieveService.cancelTask(businFlowTask.getDispatch_task_id(), baseUser.getStaff_no(), "manual");
        }

        // 3. 更新任务状态
        LambdaUpdateWrapper<BusinFlowTask> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(BusinFlowTask::getSerial_id, businFlowTask.getSerial_id());
        lambdaUpdateWrapper.set(BusinFlowTask::getPush_flag, WskhConstant.STAGING_PUSH_FLAG);
        lambdaUpdateWrapper.set(BusinFlowTask::getOperator_no, StrUtil.SPACE);
        lambdaUpdateWrapper.set(BusinFlowTask::getOperator_name, StrUtil.SPACE);
        lambdaUpdateWrapper.set(BusinFlowTask::getDeal_datetime, null);
        lambdaUpdateWrapper.set(BusinFlowTask::getTask_source, StrUtil.SPACE);
        lambdaUpdateWrapper.set(BusinFlowTask::getDispatch_task_id, StrUtil.SPACE);
        boolean update = businFlowTaskService.update(lambdaUpdateWrapper);
        if (!update) {
            log.warn("暂存更新任务失败, request_no: {} flow_task_id: {}", form.getRequest_no(), form.getFlow_task_id());
        }

        // 4. 保存操作记录
        saveOperatorRecord(baseUser, businFlowTask, RecordTypeEnum.STAGING_OPTION);
        saveStagingTaskRecord(baseUser, businFlowTask, RecordTypeEnum.STAGING_OPTION);
        log.info("任务暂存完成, request_no: {} flow_task_id: {}", form.getRequest_no(), form.getFlow_task_id());
    }

    /**
     * 单个任务释放
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void taskActivation(BaseUser baseUser, TaskStagingAndActivationForm form) {
        log.info("开始执行任务释放, request_no: {} flow_task_id: {}, operator: {}", form.getRequest_no(), form.getFlow_task_id(), baseUser.getStaff_no());
        BusinFlowTask businFlowTask = businFlowTaskService.getById(form.getFlow_task_id());
        // 1. 验证任务状态
        if (businFlowTask == null) {
            throw new BizException(ErrorEnum.DATA_NOT_EXIST.getValue(), ErrorEnum.DATA_NOT_EXIST.getDesc());
        }
        if (!WskhConstant.STAGING_PUSH_FLAG.equals(businFlowTask.getPush_flag())) {
            throw new BizException(ErrorEnum.DATA_STATUS_ERROR.getValue(), ErrorEnum.DATA_STATUS_ERROR.getDesc());
        }

        // 2. 重新派单
        log.info("任务释放-重新派单处理, request_no: {} flow_task_id: {}", businFlowTask.getRequest_no(), businFlowTask.getSerial_id());
        // 手动推送派单
        aiDispatchAchieveService.submitDispatchData(businFlowTask.getRequest_no(), businFlowTask.getNot_allow_auditor(), businFlowTask.getSerial_id(), null);

        // 3. 保存操作记录
        saveOperatorRecord(baseUser, businFlowTask, RecordTypeEnum.ACTIVATION_OPTION);
        saveStagingTaskRecord(baseUser, businFlowTask, RecordTypeEnum.ACTIVATION_OPTION);
        log.info("任务释放完成, request_no: {} flow_task_id: {}", form.getRequest_no(), form.getFlow_task_id());
    }

    /**
     * 批量任务暂存
     */
    @Override
    public StagingTaskCount taskBulkStaging(BaseUser baseUser, TaskDetailRequest taskDetailRequest) {
        log.info("开始执行批量任务暂存, operator: {} taskDetailRequest: {}", baseUser.getStaff_no(), JSON.toJSONString(taskDetailRequest));
        return processBulkTasks(baseUser, taskDetailRequest, true);
    }

    /**
     * 批量任务释放
     */
    @Override
    public StagingTaskCount taskBulkActivation(BaseUser baseUser, TaskDetailRequest taskDetailRequest) {
        log.info("开始执行批量任务释放, operator: {} taskDetailRequest: {}", baseUser.getStaff_no(), JSON.toJSONString(taskDetailRequest));
        return processBulkTasks(baseUser, taskDetailRequest, false);
    }

    @Override
    int queryCount(TaskDetailRequest query, LambdaQueryWrapper<BusinFlowTask> wrapper, String sourceCode) {
        if ("myTask".equals(sourceCode)) {
            return mybusinFlowTaskMapper.taskListCount(query);
        }
        String tabFlagFinished = "1";
        int taskNum;
        try {
            if (tabFlagFinished.equals(query.getComplete_flag())) {
                // 判断任务是否已完成
                // 如果已完成，调用centerBusinFlowTaskMapper统计已完成的中心任务数量
                taskNum = centerBusinFlowTaskMapper.countCompleteCenterTask(query);
            } else {
                // 如果待处理，调用centerBusinFlowTaskMapper统计中心任务数量
                taskNum = centerBusinFlowTaskMapper.countCenterTask(query);
            }
        } catch (Exception e) {
            log.error("计算任务数量异常: {}", e.getMessage());
            return 0;
        }
        return taskNum;
    }

    @Override
    Page<TaskDetailInfoExport> getPageData(TaskDetailRequest taskDetailRequest, LambdaQueryWrapper<BusinFlowTask> wrapper, String sourceCode) {
        Page<TaskDetailInfoExport> page = new Page<>(1, WskhConstant.DATA_EXPORT_PAGE_SIZE);
        page.setSearchCount(false);
        if ("myTask".equals(sourceCode)) {
            return mybusinFlowTaskMapper.selectTaskAgentList(page, taskDetailRequest);
        }
        String tabFlagFinished = "1";
        Page<TaskDetailInfoExport> list;
        if (tabFlagFinished.equals(taskDetailRequest.getComplete_flag())) {
            list = centerBusinFlowTaskMapper.selectCompleteCenterTaskList(page, taskDetailRequest);
        } else {
            list = centerBusinFlowTaskMapper.selectCenterTaskList(page, taskDetailRequest);
        }
        return list;
    }

    @Override
    LambdaQueryWrapper<BusinFlowTask> refreshQuery(List<TaskDetailInfoExport> records, TaskDetailRequest query, LambdaQueryWrapper<BusinFlowTask> wrapper) {
        if (CollectionUtils.isEmpty(records)) {
            return wrapper;
        }
        TaskDetailInfoExport taskDetailInfoExport = records.get(records.size() - 1);
        if (Objects.nonNull(taskDetailInfoExport)) {
            query.setRequest_no(taskDetailInfoExport.getRequest_no());
        }
        return wrapper;
    }

    @Override
    void convertData(TaskDetailInfoExport taskDetailInfoExport, TaskDetailRequest taskDetailRequest) {
        // 分公司名称 company_name
        String up_branch_no = branchInfoMap.getOrDefault(taskDetailInfoExport.getBranch_no(), new BranchInfo()).getUp_branch_no();
        String companyName = StringUtils.isEmpty(up_branch_no) ? taskDetailInfoExport.getBranch_no() : branchInfoMap.getOrDefault(up_branch_no, new BranchInfo()).getBranch_name();
        taskDetailInfoExport.setCompany_name(companyName);
        // 证件类型 code_dict = "id_kind"
        taskDetailInfoExport.setId_kind(dictInfoMap.getOrDefault(DicConstant.ID_KIND_DICT + taskDetailInfoExport.getId_kind(), new DictInfo()).getSub_name());
        // 任务状态 code_dict = "task_status" 字段task_type转换
        taskDetailInfoExport.setTask_type(dictInfoMap.getOrDefault(DicConstant.TASK_STATUS_DICT + taskDetailInfoExport.getTask_type(), new DictInfo()).getSub_name());
        // 所属营业部 code_type = "branch"
        taskDetailInfoExport.setBranch_name(branchInfoMap.getOrDefault(taskDetailInfoExport.getBranch_no(), new BranchInfo()).getBranch_name());
        // 任务来源 code_dict = "task_source"
        taskDetailInfoExport.setTask_source(dictInfoMap.getOrDefault(DicConstant.TASK_SOURCE_DICT + taskDetailInfoExport.getTask_source(), new DictInfo()).getSub_name());
        // 视频见证方式 code_dict = "video_type"
        taskDetailInfoExport.setVideo_type(dictInfoMap.getOrDefault(DicConstant.VIDEO_TYPE_DICT + taskDetailInfoExport.getVideo_type(), new DictInfo()).getSub_name());
        // 业务类型 code_dict = "busin_type"
        taskDetailInfoExport.setBusin_type(dictInfoMap.getOrDefault(DicConstant.BUSIN_TYPE_DICT + taskDetailInfoExport.getBusin_type(), new DictInfo()).getSub_name());
        // 接入方式 code_dict = "app_id"
        taskDetailInfoExport.setApp_id(dictInfoMap.getOrDefault(DicConstant.APP_ID_DICT + taskDetailInfoExport.getApp_id(), new DictInfo()).getSub_name());
        // 接入方式 code_dict = "client_category"
        taskDetailInfoExport.setClient_category(dictInfoMap.getOrDefault(DicConstant.CLIENT_CATEGORY_DICT + taskDetailInfoExport.getClient_category(), new DictInfo()).getSub_name());
    }

    @Override
    void initCacheData() {
        // 数据准备
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.ID_KIND_DICT).stream().collect(Collectors.toMap(x -> DicConstant.ID_KIND_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.TASK_STATUS_DICT).stream().collect(Collectors.toMap(x -> DicConstant.TASK_STATUS_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.TASK_SOURCE_DICT).stream().collect(Collectors.toMap(x -> DicConstant.TASK_SOURCE_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.VIDEO_TYPE_DICT).stream().collect(Collectors.toMap(x -> DicConstant.VIDEO_TYPE_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.BUSIN_TYPE_DICT).stream().collect(Collectors.toMap(x -> DicConstant.BUSIN_TYPE_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.APP_ID_DICT).stream().collect(Collectors.toMap(x -> DicConstant.APP_ID_DICT + x.getSub_code(), x -> x)));
        dictInfoMap.putAll(cacheDict.getDictListByDictCode(DicConstant.CLIENT_CATEGORY_DICT).stream().collect(Collectors.toMap(x -> DicConstant.CLIENT_CATEGORY_DICT + x.getSub_code(), x -> x)));
        branchInfoMap.putAll(cacheBranch.getAllBranchList().stream().collect(Collectors.toMap(BranchInfo::getBranch_no, x -> x)));
    }

    private List<TaskDetailInfoExport> getTaskDetailInfoExportList(TaskDetailRequest taskDetailRequest, String sourceCode) {
        // 计数
        int taskNum = this.queryCount(taskDetailRequest, null, sourceCode);
        if (taskNum == 0) {
            return Collections.emptyList();
        }
        if (taskNum > 50000) {
            log.error("查询任务列表，导出数据超过50000条，请重新选择查询条件");
            throw new BizException("-9997", ErrorEnum.TOO_MUCH_DATA_MESSAGE.getDesc());
        }
        // 获取数据
        // 数据转换
        List<TaskDetailInfoExport> taskDetailInfoList;
        try {
            taskDetailInfoList = super.executeDataQueryAndTransformation(taskDetailRequest, taskNum, null, sourceCode);
        } catch (Exception e) {
            log.error("查询任务列表，获取导出数据消息异常：", e);
            throw new BizException("-9997", ErrorEnum.FAILED_GET_EXPORT_DATA_MESSAGE.getDesc());
        }
        return taskDetailInfoList;
    }

    private List<String> getOperatorBranchList(String operator_no, String branch_no) {
        List<String> enBranchNo = backendUser.getEnBranchNo(operator_no);
        if (CollectionUtils.isEmpty(enBranchNo)) {
            return Lists.newArrayList();
        }
        if (StringUtils.isBlank(branch_no)) {
            return enBranchNo;
        }
        List<String> branch_nos = Arrays.stream(branch_no.split(",")).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(branch_nos)) {
            return enBranchNo.stream().filter(branch_nos::contains).collect(Collectors.toList());
        }
        return enBranchNo;
    }

    /**
     * 设置任务详情请求的参数。
     *
     * @param taskDetailRequest 任务详情请求对象，用于设置任务相关参数。
     * @param operatorBranchs   操作分支列表，将被设置到任务详情请求对象中。
     */
    private void setTaskDetailRequest(TaskDetailRequest taskDetailRequest, List<String> operatorBranchs) {
        taskDetailRequest.setOperator_branch(operatorBranchs);
        //选择任务状态类似audit-1
        String taskStatus = taskDetailRequest.getTask_status();
        if (StringUtils.isNotBlank(taskStatus)) {
            List<String> taskStatusTypeList = Arrays.stream(taskStatus.split(","))
                    .filter(StringUtils::isNotBlank) // 过滤空或空白字符串
                    .collect(Collectors.toList());
            taskDetailRequest.setTask_status_type_list(taskStatusTypeList);
        } else {
            // 如果taskStatus为空或只包含空白字符，这里决定是否要设置为空列表或直接不设置
            taskDetailRequest.setTask_status_type_list(Collections.emptyList()); // 设为空列表，明确表示状态列表为空
        }
    }

    /**
     * 批量处理任务的通用方法
     */
    private StagingTaskCount processBulkTasks(BaseUser baseUser, TaskDetailRequest request, boolean isStaging) {
        // 请求参数处理
        requestDataHandle(baseUser, request);

        String typeValue = isStaging ? RecordTypeEnum.STAGING_OPTION.getValue() : RecordTypeEnum.ACTIVATION_OPTION.getValue();
        if (isStaging) {
            request.setPush_flags(Arrays.asList(WskhConstant.NO_PUSH_FLAG, WskhConstant.ALREADY_PUSH_FLAG));
        } else {
            request.setPush_flag(WskhConstant.STAGING_PUSH_FLAG);
        }
        log.info("批量{}请求参数：{}", typeValue, JSON.toJSONString(request));
        StagingTaskCount stagingTaskCount = new StagingTaskCount();
        // 校验暂存or释放数量
        int count = centerBusinFlowTaskMapper.countStagingOrActivationCenterTaskNum(request);
        if (count == 0) {
            log.info("{}任务查询为空，结束任务处理！", typeValue);
            stagingTaskCount.setTotal_num(0);
            stagingTaskCount.setSuccess_num(0);
            stagingTaskCount.setFail_num(0);
            return stagingTaskCount;
        }
        int max_staging_num = PropertySource.getInt(PropKeyConstant.WSKH_TASK_STAGING_MAX_NUM, 2000);
        if (count > max_staging_num) {
            String message = String.format(ErrorEnum.DATA_MAX_HANDLE_ERROR.getDesc(), typeValue, count, max_staging_num);
            throw new BizException(ErrorEnum.DATA_MAX_HANDLE_ERROR.getValue(), message);
        }
        Page<TaskStagingAndActivationInfo> page = new Page<>(1, max_staging_num, Boolean.FALSE);
        List<TaskStagingAndActivationInfo> tasks = isStaging
                ? centerBusinFlowTaskMapper.selectStagingCenterTaskListByPageAndSerialId(page, request).getRecords()
                : centerBusinFlowTaskMapper.selectActivationCenterTaskListByPageAndSerialId(page, request).getRecords();

        log.info("批量{}数量：{}", typeValue, count);
        AtomicInteger errorCount = new AtomicInteger(0);
        processBatch(baseUser, tasks, isStaging, errorCount);
        log.info("批量{}完成, 总处理: {}, 失败: {}", typeValue, count, errorCount.get());
        stagingTaskCount.setTotal_num(count);
        stagingTaskCount.setSuccess_num(count - errorCount.get());
        stagingTaskCount.setFail_num(errorCount.get());
        return stagingTaskCount;
    }

    private void requestDataHandle(BaseUser baseUser, TaskDetailRequest request) {
        List<String> branches = getOperatorBranchList(baseUser.getStaff_no(), request.getBranch_no());
        if (CollectionUtils.isEmpty(branches)) {
            String message = String.format("用户所属营业部为空！用户编号：%s", baseUser.getStaff_no());
            log.warn(message);
            throw new BizException(message);
        }
        request.setOperator_branch(branches);
        if (StringUtils.isNotBlank(request.getTask_status())) {
            request.setTask_status_type_list(Arrays.stream(request.getTask_status().split(StrUtil.COMMA)).collect(Collectors.toList()));
        }
    }

    /**
     * 批量处理任务
     */
    private void processBatch(BaseUser baseUser, List<TaskStagingAndActivationInfo> tasks,
                              boolean isStaging, AtomicInteger errorCount) {
        String optionValue = isStaging ? RecordTypeEnum.STAGING_OPTION.getValue() : RecordTypeEnum.ACTIVATION_OPTION.getValue();
        WorkBusinFLowTaskServiceImpl workBusinFLowTaskService = (WorkBusinFLowTaskServiceImpl) AopContext.currentProxy();
        for (TaskStagingAndActivationInfo task : tasks) {
            // 加锁
            String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, task.getRequest_no());
            try {
                boolean isLock = redissonUtil.tryLock(lockKey, 0, 8, TimeUnit.SECONDS);
                if (!isLock) {
                    errorCount.incrementAndGet();
                    log.warn("{}任务批量处理失败, request_no: {} flow_task_id: {} 加锁失败！", optionValue, task.getRequest_no(), task.getSerial_id());
                    continue;
                }
                TaskStagingAndActivationForm form = new TaskStagingAndActivationForm();
                form.setRequest_no(task.getRequest_no());
                form.setFlow_task_id(task.getSerial_id());
                form.setTask_id(task.getTask_id());
                if (isStaging) {
                    workBusinFLowTaskService.taskStaging(baseUser, form);
                } else {
                    workBusinFLowTaskService.taskActivation(baseUser, form);
                }
            } catch (Exception e) {
                errorCount.incrementAndGet();
                log.warn("{}任务批量处理失败, request_no: {} flow_task_id: {} 异常信息: ", optionValue, task.getRequest_no(), task.getSerial_id(), e);
            } finally {
                redissonUtil.unlock(lockKey);
            }
        }
    }

    /**
     * 保存操作记录
     */
    private void saveOperatorRecord(BaseUser baseUser, BusinFlowTask businFlowTask, RecordTypeEnum recordTypeEnum) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(Fields.OPERATOR_NO, baseUser.getStaff_no());
        params.put(Fields.OPERATOR_NAME, baseUser.getUser_name());
        params.put(Fields.BUSINESS_FLAG, RecordTypeEnum.STAGING_OPTION == recordTypeEnum ? WskhConstant.BUSINESS_FLAG_22405 : WskhConstant.BUSINESS_FLAG_22406);
        params.put(Fields.BUSINESS_REMARK, "手动" + recordTypeEnum.getValue());
        params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_OPERATE_TYPE);
        log.info("保存{}任务, request_no: {} flow_task_id: {}, 操作记录: {}", recordTypeEnum.getValue(),
                businFlowTask.getRequest_no(), businFlowTask.getSerial_id(), JSON.toJSONString(params));
        businFlowRecordService.saveBusinFlowRecord(businFlowTask.getRequest_no(), params);
    }

    /**
     * 保存暂存任务记录
     */
    private void saveStagingTaskRecord(BaseUser baseUser, BusinFlowTask businFlowTask, RecordTypeEnum recordTypeEnum) {
        StagingTaskRecord stagingTaskRecord = new StagingTaskRecord();
        stagingTaskRecord.setSerial_id(idGenerator.nextUUID(null));
        stagingTaskRecord.setRequest_no(businFlowTask.getRequest_no());
        stagingTaskRecord.setTask_id(businFlowTask.getTask_id());
        stagingTaskRecord.setFlow_task_id(businFlowTask.getSerial_id());
        stagingTaskRecord.setRecord_type(recordTypeEnum.getCode());
        stagingTaskRecord.setOperator_no(baseUser.getStaff_no());
        stagingTaskRecord.setOperator_name(baseUser.getUser_name());
        stagingTaskRecord.setMatch_info(JSON.toJSONString(businFlowTask));
        stagingTaskRecord.setCreate_datetime(new Date());
        log.info("保存{}任务记录, request_no: {} flow_task_id: {}, 信息: {}", recordTypeEnum.getValue(),
                businFlowTask.getRequest_no(), businFlowTask.getSerial_id(), JSON.toJSONString(stagingTaskRecord));
        stagingTaskRecordService.save(stagingTaskRecord);
    }

}
