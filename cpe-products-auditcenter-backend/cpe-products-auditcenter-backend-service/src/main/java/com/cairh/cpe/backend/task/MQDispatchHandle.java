package com.cairh.cpe.backend.task;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cairh.cpe.backend.task.mq.DispatchNoticeMqOperator;
import com.cairh.cpe.backend.task.mq.DispatchNoticeMqOperator.DispatchNoticeMqMsg;
import com.cairh.cpe.cache.service.CacheBackendUser;
import com.cairh.cpe.cache.service.CacheBranch;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.response.BranchInfo;
import com.cairh.cpe.common.entity.support.BackendUser;
import com.cairh.cpe.common.service.IBusinFlowRecordService;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.service.IBusinProcessRequestAuditTrailService;
import com.cairh.cpe.common.service.IHandupDetailsService;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.service.aidispatch.service.IAiDispatchAchieveService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class MQDispatchHandle implements ApplicationListener<ApplicationReadyEvent> {

    @Autowired
    private DispatchNoticeMqOperator dispatchNoticeMqOperator;
    @Autowired
    private IBusinFlowTaskService businFlowTaskService;

    @Autowired
    private CacheBackendUser cacheBackendUser;
    @Autowired
    private CacheBranch cacheBranch;

    @Autowired
    private IAiDispatchAchieveService aiDispatchAchieveService;

    @Autowired
    private RedissonUtil redissonUtil;

    @Resource
    private IBusinFlowRecordService businFlowRecordService;
    @Autowired
    private IHandupDetailsService handupDetailsService;
    @Autowired
    private IBusinProcessRequestAuditTrailService businProcessRequestAuditTrailService;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        dispatchNoticeMqOperator.receive(dispatchNoticeMqMsg -> {
            try {
                consumerMessage(dispatchNoticeMqMsg);
            } catch (Exception e) {
                log.error("有问题", e);
            }
        });
    }

    /**
     * enable.auto.commit = true 自动提交开启  auto.commit.interval.ms = 5000 自动提交5s  , 任务状态 1,2,3,4,a,b
     * 1 3s 获取不到锁抛出异常
     * 2 如果任务属于通过以及驳回的情况下 ，自动跳过 （task in 3,4）
     * 3 如果状态属于审核中 自动跳过 （task = 2 ）。
     * 4
     *
     * @param dispatchNoticeMqMsg
     */
    private void consumerMessage(DispatchNoticeMqMsg dispatchNoticeMqMsg) {
        DispatchNoticeMqMsg.NoticeMqTypeEnum noticeType = dispatchNoticeMqMsg.getNoticeType();
        DispatchNoticeMqMsg.DispatchMqMsg data = dispatchNoticeMqMsg.getDispatchMsg();
        if (null == noticeType || StringUtils.isBlank(data.getBusi_serial_no())) {
            return;
        }
        log.info("kafka接收到消息 dispatchNoticeMsg= {}", JSON.toJSONString(dispatchNoticeMqMsg));
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, data.getBusi_serial_no());
        try {

            boolean isLock = redissonUtil.tryLock(lockKey, 3, 20, TimeUnit.SECONDS);
            if (!isLock) {
                log.error("执行consumerMessage方法长时间未获取锁，【{}】", data.getBusi_serial_no());
                throw new BizException(ErrorEnum.AUDIT_TASK_MQ_ERROR_TIP.getValue(), ErrorEnum.AUDIT_TASK_MQ_ERROR_TIP.getDesc());
            }
            BusinFlowTask businFlowTask = getCurrentTask(data);
            switch (noticeType) {
                case DISTRIBUTED:
                    if (!checkTask(businFlowTask)) {
                        break;
                    }
                    updateTask(businFlowTask, data, DicConstant.TASK_SOURCE_1);
                    // 通知派单是否弹框
                    aiDispatchAchieveService.popUpMsg(Long.parseLong(WskhConstant.SUBSYS_ID), data.getOperator_no());

                    BackendUser backendUser = cacheBackendUser.getBackendUserByStaffNo(data.getOperator_no());
                    // 系统自动办理流水
                    HashMap<String, Object> params = new HashMap<>();
                    params.put(Fields.OPERATOR_NO, WskhConstant.SUPER_USER);
                    params.put(Fields.OPERATOR_NAME, WskhConstant.SUPER_USER);
                    params.put(Fields.RECORD_TYPE, FlowStatusConst.SYSTEM_RECORD_TYPE);
                    params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22397);
                    params.put(Fields.BUSINESS_REMARK, "系统自动分配【" + TaskTypeEnum.getValue(businFlowTask.getTask_type()) + "任务】给见证人" + backendUser.getUser_name() + "(" + data.getOperator_no() + ")");
                    businFlowRecordService.saveBusinFlowRecord(data.getBusi_serial_no(), params);
                    break;
                case APPOINT:
                    //任务指派接受完成 不需要校验是否审核中
                    BusinFlowTask appointCurrentTask = getCurrentTask(data);
                    if (!checkTask(appointCurrentTask)) {
                        break;
                    }
                    if (data.getCurrent_operator_no().equals(appointCurrentTask.getOperator_no())) {
                        log.info("已处理，消费信息");
                        break;
                    }
                    updateTask(appointCurrentTask, data, DicConstant.TASK_SOURCE_2);
                    log.info("指派完成 serial_id = {} 指派给新的操作人[{}]，old的操作人={}", appointCurrentTask.getSerial_id(), data.getOperator_no(), appointCurrentTask.getOperator_no());
                    break;
                case TRANSFER:
                    //任务转派 需要派单发起  不判断是否审核中
                    BusinFlowTask transferCurrentTask = getCurrentTask(data);
                    if (!checkTask(transferCurrentTask)) {
                        break;
                    }
                    if (data.getOperator_no().equals(transferCurrentTask.getOperator_no())) {
                        log.info("已处理，消费信息");
                        break;
                    }
                    updateTask(transferCurrentTask, data, DicConstant.TASK_SOURCE_4);
                    log.info("转派完成 serial_id = {} 转派给新的操作人[{}]，old的操作人={}", transferCurrentTask.getSerial_id(), data.getOperator_no(), transferCurrentTask.getOperator_no());
                    break;
                case HANDLE:
                case CANCEL:
                    break;
                case RELEASE:
                    //派单释放 通知见证
                    BusinFlowTask releaseCurrentTask = getCurrentTask(data);
                    if (!checkTask(releaseCurrentTask)) {
                        break;
                    }
                    LambdaUpdateWrapper<BusinFlowTask> taskWrapper = new LambdaUpdateWrapper<>();
                    taskWrapper.set(BusinFlowTask::getAllow_auditor, " ");
                    taskWrapper.set(BusinFlowTask::getOperator_no, " ");
                    taskWrapper.set(BusinFlowTask::getOperator_name, " ");
                    taskWrapper.set(BusinFlowTask::getDeal_datetime, null);
                    taskWrapper.eq(BusinFlowTask::getSerial_id, releaseCurrentTask.getSerial_id());
                    businFlowTaskService.update(taskWrapper);
                    log.info("释放完成 serial_id = {} old的操作人={}", data.getBusi_serial_no(), releaseCurrentTask.getOperator_no());
                    break;
                case RECOVERY:
                    //派单超时任务回收
                    BusinFlowTask recoveryCurrentTask = getCurrentTask(data);
                    if (!checkTask(recoveryCurrentTask)) {
                        break;
                    }
                    LambdaUpdateWrapper<BusinFlowTask> wrapper = new LambdaUpdateWrapper<>();
                    wrapper.set(BusinFlowTask::getAllow_auditor, " ");
                    wrapper.set(BusinFlowTask::getOperator_no, " ");
                    wrapper.set(BusinFlowTask::getOperator_name, " ");
                    wrapper.set(BusinFlowTask::getDeal_datetime, null);
                    wrapper.eq(BusinFlowTask::getSerial_id, recoveryCurrentTask.getSerial_id());
                    businFlowTaskService.update(wrapper);
                    // 回收记录流水表
                    handupDetailsService.saveHandupDetails(recoveryCurrentTask, HandupTypeEnum.RECOVERY);
                    log.info("回收完成 serial_id = {} old的操作人={}", data.getBusi_serial_no(), recoveryCurrentTask.getOperator_no());
                    break;
            }
        } catch (Exception e) {
            log.error("审核通过请求失败", e);
            throw new BizException("-1", e.getMessage());
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }

    BusinFlowTask getCurrentTask(DispatchNoticeMqMsg.DispatchMqMsg data) {
        LambdaQueryWrapper<BusinFlowTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinFlowTask::getRequest_no, data.getBusi_serial_no())
                .eq(BusinFlowTask::getDispatch_task_id, data.getTask_id());
        List<BusinFlowTask> list = businFlowTaskService.list(queryWrapper);
        log.info("开始处理任务业务编号[{}]，查询list是[{}]", data.getBusi_serial_no(), JSON.toJSONString(list));
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }


    private void updateTask(BusinFlowTask distributedCurrentTask, DispatchNoticeMqMsg.DispatchMqMsg data, String task_source) {
        LambdaUpdateWrapper<BusinFlowTask> taskWrapper = new LambdaUpdateWrapper<>();
        taskWrapper.set(BusinFlowTask::getDeal_datetime, new Date());
        taskWrapper.set(BusinFlowTask::getAllow_auditor, data.getOperator_no());
        taskWrapper.set(BusinFlowTask::getOperator_no, data.getOperator_no());
        taskWrapper.set(BusinFlowTask::getTask_source, task_source);
        BackendUser backendUser = cacheBackendUser.getBackendUserByStaffNo(data.getOperator_no());
        if (backendUser != null) {
            taskWrapper.set(BusinFlowTask::getOperator_name, backendUser.getUser_name());
        }
        if (StringUtils.isNotBlank(data.getWhite_listed_flag())) {
            taskWrapper.set(BusinFlowTask::getWhite_flag, data.getWhite_listed_flag());
        }
        if (Objects.nonNull(data.getWhite_datetime())) {
            taskWrapper.set(BusinFlowTask::getWhite_datetime, data.getWhite_datetime());
        }
        taskWrapper.eq(BusinFlowTask::getSerial_id, distributedCurrentTask.getSerial_id());
        businFlowTaskService.update(taskWrapper);
        log.info("接收派单mq消息调用updateTask开始处理任务业务编号:{},operator_no:{},task_source:{},serial_id:{}", data.getBusi_serial_no(), data.getOperator_no(), task_source, distributedCurrentTask.getSerial_id());

        // 更新任务跟踪表task_source、white_flag
        Map<String, Object> params = new HashMap<>(2);
        params.put(Fields.TASK_SOURCE, task_source);
        if (StringUtils.isNotBlank(data.getWhite_listed_flag())) {
            params.put(Fields.WHITE_FLAG, data.getWhite_listed_flag());
        }
        if (Objects.nonNull(data.getWhite_datetime())) {
            params.put(Fields.WHITE_DATETIME, data.getWhite_datetime());
        }
        params.put(Fields.DEAL_DATETIME, new Date());
        params.put(Fields.OPERATOR_NO, data.getOperator_no());
        if (backendUser != null) {
            params.put(Fields.OPERATOR_NAME, backendUser.getUser_name());
            params.put(Fields.OPERATOR_BRANCH_NO, backendUser.getBranch_no());
            BranchInfo branchInfo = cacheBranch.getBranchByNo(backendUser.getBranch_no());
            if (branchInfo != null) {
                params.put(Fields.OPERATOR_UP_BRANCH_NO, getUpBranchNo(branchInfo));
            }
        }
        businProcessRequestAuditTrailService.updateBusinProcessRequestAuditTrail(distributedCurrentTask, params,
                UpdateBusinProcessRequestAuditTrailSourceEnum.SOURCE_TASK_SPECIALFIELDS);
    }

    private String getUpBranchNo(BranchInfo branchInfo) {
        String upBranchNo = branchInfo.getUp_branch_no();
        if (StringUtils.equalsAny(branchInfo.getBranch_type(), BranchConstant.LEVEL_HEADQUARTERS, BranchConstant.LEVEL_SUBSIDIARY_COMPANY, BranchConstant.LEVEL_BRANCH_OFFICE)) {
            upBranchNo = branchInfo.getBranch_no();
        }
        return upBranchNo;
    }

    /**
     * 判断是否可操作
     *
     * @param task
     * @return
     */
    private boolean checkTask(BusinFlowTask task) {
        if (null == task) {
            return Boolean.FALSE;
        }
        List<String> noAllowStatus = Lists.newArrayList(FlowStatusConst.AUDIT_PASS, FlowStatusConst.AUDIT_NO_PASS, FlowStatusConst.AUDIT_INVALIDATE);
        if (noAllowStatus.contains(task.getTask_status())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
