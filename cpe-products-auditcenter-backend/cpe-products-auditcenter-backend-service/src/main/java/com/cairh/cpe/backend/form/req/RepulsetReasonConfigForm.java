package com.cairh.cpe.backend.form.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RepulsetReasonConfigForm implements Serializable {

    /**
     * 流水号
     */
    private String serial_id;

    /**
     * 任务类别
     */
    @NotBlank
    private String audit_type;

    /**
     * 前端节点
     */
    private String anode_id;

    /**
     * 业务类型
     */
    private String busin_type;

    /**
     * 证件类型
     */
    private String id_kind;

    /**
     * 整改原因分组
     */
    @NotBlank
    private String cause_group;


    private List<RepulsetReasonChildren> cause_children;

    /**
     * 顺序号
     */
    private Integer order_no;


}
