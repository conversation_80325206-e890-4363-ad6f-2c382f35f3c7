package com.cairh.cpe.backend.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cairh.cpe.backend.form.req.*;
import com.cairh.cpe.backend.form.resp.BidirectionalVideoChatLogInfo;
import com.cairh.cpe.backend.form.support.OnlineUserByEnRoleAndBranchSupportResponse;
import com.cairh.cpe.backend.form.support.OperatorAndBranchExtendInfo;
import com.cairh.cpe.backend.service.IAiAuditService;
import com.cairh.cpe.backend.service.IAuditCenterCommonService;
import com.cairh.cpe.backend.service.IAuditDetailHandleService;
import com.cairh.cpe.businflow.entity.CustModifyRecord;
import com.cairh.cpe.businflow.service.ICustModifyRecordService;
import com.cairh.cpe.cache.service.CacheBackendUser;
import com.cairh.cpe.cache.service.CacheBranch;
import com.cairh.cpe.cache.service.CacheDict;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.backend.util.BaseBeanUtil;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.constant.flow.FlowRecordEnum;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.dto.ProfessionReasonConfig;
import com.cairh.cpe.common.entity.BidirectionalVideoChatLog;
import com.cairh.cpe.common.entity.BusinFlowRecord;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.clob.*;
import com.cairh.cpe.common.entity.request.AuditChangeForm;
import com.cairh.cpe.common.entity.request.AuditForm;
import com.cairh.cpe.common.entity.response.BranchInfo;
import com.cairh.cpe.common.entity.support.BackendUser;
import com.cairh.cpe.common.service.*;
import com.cairh.cpe.common.util.*;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.db.config.IdGenerator;
import com.cairh.cpe.esb.base.rpc.IVBaseUserInfoDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseQryOnlineUserByEnRoleAndBranchRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseQryOnlineUserByEnRoleAndBranchResponse;
import com.cairh.cpe.esb.base.rpc.dto.resp.support.OperatorAndBranchInfo;
import com.cairh.cpe.service.aiaudit.response.AuditBusinRecordQueryResp;
import com.cairh.cpe.service.aiaudit.response.QueryAuditBusinRecordResp;
import com.cairh.cpe.service.aidispatch.service.IAiDispatchAchieveService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AuditDetailHandleServiceImpl implements IAuditDetailHandleService {

    @Autowired
    IUserQueryExtInfoService userQueryExtInfoService;
    @Autowired
    private IRequestService requestService;
    @Autowired
    private IRequestFlowService requestFlowService;
    @Autowired
    private IBusinFlowTaskService businFlowTaskService;
    @Autowired
    private IAiDispatchAchieveService aiDispatchAchieveService;
    @Autowired
    private ICustModifyRecordService custModifyRecordService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @DubboReference(check = false)
    private IVBaseUserInfoDubboService userInfoDubboService;
    @Autowired
    private IBusinFlowRequestService businFlowRequestService;
    @Autowired
    private IAuditCenterCommonService auditCenterCommonService;
    @Autowired
    private IAiAuditService aiAuditService;
    @Autowired
    private CacheBranch cacheBranch;
    @Autowired
    private CacheDict cacheDict;
    @Autowired
    private RedissonUtil redissonUtil;
    @Autowired
    private IBusinFlowParamsService businFlowParamsService;
    @Resource
    private CacheBackendUser cacheBackendUser;
    @Autowired
    private IHandupDetailsService handupDetailsService;
    @Autowired
    private IBusinProcessRequestAuditTrailService businProcessRequestAuditTrailService;
    @Autowired
    private ITaskReasonRecordService taskReasonRecordService;
    @Resource
    private IBusinFlowRecordService businFlowRecordService;
    @Autowired
    private IBidirectionalVideoChatLogService bidirectionalVideoChatLogService;
    @Resource
    private IdGenerator idGenerator;

    @Override
    public Result<String> dealAuditTask(BaseUser baseUser, DealAuditTaskForm dealAuditTask, BusinFlowTask businFlowTask) {
        //修改派单状态
        if (StringUtils.equals(businFlowTask.getPush_flag(), WskhConstant.ALREADY_PUSH_FLAG)
                && StringUtils.isNotBlank(businFlowTask.getDispatch_task_id())) {
            aiDispatchAchieveService.handleAuditDispatchTask(businFlowTask.getDispatch_task_id(), baseUser.getStaff_no());
        }
        AuditForm auditForm = new AuditForm();
        auditForm.setTask_id(businFlowTask.getSerial_id());
        auditForm.setOperator_no(baseUser.getStaff_no());
        auditForm.setOperator_name(baseUser.getUser_name());
        auditForm.setTask_status(FlowStatusConst.AUDIT_AUDITING);// 处理中
        auditForm.setAudit_remark("任务处理中");
        auditForm.setPush_flag(businFlowTask.getPush_flag());
        // 查询用户信息
        queryAndUpdateAuditForm(baseUser.getStaff_no(), auditForm);
        requestFlowService.dealAudit(auditForm);

        return Result.success();
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void auditPassTask(FinishAuditTaskForm finishAuditTaskForm, BusinFlowTask currentTask, BaseUser baseUser) {
        List<CustModifyRecord> custModifyRecords = checkCustModifyAndSave(finishAuditTaskForm, currentTask.getTask_type());
        log.info("判断是否需要往大字段中添加标识来判断流程节点 requestNo ={}", finishAuditTaskForm.getRequest_no());
        // 如果是见证 则直接跳过  如果有修改信息，则重新生成复审记录
        if (!StringUtils.equals(currentTask.getTask_type(), FlowNodeConst.AUDIT)) {
            checkReviewAndSaveFlag(currentTask, custModifyRecords);
        }
        log.info("本地更新任务状态 判断是否需要生成审核任务 requestNo ={}", finishAuditTaskForm.getRequest_no());
        AuditForm auditForm = new AuditForm();
        auditForm.setTask_id(currentTask.getSerial_id());
        auditForm.setOperator_no(baseUser.getStaff_no());
        auditForm.setTask_status(FlowStatusConst.AUDIT_PASS);
        auditForm.setAudit_remark(getAuditRemark(currentTask));

        if (StringUtils.isNotBlank(finishAuditTaskForm.getMust_pass_reason()) ||
                (null != finishAuditTaskForm.getAudit_warning_num() && finishAuditTaskForm.getAudit_warning_num() > 0)) {
            HashMap<String, Object> params = new HashMap<>();
            params.put(Fields.OPERATOR_NO, currentTask.getOperator_no());
            params.put(Fields.OPERATOR_NAME, currentTask.getOperator_name());
            params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22394);
            params.put(Fields.BUSINESS_REMARK, finishAuditTaskForm.getMust_pass_reason());
            params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_OPERATE_TYPE);
            businFlowRecordService.saveBusinFlowRecord(finishAuditTaskForm.getRequest_no(), params);
        }
        queryAndUpdateAuditForm(baseUser.getStaff_no(), auditForm);
        log.info("审核通过同步更新原始数据 requestNo ={}", finishAuditTaskForm.getRequest_no());
        requestFlowService.auditPass(auditForm, FlowRecordEnum.PASS);
        log.info("保存快照 requestNo ={}", finishAuditTaskForm.getRequest_no());
        businFlowParamsService.saveSnapshot(finishAuditTaskForm.getRequest_no(), "pass");
        userQueryExtInfoService.saveSnapshot(finishAuditTaskForm.getRequest_no(), "pass");
        log.info("通知智能派单任务已完成 requestNo ={}", finishAuditTaskForm.getRequest_no());
        aiDispatchAchieveService.finishDispatchTask(currentTask.getDispatch_task_id(),
                baseUser.getStaff_no());
    }


    private String getAuditRemark(BusinFlowTask currentTask) {
        String task_type = currentTask.getTask_type(), audit_remark = StrUtil.SPACE;
        if (StrUtil.equals(task_type, FlowNodeConst.AUDIT)) {
            audit_remark = "任务审核通过";
        } else if (StrUtil.equals(task_type, FlowNodeConst.REVIEW)) {
            audit_remark = "任务复核通过";
        } else if (StrUtil.equals(task_type, FlowNodeConst.SECONDARY_REVIEW)) {
            audit_remark = "任务二次复核通过";
        }

        return audit_remark;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BusinFlowTask auditNotPassTask(FinishAuditTaskForm finishAuditTaskForm, BaseUser baseUser) {
        BusinFlowRequest flowRequest = requestService.getByRequestNo(finishAuditTaskForm.getRequest_no());
        BusinFlowTask currTaskType = businFlowTaskService.getById(finishAuditTaskForm.getTask_id());
        log.info("[审核驳回]记录资料修改流水 requestNo ={}", finishAuditTaskForm.getRequest_no());
        checkCustModifyAndSave(finishAuditTaskForm, flowRequest.getAnode_id());
        AuditForm auditForm = new AuditForm();
        auditForm.setTask_id(currTaskType.getSerial_id());
        auditForm.setOperator_no(baseUser.getStaff_no());
        auditForm.setTask_status(FlowStatusConst.AUDIT_NO_PASS);
        auditForm.setAudit_remark("任务审核不通过");
        auditForm.setReasons(finishAuditTaskForm.getReasons());
        if (StringUtils.isNotBlank(finishAuditTaskForm.getAi_nopass_reason())) {
            log.info("[审核驳回]记录流水 除智能审核不通过的原因 requestNo ={}", finishAuditTaskForm.getRequest_no());
            Map<String, Object> params = new HashMap<>();
            params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_OPERATE_TYPE);
            params.put(Fields.OPERATOR_NO, currTaskType.getOperator_no());
            params.put(Fields.OPERATOR_NAME, currTaskType.getOperator_name());
            params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22396);
            params.put("ai_nopass_reason", finishAuditTaskForm.getAi_nopass_reason());
            params.put("ai_nopass_reason_other", finishAuditTaskForm.getAi_nopass_reason_other());
            String reason = StringUtils.isNotBlank(finishAuditTaskForm.getAi_nopass_reason_other()) ? finishAuditTaskForm.getAi_nopass_reason_other() : cacheDict.getDictDesc(DicConstant.AI_NOPASS_REASON, finishAuditTaskForm.getAi_nopass_reason());
            params.put(Fields.BUSINESS_REMARK, "删除智能审核的原因是：" + reason + "。删除智能审核的驳回项是：" + finishAuditTaskForm.getAi_nopass_reason_list());
            requestFlowService.submitParamsByOperator(currTaskType.getRequest_no(), params);
        }
        log.info("[审核驳回]审核不通过同步更新原始数据 requestNo ={}", finishAuditTaskForm.getRequest_no());
        requestFlowService.auditNoPass(auditForm);
        log.info("[审核驳回]BUSINFLOWPARAMS-USERQUERYEXTINFO快照 requestNo ={}", finishAuditTaskForm.getRequest_no());
        businFlowParamsService.saveSnapshot(finishAuditTaskForm.getRequest_no(), "notPass");
        userQueryExtInfoService.saveSnapshot(finishAuditTaskForm.getRequest_no(), "notPass");
        log.info("[审核驳回]通知智能派单任务已完成 requestNo ={}", finishAuditTaskForm.getRequest_no());
        if (!StringUtils.equals(currTaskType.getPush_flag(), WskhConstant.NEED_PUSH_FLAG)) {
            aiDispatchAchieveService.finishDispatchTask(currTaskType.getDispatch_task_id(), baseUser.getStaff_no());
        }
        // 驳回原因记录
        taskReasonRecordService.saveTaskReasonRecord(currTaskType, finishAuditTaskForm.getReasons(), baseUser);
        return currTaskType;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void suspendTask(SuspendTaskForm suspendTaskForm) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, suspendTaskForm.getRequest_no());
        boolean isLock = redissonUtil.tryLock(lockKey, 0, 10, TimeUnit.SECONDS);
        if (!isLock) {
            log.info("任务挂起未获取到锁，request_no={},serial_id={}", suspendTaskForm.getRequest_no());
            return;
        }
        try {
            BusinFlowTask currTaskType = businFlowTaskService.getById(suspendTaskForm.getTask_id());
            if (StringUtils.isBlank(currTaskType.getOperator_no()) && StringUtils.isBlank(currTaskType.getOperator_name())) {
                throw new BizException(ErrorEnum.RECYCLE_TASK_TIP.getValue(), ErrorEnum.RECYCLE_TASK_TIP.getDesc());
            }
            LambdaUpdateWrapper<BusinFlowTask> businFlowTaskLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            businFlowTaskLambdaUpdateWrapper
                    .set(BusinFlowTask::getRemark, suspendTaskForm.getSuspend_reason())
                    .set(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_SUSPEND)
                    .set(BusinFlowTask::getDeal_datetime, new Date())
                    .set(BusinFlowTask::getSuspend_remind_num, 0);
            businFlowTaskLambdaUpdateWrapper.eq(BusinFlowTask::getSerial_id, currTaskType.getSerial_id());
            businFlowTaskService.update(businFlowTaskLambdaUpdateWrapper);

            //挂起告知派单
            aiDispatchAchieveService.handUpNotice(currTaskType.getDispatch_task_id(), "1");

            //记录挂起操作和挂起原因
            Map<String, Object> params = new HashMap<>();
            params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_OPERATE_TYPE);
            params.put(Fields.OPERATOR_NO, currTaskType.getOperator_no());
            params.put(Fields.OPERATOR_NAME, currTaskType.getOperator_name());
            params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22395);
            params.put(Fields.BUSINESS_REMARK, cacheDict.getDictDesc(DicConstant.SUSPEND_REASON, suspendTaskForm.getSuspend_reason()));
            businFlowRecordService.saveBusinFlowRecord(currTaskType.getRequest_no(), params);

            // 挂起成功记录流水表
            handupDetailsService.saveHandupDetails(currTaskType, HandupTypeEnum.SUSPEND);
            // 挂起成功记录任务审核流程表
            businProcessRequestAuditTrailService.updateBusinProcessRequestAuditTrail(currTaskType, null, UpdateBusinProcessRequestAuditTrailSourceEnum.SOURCE_TASK_SUSPEND);
        } catch (Exception e) {
            log.error("任务挂起发生异常信息 request_no={},serial_id={}", suspendTaskForm.getRequest_no(), suspendTaskForm.getTask_id(), e);
            throw new BizException(ErrorEnum.AUDIT_TASK_SUSPEND_ERROR.getValue(), e.getMessage());
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }

    public List<CustModifyRecord> checkCustModifyAndSave(FinishAuditTaskForm finishAuditTaskForm, String anode_id) {
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(finishAuditTaskForm.getRequest_no());
        AuditChangeForm auditChangeForm = new AuditChangeForm();
        auditChangeForm.setFlow_task_id(finishAuditTaskForm.getTask_id());
        // 网厅业务办理
        if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, clobContentInfo.getBusin_type())) {
            UserIndependenceInfo independenceInfo = new UserIndependenceInfo();
            if (!StringUtil.equals(clobContentInfo.getOriginal_file_80(), clobContentInfo.getFile_80())) {
                independenceInfo.setFile_80(clobContentInfo.getFile_80());
            }
            // 身份证正反面切换保存
            if (StringUtils.equals(clobContentInfo.getClient_category(), ClientCategoryEnum.CLIENT_PERSON.getCode())) {
                if (!StringUtil.equals(clobContentInfo.getOriginal_photo_front(), clobContentInfo.getPhoto_front())
                        || !StringUtil.equals(clobContentInfo.getOriginal_photo_back(), clobContentInfo.getPhoto_back())) {
                    independenceInfo.setFile_6A(clobContentInfo.getPhoto_front());
                    independenceInfo.setFile_6B(clobContentInfo.getPhoto_back());
                }
            } else if (StringUtils.equalsAny(clobContentInfo.getClient_category(), ClientCategoryEnum.CLIENT_INSTITUTION.getCode(),
                    ClientCategoryEnum.CLIENT_PRODUCT.getCode())) {
                if (!StringUtil.equals(clobContentInfo.getOriginal_agent_photo_front(), clobContentInfo.getAgent_photo_front())
                        || !StringUtil.equals(clobContentInfo.getOriginal_agent_photo_back(), clobContentInfo.getAgent_photo_back())) {
                    independenceInfo.setFile_6A(clobContentInfo.getAgent_photo_front());
                    independenceInfo.setFile_6B(clobContentInfo.getAgent_photo_back());
                }
            }
            auditChangeForm.setIndependenceInfo(independenceInfo);
            // 填充基本信息
            UserBaseInfo userBaseInfo = new UserBaseInfo();
            userBaseInfo.setDishonest_record(clobContentInfo.getDishonest_record());
            userBaseInfo.setDishonest_record_remark(clobContentInfo.getDishonest_record_remark());
            auditChangeForm.setUser_base_info(userBaseInfo);
        } else {
            auditChangeForm.setChoose_branch_reason_content(clobContentInfo.getChoose_branch_reason_content());
            auditChangeForm.setChoose_profession_reason_content(clobContentInfo.getChoose_profession_reason_content());
            auditChangeForm.setTranslation_address(clobContentInfo.getTranslation_address());
            auditChangeForm.setAlternative_address(clobContentInfo.getAlternative_address());
            // 填充身份证正面照，身份证反面照和用户大头照
            UserIndependenceInfo independenceInfo = new UserIndependenceInfo();
            if (!StringUtil.equals(clobContentInfo.getFile_6A(), clobContentInfo.getOriginal_file_6A()) || !StringUtil.equals(clobContentInfo.getFile_6B(), clobContentInfo.getOriginal_file_6B())) {
                independenceInfo.setFile_6A(clobContentInfo.getFile_6A());
                independenceInfo.setFile_6B(clobContentInfo.getFile_6B());
            }
            if (!StringUtil.equals(clobContentInfo.getFile_80(), clobContentInfo.getOriginal_file_80())) {
                independenceInfo.setFile_80(clobContentInfo.getFile_80());
            }
            if (!StringUtil.equals(clobContentInfo.getFile_7C(), clobContentInfo.getOriginal_file_7C()) || !StringUtil.equals(clobContentInfo.getFile_7D(), clobContentInfo.getOriginal_file_7D())) {
                independenceInfo.setFile_7C(clobContentInfo.getFile_7C());
                independenceInfo.setFile_7D(clobContentInfo.getFile_7D());
            }
            if (!StringUtil.equals(clobContentInfo.getFile_7X(), clobContentInfo.getOriginal_file_7X())) {
                independenceInfo.setFile_7X(clobContentInfo.getFile_7X());
            }
            if (!StringUtil.equals(clobContentInfo.getFile_7W(), clobContentInfo.getOriginal_file_7W())) {
                independenceInfo.setFile_7W(clobContentInfo.getFile_7W());
            }
            if (!StringUtil.equals(clobContentInfo.getFile_Bm(), clobContentInfo.getOriginal_file_Bm())) {
                independenceInfo.setFile_Bm(clobContentInfo.getFile_Bm());
            }

            auditChangeForm.setIndependenceInfo(independenceInfo);
            // 填充身份证信息
            IDCardInfo idCardInfo = new IDCardInfo();
            idCardInfo.setId_begindate(clobContentInfo.getId_card_info().getId_begindate());
            idCardInfo.setId_enddate(clobContentInfo.getId_card_info().getId_enddate());
            idCardInfo.setId_address(clobContentInfo.getId_card_info().getId_address());
            idCardInfo.setPrev_id_number(clobContentInfo.getId_card_info().getPrev_id_number());
            idCardInfo.setNationality(clobContentInfo.getId_card_info().getNationality());
            idCardInfo.setEnglish_name(clobContentInfo.getId_card_info().getEnglish_name());
            idCardInfo.setAuxiliary_id_address(clobContentInfo.getId_card_info().getAuxiliary_id_address());
            idCardInfo.setAuxiliary_id_begindate(clobContentInfo.getId_card_info().getAuxiliary_id_begindate());
            idCardInfo.setAuxiliary_id_enddate(clobContentInfo.getId_card_info().getAuxiliary_id_enddate());
            idCardInfo.setBirthday(clobContentInfo.getId_card_info().getBirthday());
            auditChangeForm.setId_card_info(idCardInfo);
            // 填充基本信息
            UserBaseInfo userBaseInfo = new UserBaseInfo();
            userBaseInfo.setAddress(clobContentInfo.getUser_base_info().getAddress());
            userBaseInfo.setProfession_code(clobContentInfo.getUser_base_info().getProfession_code());
            userBaseInfo.setWork_unit(clobContentInfo.getUser_base_info().getWork_unit());
            userBaseInfo.setChoose_branch_reason(clobContentInfo.getUser_base_info().getChoose_branch_reason());
            userBaseInfo.setChoose_profession_reason(clobContentInfo.getUser_base_info().getChoose_profession_reason());
            userBaseInfo.setDishonest_record(clobContentInfo.getUser_base_info().getDishonest_record());
            userBaseInfo.setDishonest_record_remark(clobContentInfo.getUser_base_info().getDishonest_record_remark());
            userBaseInfo.setProfession_other(clobContentInfo.getUser_base_info().getProfession_other());
            auditChangeForm.setUser_base_info(userBaseInfo);

            // 跨境理财通业务
            if (StringUtils.equals(clobContentInfo.getBusin_type(), WskhConstant.CROSS_BUSIN_TYPE)) {
                UserRpcInfo userRpcInfo = new UserRpcInfo();
                userRpcInfo.setRpc_file_id(clobContentInfo.getRpc_file_id());
                userRpcInfo.setRpc_remark(clobContentInfo.getRpc_remark());
                userRpcInfo.setRpc_option(clobContentInfo.getRpc_option());
                auditChangeForm.setUser_rpc_info(userRpcInfo);
            }
        }
        auditChangeForm.setModify_link(anode_id);
        auditChangeForm.setRequest_no(finishAuditTaskForm.getRequest_no());
        // 保存客户资料修改流水
        auditChangeForm.setSaveCustmodifyRecordFlag("1");
        return custModifyRecordService.saveParamsAndRecord(auditChangeForm);

    }

    public void checkReviewAndSaveFlag(BusinFlowTask currTaskType, List<CustModifyRecord> custModifyRecordList) {
        log.info("审核通过时，与原始数据不同的字段有：{}", JSON.toJSONString(custModifyRecordList));
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(currTaskType.getRequest_no());
        boolean continue_flag = false;
        // 核实备注 以及 免冠照变更不需要判断
        List<CustModifyRecord> list = custModifyRecordList
                .stream()
                .filter(cust -> !StringUtils.equals(cust.getModify_item(), "profession_other") && !StringUtils.equals(cust.getModify_item(), "file_80"))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(list)) {
            continue_flag = true;
        } else {
            //判断是否存在免冠照的更改。再判断最后一次免冠照更改是截屏还是上传
            List<CustModifyRecord> file_80_list = custModifyRecordList
                    .stream()
                    .filter(cust -> StringUtils.equals(cust.getModify_item(), "file_80"))
                    .collect(Collectors.toList());
            //存在免冠照的更改
            if (CollectionUtil.isNotEmpty(file_80_list)) {
                LambdaQueryWrapper<BusinFlowRecord> recordWrapper = new LambdaQueryWrapper<>();
                recordWrapper.eq(BusinFlowRecord::getRequest_no, currTaskType.getRequest_no())
                        .eq(BusinFlowRecord::getBusiness_flag, FlowRecordEnum.B1017.getValue())
                        .orderByDesc(BusinFlowRecord::getCreate_datetime);
                List<BusinFlowRecord> records = businFlowRecordService.list(recordWrapper);
                for (BusinFlowRecord record : records) {//最后一次的记录是上传还是截图
                    if (StringUtils.contains(record.getBusi_content(), "\"upload_80_type\":\"upload\"")) {//上传
                        continue_flag = true;
                        break;
                    } else if (StringUtils.contains(record.getBusi_content(), "\"upload_80_type\":\"screenshot\"")) {//截图
                        break;
                    }
                }
            }
        }
        HashMap<String, Object> params = new HashMap<>();
        params.put(WskhFields.ACTIVITI_CONTINUE_REVIEW_DOUBLE_FLAG, "0");
        params.put(WskhFields.ACTIVITI_CONTINUE_REVIEW_FLAG, "0");
        params.put(WskhFields.ACTIVITI_CONTINUE_DOUBLE_FLAG, "0");
        params.put(WskhFields.ACTIVITI_END_FLAG, "0");
        String scores = PropertySource.get(PropKeyConstant.WSKH_ZD_SCORE_CONFIDENCE_RANGE, "60,75");
        String[] scoreArr = scores.split(",");
        int minScore = Integer.parseInt(scoreArr[0]);
        int maxScore = Integer.parseInt(scoreArr[1]);
        if (continue_flag) {
            //存在修改情况就进行二次复核
            if (StringUtils.equals(currTaskType.getTask_type(), FlowNodeConst.REVIEW)) {
                //生成新的复核任务
                params.put(WskhFields.ACTIVITI_CONTINUE_REVIEW_FLAG, "1");
                params.put(Fields.BUSINESS_REMARK, "重新进行复核");
            } else if (StringUtils.equals(currTaskType.getTask_type(), FlowNodeConst.SECONDARY_REVIEW)) {
                //生成新的二次复核任务
                params.put(WskhFields.ACTIVITI_CONTINUE_DOUBLE_FLAG, "1");
                params.put(Fields.BUSINESS_REMARK, "重新进行二次复核");
            }
        } else if (StringUtils.isNotBlank(clobContentInfo.getFile_82())) {
            int face_score_82_80 = StringUtils.isNotBlank(clobContentInfo.getFace_score_82_80()) ? Double.valueOf(clobContentInfo.getFace_score_82_80()).intValue() : 0;
            if (face_score_82_80 >= minScore && face_score_82_80 < maxScore && !StringUtils.equals(currTaskType.getTask_type(), FlowNodeConst.SECONDARY_REVIEW)) {//只有复核才存在小于75分的情况
                //生成二次复核任务
                params.put(WskhFields.ACTIVITI_CONTINUE_REVIEW_DOUBLE_FLAG, "1");
                params.put(Fields.BUSINESS_REMARK, "进入二次复核");
            } else {
                //结束
                params.put(WskhFields.ACTIVITI_END_FLAG, "1");
                params.put(Fields.BUSINESS_REMARK, "结束");
            }
        } else {
            //结束
            params.put(WskhFields.ACTIVITI_END_FLAG, "1");
            params.put(Fields.BUSINESS_REMARK, "结束");
        }
        params.put(Fields.RECORD_TYPE, FlowStatusConst.SYSTEM_RECORD_TYPE);// 系统自动办理流水

        requestFlowService.saveParamsRecord(currTaskType.getRequest_no(), params);
    }

    @Override
    public OnlineUserByEnRoleAndBranchSupportResponse transferBranchTree(TransferBranchTreeForm transferBranchTreeForm) {
        BusinFlowTask businFlowTask = businFlowTaskService.getById(transferBranchTreeForm.getTask_id());
        Assert.notNull(businFlowTask, String.format("业务编号[%s]对应的[%s]任务不存在,无法获取人员机构树", transferBranchTreeForm.getRequest_no(), transferBranchTreeForm.getTask_type()));

        BusinFlowRequest businFlowRequest = requestService.getByRequestNo(businFlowTask.getRequest_no());

        VBaseQryOnlineUserByEnRoleAndBranchRequest onlineUserByEnRoleAndBranchRequest = new VBaseQryOnlineUserByEnRoleAndBranchRequest();
        onlineUserByEnRoleAndBranchRequest.setRole(TaskTypeRoleTranslator.translate(businFlowTask.getTask_type()));
        onlineUserByEnRoleAndBranchRequest.setBranch_no(businFlowRequest.getBranch_no());
        VBaseQryOnlineUserByEnRoleAndBranchResponse onlineUserByEnRoleAndBranchResponse = userInfoDubboService.qryOnlineUserInfoAndBranchInfo(onlineUserByEnRoleAndBranchRequest);

        OnlineUserByEnRoleAndBranchSupportResponse onlineUserByEnRoleAndBranchSupportResponse = new OnlineUserByEnRoleAndBranchSupportResponse();
        onlineUserByEnRoleAndBranchSupportResponse.setBaseAllBranchQryResponses(onlineUserByEnRoleAndBranchResponse.getBaseAllBranchQryResponses());
        List<OperatorAndBranchInfo> operatorAndBranchInfos = onlineUserByEnRoleAndBranchResponse.getOperatorAndBranchInfoList();
        if (CollectionUtil.isNotEmpty(operatorAndBranchInfos)) {
            List<OperatorAndBranchExtendInfo> operatorAndBranchExtendInfos = operatorAndBranchInfos.stream().map(operatorAndBranchInfo -> {
                OperatorAndBranchExtendInfo operatorAndBranchExtendInfo = new OperatorAndBranchExtendInfo();
                BaseBeanUtil.copyProperties(operatorAndBranchInfo, operatorAndBranchExtendInfo);

                Integer unhandleNum = businFlowTaskService.lambdaQuery()
                        .eq(BusinFlowTask::getOperator_no, operatorAndBranchInfo.getOperator_no())
                        .eq(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_PENDING).count();
                operatorAndBranchExtendInfo.setUnhandle_num(unhandleNum);

                return operatorAndBranchExtendInfo;
            }).collect(Collectors.toList());
            //过滤不允许操作员
            operatorAndBranchExtendInfos = operatorAndBranchExtendInfos.stream().filter(operatorAndBranchInfo -> !StringUtils.contains(businFlowTask.getNot_allow_auditor(), operatorAndBranchInfo.getOperator_no())).collect(Collectors.toList());

            onlineUserByEnRoleAndBranchSupportResponse.setOperatorAndBranchInfoExtendList(operatorAndBranchExtendInfos);
        }

        return onlineUserByEnRoleAndBranchSupportResponse;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void transferTask(TransferTaskForm transferTaskForm) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, transferTaskForm.getRequest_no());
        boolean isLock = redissonUtil.tryLock(lockKey, 0, 10, TimeUnit.SECONDS);
        if (!isLock) {
            log.info("任务转交未获取到锁，request_no={}", transferTaskForm.getRequest_no());
            return;
        }
        try {

            BusinFlowTask businFlowTask = businFlowTaskService.getById(transferTaskForm.getFlow_task_id());
            if (StringUtils.isBlank(businFlowTask.getOperator_no()) && StringUtils.isBlank(businFlowTask.getOperator_name())) {
                throw new BizException(ErrorEnum.RECYCLE_TASK_TIP.getValue(), ErrorEnum.RECYCLE_TASK_TIP.getDesc());
            }
            Assert.notNull(businFlowTask, String.format("业务编号[%s]对应的[%s]任务不存在,无法进行转交", transferTaskForm.getRequest_no(), transferTaskForm.getTask_type()));

            if (StrUtil.equals(transferTaskForm.getOperator_no(), transferTaskForm.getTransfer_operator_no())) {
                log.error("业务编号{}对应的{}任务不允许转交给自身", transferTaskForm.getRequest_no(), transferTaskForm.getTask_type());
                throw new BizException(ErrorEnum.AUDIT_TASK_TRANSFER_ERROR.getValue(), ErrorEnum.AUDIT_TASK_TRANSFER_ERROR.getDesc());
            }

            if (StringUtils.isBlank(businFlowTask.getOperator_no())) {
                log.error("任务{}当前操作人{}为空，不可转交", transferTaskForm.getRequest_no(), transferTaskForm.getOperator_no());
                throw new BizException(ErrorEnum.AUDIT_TASK_TRANSFER_NO_OPERATOR_ERROR.getValue(), ErrorEnum.AUDIT_TASK_TRANSFER_NO_OPERATOR_ERROR.getDesc());
            }

            if (StringUtils.equals(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_TRANSFER)) {
                log.error("任务{}已经被转交,状态为{}不可转交", transferTaskForm.getRequest_no(), businFlowTask.getTask_status());
                return;
            }

            // 双向视频见证任务状态为待审核才可转交
            if (StringUtils.equals(businFlowTask.getVideo_type(), WskhConstant.VIDEO_TYPE_2)
                    && StringUtils.equals(businFlowTask.getTask_type(), TaskTypeEnum.AUDIT.getCode())
                    && !StringUtils.equals(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_PENDING)) {
                if (StringUtils.equals(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_INVALIDATE)) {
                    throw new BizException(ErrorEnum.BIDIRECTIONAL_AUDIT_INVALIDATE_TASK.getValue(), ErrorEnum.BIDIRECTIONAL_AUDIT_INVALIDATE_TASK.getDesc());
                }
                log.error("当前任务{}状态{}不可转交", businFlowTask.getRequest_no(), businFlowTask.getTask_status());
                throw new BizException(ErrorEnum.AUDIT_TASK_TRANSFER_STATUS_ERROR.getValue(), ErrorEnum.AUDIT_TASK_TRANSFER_STATUS_ERROR.getDesc());
            }

            if (StringUtils.isBlank(businFlowTask.getOperator_no()) && StringUtils.isBlank(businFlowTask.getOperator_name())) {
                throw new BizException(ErrorEnum.RECYCLE_TASK_TIP.getValue(), ErrorEnum.RECYCLE_TASK_TIP.getDesc());
            }
            LambdaUpdateWrapper<BusinFlowTask> businFlowTaskLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            businFlowTaskLambdaUpdateWrapper
                    .set(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_TRANSFER);
            businFlowTaskLambdaUpdateWrapper.eq(BusinFlowTask::getSerial_id, businFlowTask.getSerial_id());
            businFlowTaskService.update(businFlowTaskLambdaUpdateWrapper);


            TransferTaskMessage transferTaskMessage = new TransferTaskMessage();
            transferTaskMessage.setFlow_task_id(transferTaskForm.getFlow_task_id());
            transferTaskMessage.setRequest_no(transferTaskForm.getRequest_no());
            transferTaskMessage.setOperator_no(transferTaskForm.getTransfer_operator_no());
            transferTaskMessage.setTransfer_operator_no(transferTaskForm.getOperator_no());
            transferTaskMessage.setTransfer_operator_name(transferTaskForm.getOperator_name());
            transferTaskMessage.setTask_type(transferTaskForm.getTask_type());
            transferTaskMessage.setMessage_content(transferTaskForm.getTransfer_reason());
            transferTaskMessage.setTransfer_first_duration(PropertySource.getInt(PropKeyConstant.TASK_TRANSFER_FIRST_DURATION, 60));
            transferTaskMessage.setTransfer_second_duration(PropertySource.getInt(PropKeyConstant.TASK_TRANSFER_SECOND_DURATION, 30));

            redisTemplate.convertAndSend(Constant.CHANNEL_TRANSFER_TASK, JSON.toJSONString(transferTaskMessage));

            HashMap<String, Object> params = new HashMap<>(8);
            params.put(WskhFields.TASK_TRANSFER_SPONSOR_TIME, KHDateUtil.formatDate(new Date(), KHDateUtil.DATE_TIME_FORMAT));
            params.put(Fields.OPERATOR_NO, businFlowTask.getOperator_no());
            params.put(Fields.OPERATOR_NAME, businFlowTask.getOperator_name());
            params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22389);
            params.put(Fields.BUSINESS_REMARK, String.format("向操作员%s的任务移交已发起,原因是：%s", transferTaskForm.getTransfer_operator_no(), transferTaskForm.getTransfer_reason()));
            params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_OPERATE_TYPE);
            requestFlowService.saveParamsRecord(transferTaskForm.getRequest_no(), params);

            String key = String.format(RedisKeyConstant.WSKH_AC_TASK_TRANSFER_TIME, transferTaskForm.getRequest_no());
            // 记录当前转交时间，存放到redis，过期时间10分钟
            redisTemplate.opsForValue().set(key, KHDateUtil.formatDate(new Date(), KHDateUtil.DATE_TIME_FORMAT));
            redisTemplate.expire(key, 10, TimeUnit.MINUTES);

            // 转交记录任务审核流程表
            businProcessRequestAuditTrailService.updateBusinProcessRequestAuditTrail(businFlowTask, null,
                    UpdateBusinProcessRequestAuditTrailSourceEnum.SOURCE_TASK_TRANSFER);
        } catch (Exception e) {
            log.error("任务转交发生异常信息 request_no={}", transferTaskForm.getRequest_no(), e);
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void uploadAuditParam(AuditVideoParmRequest videoParmRequest) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, videoParmRequest.getRequest_no());
        boolean isLock = redissonUtil.tryLock(lockKey, 0, 10, TimeUnit.SECONDS);
        if (!isLock) {
            log.info("视频录制上传未获取到锁，request_no={}", videoParmRequest.getRequest_no());
            return;
        }
        try {
            HashMap<String, Object> params = new HashMap<>(8);
            params.put(WskhFields.TASK_TRANSFER_ANSWER_TIME, KHDateUtil.formatDate(new Date(), KHDateUtil.DATE_TIME_FORMAT));
            params.put("file_8A", videoParmRequest.getFile_8A());
            params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22399);
            params.put(Fields.BUSINESS_REMARK, "双向视频上传提交");
            params.put(Fields.VIDEO_TYPE, videoParmRequest.getVideo_type());
            requestFlowService.saveParamsRecord(videoParmRequest.getRequest_no(), params);
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void transferAccept(TransferAcceptForm transferAcceptForm) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, transferAcceptForm.getRequest_no());
        //转交接收
        boolean isLock = redissonUtil.tryLock(lockKey, 2, 10, TimeUnit.SECONDS);
        if (!isLock) {
            log.error("转交接受未获得锁，request_no={}", transferAcceptForm.getRequest_no());
            throw new BizException(ErrorEnum.AUDIT_TASK_TRANSFER_ACCEPT_ERROR.getValue(),
                    String.format(ErrorEnum.AUDIT_TASK_TRANSFER_ACCEPT_ERROR.getDesc(), transferAcceptForm.getRequest_no(), transferAcceptForm.getTask_type()));
        }
        try {
            BusinFlowTask businFlowTask = businFlowTaskService.getById(transferAcceptForm.getFlow_task_id());
            Assert.notNull(businFlowTask, String.format("业务编号[%s]对应的[%s]任务不存在,无法接受此转交", transferAcceptForm.getRequest_no(), transferAcceptForm.getTask_type()));

            if (!StrUtil.equals(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_TRANSFER)) {
                log.error("业务编号[{}]对应的[{}]任务不为转交状态,不允许接受此次转交", transferAcceptForm.getRequest_no(), transferAcceptForm.getTask_type());
                throw new BizException(ErrorEnum.AUDIT_TASK_TRANSFER_ACCEPT_STATUS_ERROR.getValue(),
                        String.format(ErrorEnum.AUDIT_TASK_TRANSFER_ACCEPT_STATUS_ERROR.getDesc(), transferAcceptForm.getRequest_no(), transferAcceptForm.getTask_type()));
            }

            if (!StrUtil.equals(businFlowTask.getOperator_no(), transferAcceptForm.getTransfer_operator_no())) {
                log.error("该任务已被认领，请勿重复认领");
                throw new BizException(ErrorEnum.AUDIT_TASK_TRANSFER_ACCEPT_CLAIM_ERROR.getValue(), ErrorEnum.AUDIT_TASK_TRANSFER_ACCEPT_CLAIM_ERROR.getDesc());
            }
            if (!StringUtils.equals(businFlowTask.getPush_flag(), WskhConstant.ALREADY_PUSH_FLAG)) {
                log.error("该任务还没有推送派单，请勿操作");
                throw new BizException(ErrorEnum.AUDIT_TASK_TRANSFER_ACCEPT_NO_PAUSE_ERROR.getValue(), ErrorEnum.AUDIT_TASK_TRANSFER_ACCEPT_NO_PAUSE_ERROR.getDesc());
            }
            if (StrUtil.equals(businFlowTask.getOperator_no(), transferAcceptForm.getOperator_no())) {
                log.error("业务编号[{}]对应的[{}]任务不允许转交给自身", transferAcceptForm.getRequest_no(), transferAcceptForm.getTask_type());
                throw new BizException(ErrorEnum.AUDIT_TASK_TRANSFER_ACCEPT_ONESELF_ERROR.getValue(), ErrorEnum.AUDIT_TASK_TRANSFER_ACCEPT_ONESELF_ERROR.getDesc());
            }

            // 双向视频见证任务状态为作废状态
            if (StringUtils.equals(businFlowTask.getTask_type(), TaskTypeEnum.AUDIT.getCode())
                    && StringUtils.equals(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_INVALIDATE)) {
                throw new BizException(ErrorEnum.BIDIRECTIONAL_AUDIT_INVALIDATE_TASK.getValue(), ErrorEnum.BIDIRECTIONAL_AUDIT_INVALIDATE_TASK.getDesc());
            }
            //通知智能派单，任务被转派
            aiDispatchAchieveService.taskChangeOperator(businFlowTask.getDispatch_task_id(), transferAcceptForm.getTransfer_operator_no(), transferAcceptForm.getOperator_no());
            //更新数据
            Date date = new Date();
            LambdaUpdateWrapper<BusinFlowTask> businFlowTaskLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            businFlowTaskLambdaUpdateWrapper
                    .set(BusinFlowTask::getOperator_no, transferAcceptForm.getOperator_no())
                    .set(BusinFlowTask::getOperator_name, transferAcceptForm.getOperator_name())
                    .set(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_PENDING)
                    .set(BusinFlowTask::getDeal_datetime, null)
                    .set(BusinFlowTask::getSuspend_remind_num, 0)
                    .set(BusinFlowTask::getTask_source, DicConstant.TASK_SOURCE_3);
            businFlowTaskLambdaUpdateWrapper.eq(BusinFlowTask::getSerial_id, businFlowTask.getSerial_id());
            businFlowTaskService.update(businFlowTaskLambdaUpdateWrapper);

            userQueryExtInfoService.updateOperatorInfo(transferAcceptForm.getRequest_no(), transferAcceptForm.getOperator_no()
                    , transferAcceptForm.getOperator_name(), null, businFlowTask.getTask_type());

            LambdaUpdateWrapper<BusinFlowRequest> businFlowRequestLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            businFlowRequestLambdaUpdateWrapper.set(BusinFlowRequest::getOperator_no, transferAcceptForm.getOperator_no())
                    .set(BusinFlowRequest::getRequest_status, FlowStatusConst.REQUEST_STATUS_PENDING_AUDIT)
                    .eq(BusinFlowRequest::getRequest_no, businFlowTask.getRequest_no());
            businFlowRequestService.update(businFlowRequestLambdaUpdateWrapper);


            HashMap<String, Object> params = new HashMap<>(8);
            params.put(WskhFields.TASK_TRANSFER_ANSWER_TIME, KHDateUtil.formatDate(new Date(), KHDateUtil.DATE_TIME_FORMAT));
            params.put(Fields.OPERATOR_NO, transferAcceptForm.getOperator_no());
            params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22390);
            params.put(Fields.BUSINESS_REMARK, String.format("来自操作员%s的任务移交已接受", transferAcceptForm.getTransfer_operator_no()));
            params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_OPERATE_TYPE);

            businFlowRecordService.saveBusinFlowRecord(transferAcceptForm.getRequest_no(), params);
            // 转交接受成功记录流水表
            handupDetailsService.saveHandupDetails(businFlowTask, HandupTypeEnum.TRANSFER);
            // 转交接受记录任务审核流程表
            businFlowTask.setOperator_no(transferAcceptForm.getOperator_no());
            businFlowTask.setOperator_name(transferAcceptForm.getOperator_name());
            // 获取转交用户信息
            AuditForm auditForm = new AuditForm();
            auditForm.setOperator_no(transferAcceptForm.getOperator_no());
            auditForm.setOperator_name(transferAcceptForm.getOperator_name());
            queryAndUpdateAuditForm(transferAcceptForm.getOperator_no(), auditForm);
            Map<String, Object> userParams = paddingMap(auditForm);
            // 更新任务跟踪表
            businFlowTask.setTask_source(DicConstant.TASK_SOURCE_3);
            businProcessRequestAuditTrailService.updateBusinProcessRequestAuditTrail(businFlowTask, userParams,
                    UpdateBusinProcessRequestAuditTrailSourceEnum.SOURCE_TASK_TRANSFERACCEPT);
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void transferRefuse(TransferRefuseForm transferRefuseForm) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, transferRefuseForm.getRequest_no());
        boolean isLock = redissonUtil.tryLock(lockKey, 0, 10, TimeUnit.SECONDS);
        if (!isLock) {
            log.info("转交拒绝未获取到锁，request_no={},serial_id={}", transferRefuseForm.getRequest_no(), transferRefuseForm.getFlow_task_id());
            return;
        }
        try {
            BusinFlowTask businFlowTask = businFlowTaskService.getById(transferRefuseForm.getFlow_task_id());
            Assert.notNull(businFlowTask, String.format("业务编号[%s]对应的[%s]任务不存在,无法拒绝此转交", transferRefuseForm.getRequest_no(), transferRefuseForm.getTask_type()));

            if (!StrUtil.equals(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_TRANSFER)) {
                log.error("业务编号[{}]对应的[{}]任务不为转交状态,不允许拒绝此次转交", transferRefuseForm.getRequest_no(), transferRefuseForm.getTask_type());
                throw new BizException(ErrorEnum.AUDIT_TASK_TRANSFER_REFUSE_ONESELF_ERROR.getValue(),
                        String.format(ErrorEnum.AUDIT_TASK_TRANSFER_REFUSE_ONESELF_ERROR.getDesc(), transferRefuseForm.getRequest_no(), transferRefuseForm.getTask_type()));
            }

            if (!StrUtil.equals(businFlowTask.getOperator_no(), transferRefuseForm.getTransfer_operator_no())) {
                log.error("该任务已被认领，请勿重复认领");
                throw new BizException(ErrorEnum.AUDIT_TASK_TRANSFER_ACCEPT_CLAIM_ERROR.getValue(), ErrorEnum.AUDIT_TASK_TRANSFER_ACCEPT_CLAIM_ERROR.getDesc());
            }

            if (StrUtil.equals(businFlowTask.getOperator_no(), transferRefuseForm.getOperator_no())) {
                log.error("业务编号[{}]对应的[{}]任务不允许转交给自身", transferRefuseForm.getRequest_no(), transferRefuseForm.getTask_type());
                throw new BizException(ErrorEnum.AUDIT_TASK_TRANSFER_ACCEPT_ONESELF_ERROR.getValue(), ErrorEnum.AUDIT_TASK_TRANSFER_ACCEPT_ONESELF_ERROR.getDesc());
            }

            // 双向视频见证任务状态为作废状态
            if (StringUtils.equals(businFlowTask.getTask_type(), TaskTypeEnum.AUDIT.getCode())
                    && StringUtils.equals(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_INVALIDATE)) {
                throw new BizException(ErrorEnum.BIDIRECTIONAL_AUDIT_INVALIDATE_TASK.getValue(), ErrorEnum.BIDIRECTIONAL_AUDIT_INVALIDATE_TASK.getDesc());
            }

            LambdaUpdateWrapper<BusinFlowTask> businFlowTaskLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            businFlowTaskLambdaUpdateWrapper
                    .set(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_PENDING);
            businFlowTaskLambdaUpdateWrapper.eq(BusinFlowTask::getSerial_id, businFlowTask.getSerial_id());
            businFlowTaskService.update(businFlowTaskLambdaUpdateWrapper);

            HashMap<String, Object> params = new HashMap<>(8);
            params.put(WskhFields.TASK_TRANSFER_ANSWER_TIME, KHDateUtil.formatDate(new Date(), KHDateUtil.DATE_TIME_FORMAT));
            params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22391);
            params.put(Fields.BUSINESS_REMARK, String.format("来自操作员%s的任务移交已拒绝", transferRefuseForm.getTransfer_operator_no()));
            params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_OPERATE_TYPE);
            businFlowRecordService.saveBusinFlowRecord(transferRefuseForm.getRequest_no(), params);
        } catch (Exception e) {
            log.error("转交拒绝发生异常信息 request_no={},serial_id={}", transferRefuseForm.getRequest_no(), transferRefuseForm.getFlow_task_id(), e);
            throw new BizException(ErrorEnum.AUDIT_TASK_TRANSFER_REFUSE_ERROR.getValue(), e.getMessage());
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }

    @Override
    public String checkNotBlank(FinishAuditTaskForm finishAuditTaskForm, ClobContentInfo clobContentInfo) {
        String common_fail_message = "";
        //身份证反面
        // 网厅业务办理-个人户-身份证或机构户，校验身份证反面是否为空
        if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, clobContentInfo.getBusin_type())) {
            // 个人
            boolean isClientPerson = StringUtils.equals(clobContentInfo.getClient_category(), ClientCategoryEnum.CLIENT_PERSON.getCode());

            // 获取需要验证的身份证类型和照片信息
            String idKind = isClientPerson ? clobContentInfo.getId_kind() : clobContentInfo.getAgent_id_kind();
            String photoFront = isClientPerson ? clobContentInfo.getPhoto_front() : clobContentInfo.getAgent_photo_front();
            String photoBack = isClientPerson ? clobContentInfo.getPhoto_back() : clobContentInfo.getAgent_photo_back();

            // 验证身份证照片
            if (StringUtils.equals(IdKindEnum.ID_CARD.getCode(), idKind)) {
                if (StringUtils.isBlank(photoFront)) {
                    common_fail_message += "身份证正面不能为空，";
                }
                if (StringUtils.isBlank(photoBack)) {
                    common_fail_message += "身份证反面不能为空，";
                }
            }
        } else {
            //身份证正面
            if (StringUtils.isBlank(clobContentInfo.getFile_6A())) {
                common_fail_message += "身份证正面不能为空，";
            }
            //身份证反面
            if (StringUtils.isBlank(clobContentInfo.getFile_6B())) {
                common_fail_message += "身份证反面不能为空，";
            }
        }

        //免冠照
        if (StringUtils.isBlank(clobContentInfo.getFile_80())) {
            common_fail_message += "请采集客户免冠照，";
        }
        // 非网厅业务办理业务类型
        if (!ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, clobContentInfo.getBusin_type())) {
            //经常居住地址
            if (StringUtils.isBlank(clobContentInfo.getUser_base_info().getAddress())) {
                common_fail_message += "经常居住地址不能为空，";
            } else {
                String translationAddress = auditCenterCommonService.getTranslationAddress(clobContentInfo.getUser_base_info().getAddress());
                if ("经常居住地址的格式不正确".equals(translationAddress)) {
                    common_fail_message += "经常居住地址的格式不正确，";
                }
            }
            //职业
            if (StringUtils.isBlank(clobContentInfo.getUser_base_info().getProfession_code())) {
                common_fail_message += "职业不能为空，";
            }
            if (StringUtils.isBlank(clobContentInfo.getId_card_info().getId_begindate())) {
                common_fail_message += "身份证开始日期不能为空，";
            }
            if (StringUtils.isBlank(clobContentInfo.getId_card_info().getId_enddate())) {
                common_fail_message += "身份证结束日期不能为空，";
            }


            //异地开户理由 客户属于异地开户，请填写异地开户理由信息！
            String branch_no = clobContentInfo.getBranch_no();
            BranchInfo branch = cacheBranch.getBranchByNo(branch_no);
            if (null != branch) {
                String province_code = branch.getProvince_code();
                String address = clobContentInfo.getUser_base_info().getAddress();
                String province = auditCenterCommonService.getAddressCode(address, "province");
                if (!StringUtils.equals(province_code, province) && StringUtils.isBlank(clobContentInfo.getUser_base_info().getChoose_branch_reason())) {
                    common_fail_message += "客户属于异地开户，请填写异地开户理由信息，";
                }
            }
        }

        // 证件地址、智能审核
        if (StringUtils.equalsAny(StringUtils.defaultIfBlank(clobContentInfo.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL),
                WskhConstant.BUSIN_TYPE_NORMAL) && !StringUtils.equals(IdKindEnum.FOREIGN_PREV_PERMIT.getCode(), clobContentInfo.getId_kind())) {
            // 身份证信息
            // 通行证证件地址可为空，不校验证件地址
            if (!StringUtils.equalsAny(clobContentInfo.getId_kind(), IdKindEnum.TAIWAN_PASS_CARD.getCode(), IdKindEnum.GA_PASS_CARD.getCode())) {
                if (StringUtils.isBlank(clobContentInfo.getId_card_info().getId_address())) {
                    common_fail_message += "证件地址不能为空，";
                }
            }

            //查询智能审核结果
            AuditQueryRecordReq auditQueryRecordReq = new AuditQueryRecordReq();
            auditQueryRecordReq.setRequest_no(clobContentInfo.getRequest_no());
            auditQueryRecordReq.setItem_identity(Collections.singletonList("3"));
            QueryAuditBusinRecordResp queryAuditBusinRecordResp = aiAuditService.queryAuditBusinRecordV1(auditQueryRecordReq, "");
            List<AuditBusinRecordQueryResp> list = queryAuditBusinRecordResp.getAuditBusinRecordQueryRespList();

            String scores_zt = PropertySource.get(PropKeyConstant.WSKH_ZT_SCORE_CONFIDENCE_RANGE, "40,60");
            int minScore_zt = Integer.parseInt(scores_zt.split(",")[0]);
            int face_score = StringUtils.isNotBlank(clobContentInfo.getFace_score()) ? Double.valueOf(clobContentInfo.getFace_score()).intValue() : 0;
            if (CollectionUtils.isNotEmpty(list)) {
                if (StringUtils.isBlank(clobContentInfo.getFile_82()) && face_score >= minScore_zt) {
                    list = list.stream()
                            .filter(item -> {
                                //face_contrast_card_police -身份证头像和公安部头像比对 face_contrast_face_police-公安部头像和免冠照比对
                                if (StringUtils.equalsAny(item.getDrl_rule_name(), "face_contrast_card_police", "face_contrast_face_police")) {
                                    return false;
                                }
                                return true;
                            }).collect(Collectors.toList());
                }
                String str = list.stream()
                        .filter(i -> {
                            if (StringUtils.equals(String.valueOf(i.getError_no()), "0")) {
                                if (StringUtils.equals(i.getMatch_result(), "0")) {
                                    return true;
                                }
                            }
                            return false;
                        })
                        .map(AuditBusinRecordQueryResp::getHandle_info)
                        .collect(Collectors.joining(StrUtil.COMMA));
                if (StringUtils.isNotBlank(str)) {
                    common_fail_message += StrUtil.removeSuffix(str, StrPool.COMMA) + "，";
                }
            }
        }

        // 职业备注信息校验 只支持普通开户业务类型
        if (StringUtils.equalsAny(StringUtils.defaultIfBlank(clobContentInfo.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL),
                WskhConstant.BUSIN_TYPE_NORMAL)) {
            int age = 0;
            String client_gender = "";
            boolean checkProfessionMust = false;
            if (clobContentInfo.getId_card_info() != null) {
                IDCardInfo idCardInfo = clobContentInfo.getId_card_info();
                client_gender = idCardInfo.getClient_gender();
                String birthday = StringUtils.isEmpty(idCardInfo.getBirthday())
                        ? IdentifyUtils.getIdCardBirthDay(idCardInfo.getId_no()) : idCardInfo.getBirthday();
                age = AgeUtil.ageUtil(birthday);
            }
            // 获取配置信息
            String ageProfessionReasonCodeConfig = PropertySource.get(PropKeyConstant.WSKH_AGE_PROFESSION_REASON_CODE_CONFIG, "");
            if (StringUtils.isNotBlank(ageProfessionReasonCodeConfig)) {
                List<ProfessionReasonConfig> configList = PropertiesUtils.parseConfig(ageProfessionReasonCodeConfig);
                List<String> professionCodeList = PropertiesUtils.getProfessionCodeList(age, client_gender, configList);
                if (professionCodeList.contains(clobContentInfo.getUser_base_info().getProfession_code())) {
                    checkProfessionMust = true;
                }
            } else {
                if (age < 24 && ",22,".contains("," + clobContentInfo.getUser_base_info().getProfession_code() + ",")) {
                    checkProfessionMust = true;
                }
                if (age > 28 && ",58,".contains("," + clobContentInfo.getUser_base_info().getProfession_code() + ",")) {
                    checkProfessionMust = true;
                }
                if (age < 50 && ",55,".contains("," + clobContentInfo.getUser_base_info().getProfession_code() + ",")) {
                    checkProfessionMust = true;
                }
                if (((age > 55 && StringUtils.equals(client_gender, "2")) || (age > 60 && StringUtils.equals(client_gender, "1")))
                        && (!",55,34,41,51,56,57,".contains("," + clobContentInfo.getUser_base_info().getProfession_code() + ",")
                )) {
                    checkProfessionMust = true;
                }
            }
            if (checkProfessionMust && StringUtils.isBlank(clobContentInfo.getUser_base_info().getChoose_profession_reason())) {
                common_fail_message += "客户职业与常规年龄不符，请填写职业备注信息，";
            }
        }

        if (StringUtils.equalsAny(StringUtils.defaultIfBlank(clobContentInfo.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL), WskhConstant.BUSIN_TYPE_NORMAL,
                WskhConstant.CROSS_BUSIN_TYPE)) {
            //2.失信记录校验
            if (StringUtils.isBlank(clobContentInfo.getDishonest_record())) {
                // 失信记录为空时，身份证类型为港澳台居民居住证或外国人永久居留证时，不校验
                if (!StringUtils.equalsAny(clobContentInfo.getId_kind(), IdKindEnum.GAT_RESIDENCE_PERMIT.getCode(), IdKindEnum.FOREIGN_PREV_PERMIT.getCode())) {
                    common_fail_message += "请选择失信记录，";
                }
            } else if (StringUtils.equals(clobContentInfo.getDishonest_record(), "2")) {
                common_fail_message += "用户失信记录涉及市场禁止且在处罚期内，";
            } else if (StringUtils.equals(clobContentInfo.getDishonest_record(), "1")) {
                common_fail_message += "用户存在失信记录，";
            } else if (",3,4,".contains(clobContentInfo.getDishonest_record()) && StringUtils.isBlank(clobContentInfo.getDishonest_record_remark())) {
                common_fail_message += "存在失信记录，请填写失信备注信息，";
            }
        }

        if (StringUtils.isNotBlank(common_fail_message)) {
            return StrUtil.removeSuffix(common_fail_message, "，");
        }

        return null;
    }

    @Override
    public void saveBidirectionalVideoChatLog(SaveBidirectionalVideoChatLogRequest request) {
        log.info("保存双向视频通话记录，请求参数：{}", request);
        BidirectionalVideoChatLog bidirectionalVideoChatLog = new BidirectionalVideoChatLog();
        BaseBeanUtil.copyProperties(request, bidirectionalVideoChatLog);
        bidirectionalVideoChatLog.setSerial_id(idGenerator.nextUUID(null));
        bidirectionalVideoChatLog.setTohis_flag(Constant.TOHIS_FLAG_N);
        bidirectionalVideoChatLogService.save(bidirectionalVideoChatLog);
    }

    @Override
    public List<BidirectionalVideoChatLogInfo> queryBidirectionalVideoChatLog(String request_no) {
        log.info("查询双向视频通话记录，request_no：{}", request_no);
        LambdaQueryWrapper<BidirectionalVideoChatLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BidirectionalVideoChatLog::getRequest_no, request_no)
                .orderByDesc(BidirectionalVideoChatLog::getCreate_datetime);
        List<BidirectionalVideoChatLog> list = bidirectionalVideoChatLogService.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().map(item -> {
                BidirectionalVideoChatLogInfo info = new BidirectionalVideoChatLogInfo();
                BaseBeanUtil.copyProperties(item, info);
                return info;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 查询用户信息，更新用户所属机构&上级机构
     *
     * @param operator_no 用户编号
     * @param auditForm   更新对象
     */
    private void queryAndUpdateAuditForm(String operator_no, AuditForm auditForm) {
        BackendUser backendUser = cacheBackendUser.getBackendUserByStaffNo(operator_no);

        String branchNo = backendUser.getBranch_no();
        auditForm.setBranch_no(branchNo);
        BranchInfo optionalBranchInfo = cacheBranch.getBranchByNo(branchNo);
        if (Objects.isNull(optionalBranchInfo)) {
            log.error("查询用户staff_no=[{}]分支结构branch_no=[{}]信息为空！", operator_no, branchNo);
            throw new BizException(ErrorEnum.QUERY_USERINFO_BRANCH_ERROR.getValue(), String.format(ErrorEnum.QUERY_USERINFO_BRANCH_ERROR.getDesc(), operator_no, branchNo));
        }
        auditForm.setBranch_name(optionalBranchInfo.getBranch_name());

        if (StringUtils.equalsAny(optionalBranchInfo.getBranch_type(), BranchConstant.LEVEL_HEADQUARTERS, BranchConstant.LEVEL_SUBSIDIARY_COMPANY, BranchConstant.LEVEL_BRANCH_OFFICE)) {
            auditForm.setUp_branch_no(branchNo);
            auditForm.setUp_branch_name(optionalBranchInfo.getBranch_name());
        } else {
            String upBranchNo = StringUtils.isNotBlank(optionalBranchInfo.getUp_branch_no())
                    ? optionalBranchInfo.getUp_branch_no() : branchNo;
            BranchInfo upOptionalBranchInfo = cacheBranch.getBranchByNo(upBranchNo);
            if (Objects.isNull(upOptionalBranchInfo)) {
                log.error("查询用户staff_no=[{}]上级分支结构up_branch_no=[{}]信息为空！", operator_no, upBranchNo);
                throw new BizException(ErrorEnum.QUERY_USERINFO_UP_BRANCH_ERROR.getValue(), String.format(ErrorEnum.QUERY_USERINFO_UP_BRANCH_ERROR.getDesc(), operator_no, branchNo));
            }
            auditForm.setUp_branch_no(upBranchNo);
            auditForm.setUp_branch_name(upOptionalBranchInfo.getBranch_name());
        }
    }

    private Map<String, Object> paddingMap(AuditForm auditForm) {
        Map<String, Object> map = new HashMap<>(6);
        if (auditForm != null) {
            map.put(Fields.OPERATOR_NO, auditForm.getOperator_no());
            map.put(Fields.OPERATOR_NAME, auditForm.getOperator_name());
            map.put(Fields.BRANCH_NO, auditForm.getBranch_no());
            map.put(Fields.BRANCH_NAME, auditForm.getBranch_name());
            map.put(Fields.UP_BRANCH_NO, auditForm.getUp_branch_no());
            map.put(Fields.UP_BRANCH_NAME, auditForm.getUp_branch_name());
        }
        return map;
    }
}
