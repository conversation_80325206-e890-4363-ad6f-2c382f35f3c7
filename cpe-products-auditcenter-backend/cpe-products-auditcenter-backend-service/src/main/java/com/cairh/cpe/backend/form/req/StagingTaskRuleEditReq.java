package com.cairh.cpe.backend.form.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 规则编辑请求
 *
 * <AUTHOR>
 * @since 2025/3/12 09:47
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StagingTaskRuleEditReq extends StagingTaskRuleAddReq {

    /**
     * 规则唯一编号
     */
    @NotBlank(message = "规则唯一编号不能为空")
    private String serial_id;

}
