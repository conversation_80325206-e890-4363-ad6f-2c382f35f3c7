package com.cairh.cpe.backend.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cairh.cpe.api.aiaudit.resp.MaterialInfo;
import com.cairh.cpe.backend.form.req.AuditQueryRecordReq;
import com.cairh.cpe.backend.form.req.HisAuditForm;
import com.cairh.cpe.backend.service.IAiAuditService;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.Fields;
import com.cairh.cpe.common.constant.IdKindEnum;
import com.cairh.cpe.common.constant.PropKeyConstant;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowParams;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.service.*;
import com.cairh.cpe.service.aiaudit.response.AuditBusinRecordQueryResp;
import com.cairh.cpe.service.aiaudit.response.QueryAuditBusinRecordResp;
import com.cairh.cpe.service.aiaudit.service.IAiAuditAchieveService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AiAuditBackServiceImpl implements IAiAuditService {
    @Autowired
    private IAiAuditAchieveService aiAuditAchieveService;
    @Autowired
    private IRequestFlowService requestFlowService;

    @Autowired
    private IRequestService requestService;

    @Resource
    private IBusinFlowTaskService businFlowTaskService;

    @Resource
    private IBusinFlowParamsService businFlowParamsService;

    @Resource
    private IRepulsetReasonConfigService repulsetReasonConfigService;

    @Override
    public QueryAuditBusinRecordResp queryAuditBusinRecordV1(AuditQueryRecordReq auditQueryRecordReq, String notes) {
        List<String> item_identity = auditQueryRecordReq.getItem_identity();
        List<AuditBusinRecordQueryResp> auditBusinRecordQueryResps = aiAuditAchieveService.queryAuditBusinRecord(auditQueryRecordReq.getRequest_no(), item_identity);
        //将智能审核结果的人脸比对分数（80（免冠照）-82（公安照）） （2023 10 17 更改仅供参考使用）
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(auditQueryRecordReq.getRequest_no());
        // 老数据逻辑
        boolean isNewData = StringUtils.equals(clobContentInfo.getData_sign(), WskhConstant.DATA_SIGN_STATUS);

        // 港澳台居民居住证 && 港澳台通行证
        boolean isPermit = StringUtils.equals(WskhConstant.BUSIN_TYPE_NORMAL, clobContentInfo.getBusin_type()) &&
                StringUtils.equalsAny(clobContentInfo.getId_kind(), IdKindEnum.GAT_RESIDENCE_PERMIT.getCode(), IdKindEnum.GA_PASS_CARD.getCode(),
                        IdKindEnum.TAIWAN_PASS_CARD.getCode());
        // 开户是否传入人像比对数据
        if (StringUtils.isNotBlank(clobContentInfo.getDeal_status())) {
            isNewData = true;
        }
        if (!isNewData && !isPermit) {
            if (CollectionUtils.isNotEmpty(auditBusinRecordQueryResps)) {
                List<AuditBusinRecordQueryResp> records = auditBusinRecordQueryResps.stream().filter(record -> StringUtils.equals(record.getDrl_rule_name(), "face_contrast_face_police")).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(records)) {
                    //保存公安照和免冠照比对分数
                    Double match_score = records.get(0).getMatch_score();
                    if (StringUtils.isBlank(clobContentInfo.getFace_score_82_80()) || !StringUtils.equals(String.valueOf(match_score), String.valueOf(clobContentInfo.getFace_score_82_80()))) {
                        HashMap<String, Object> params = new HashMap<>();
                        params.put("face_score_82_80", match_score);
                        params.put(Fields.RECORD_TYPE, FlowStatusConst.SYSTEM_RECORD_TYPE);
                        requestFlowService.saveParamsRecord(auditQueryRecordReq.getRequest_no(), params);
                    }
                }
            }
        }
        QueryAuditBusinRecordResp queryAuditBusinRecordResp = new QueryAuditBusinRecordResp();
        boolean bidirectional_busin_type = StringUtils.equalsAny(clobContentInfo.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL) &&
                StringUtils.equals(clobContentInfo.getVideo_type(), WskhConstant.VIDEO_TYPE_2);
        // 双向视频不验证分数
        if ("controller".equals(notes) && !bidirectional_busin_type
                && !ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, clobContentInfo.getBusin_type()) && !isPermit) {
            if (StringUtils.isNotBlank(clobContentInfo.getFile_82())) {
                if (StringUtils.isBlank(clobContentInfo.getFace_score_82_80())
                        && (StringUtils.isBlank(clobContentInfo.getKh_face_score_82_80()) || Double.parseDouble(clobContentInfo.getKh_face_score_82_80()) < 0)) {
                    queryAuditBusinRecordResp.setWeak_fail_message("本次人脸识别失败，请重试或人工审核！");
                }
            }
        }
        String scores_zt = PropertySource.get(PropKeyConstant.WSKH_ZT_SCORE_CONFIDENCE_RANGE, "40,60");
        int minScore_zt = Integer.parseInt(scores_zt.split(",")[0]);
        int face_score = StringUtils.isNotBlank(clobContentInfo.getFace_score()) ? Double.valueOf(clobContentInfo.getFace_score()).intValue() : 0;
        if (CollectionUtils.isNotEmpty(auditBusinRecordQueryResps)) {
            if (StringUtils.isBlank(clobContentInfo.getFile_82()) && face_score >= minScore_zt) {
                auditBusinRecordQueryResps = auditBusinRecordQueryResps.stream()
                        .filter(item -> {
                            //face_contrast_card_police -客户头部正面照与公民身份信息核查照一致 face_contrast_face_police-身份证正面头像与公民身份信息核查照一致
                            if (StringUtils.equalsAny(item.getDrl_rule_name(), "face_contrast_card_police", "face_contrast_face_police")) {
                                return false;
                            }
                            return true;
                        }).collect(Collectors.toList());
            }
        }
        queryAuditBusinRecordResp.setAuditBusinRecordQueryRespList(auditBusinRecordQueryResps);
        return queryAuditBusinRecordResp;
    }

    @Override
    public MaterialInfo getHisAuditInfo(HisAuditForm hisAuditForm) {
        LambdaQueryWrapper<BusinFlowTask> taskWrapper = new LambdaQueryWrapper<>();
        taskWrapper.eq(BusinFlowTask::getRequest_no, hisAuditForm.getRequest_no());
        List<String> taskList = businFlowTaskService.list(taskWrapper).stream().map(BusinFlowTask::getSerial_id).collect(Collectors.toList());
        taskList.add(hisAuditForm.getRequest_no());
        List<MaterialInfo> materialInfoList = businFlowParamsService.list(new LambdaQueryWrapper<>(BusinFlowParams.class)
                        .in(BusinFlowParams::getRequest_no, taskList))
                .stream()
                .collect(Collectors.groupingBy(BusinFlowParams::getRequest_no))
                .entrySet()
                .stream()
                .map(item -> {
                    StringBuilder param_content = new StringBuilder();
                    for (BusinFlowParams param : item.getValue()) {
                        param_content.append(param.getBusi_content());
                    }
                    String submit_content = param_content.toString();
                    return JSON.parseObject(submit_content, MaterialInfo.class);
                })
                .filter(item -> StringUtils.isNotBlank(item.getAi_audit_code()) && item.getAi_audit_code().equals(hisAuditForm.getTask_id()))
                .sorted(Comparator.comparing(MaterialInfo::getRequest_no, String.CASE_INSENSITIVE_ORDER))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(materialInfoList)) {
            return new MaterialInfo();
        }
        return materialInfoList.get(0);
    }

}
