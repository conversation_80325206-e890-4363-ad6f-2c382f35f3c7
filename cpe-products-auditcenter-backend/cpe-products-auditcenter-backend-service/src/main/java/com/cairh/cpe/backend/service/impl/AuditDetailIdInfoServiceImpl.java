package com.cairh.cpe.backend.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.cairh.cpe.backend.form.req.HandInfoForm;
import com.cairh.cpe.backend.form.req.IdCardInfoForm;
import com.cairh.cpe.backend.form.req.ValidateCitizenDocumentForm;
import com.cairh.cpe.backend.form.resp.HandInfoResp;
import com.cairh.cpe.backend.form.resp.IdCardInfoResult;
import com.cairh.cpe.backend.service.IAuditDetailIdInfoService;
import com.cairh.cpe.businflow.service.ICustModifyRecordService;
import com.cairh.cpe.cache.component.ComponentWorkTimeService;
import com.cairh.cpe.common.constant.ErrorEnum;
import com.cairh.cpe.common.constant.Fields;
import com.cairh.cpe.common.constant.IdKindEnum;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.clob.IDCardInfo;
import com.cairh.cpe.common.entity.request.AuditChangeForm;
import com.cairh.cpe.common.entity.request.VerifyPassPortRequest;
import com.cairh.cpe.common.entity.response.VerifyPassPortResult;
import com.cairh.cpe.common.service.IRequestFlowService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.common.util.IdCardUtils;
import com.cairh.cpe.common.util.IdentifyUtils;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.service.aiaudit.response.AiAuditRuleResp;
import com.cairh.cpe.service.aiaudit.service.IAiAuditAchieveService;
import com.cairh.cpe.service.idverify.IComponentIdVerifyService;
import com.cairh.cpe.service.third.IElectThirdFileService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class AuditDetailIdInfoServiceImpl implements IAuditDetailIdInfoService {

    @Autowired
    private IRequestService requestService;
    @Autowired
    private ICustModifyRecordService custModifyRecordService;
    @Autowired
    private IAiAuditAchieveService aiAuditAchieveService;
    @Autowired
    private IComponentIdVerifyService componentIdVerifyService;
    @Autowired
    private ComponentWorkTimeService componentWorkTimeService;
    @Autowired
    private IRequestFlowService requestFlowService;
    @Resource
    private IElectThirdFileService electThirdFileService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<AiAuditRuleResp> idRetry(IdCardInfoForm idCardInfoForm, BusinFlowRequest businFlowRequest) {
        String request_no = idCardInfoForm.getRequest_no();
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(request_no);
        AuditChangeForm auditChangeForm = new AuditChangeForm();
        auditChangeForm.setRequest_no(request_no);
        auditChangeForm.setFlow_task_id(idCardInfoForm.getTask_id());
        auditChangeForm.setOperator_no(idCardInfoForm.getOperator_no());
        auditChangeForm.setOperator_name(idCardInfoForm.getOperator_name());
        IDCardInfo id_card_info = clobContentInfo.getId_card_info();
        id_card_info.setId_address(idCardInfoForm.getId_address() != null ? idCardInfoForm.getId_address() : clobContentInfo.getId_card_info().getId_address());
        id_card_info.setId_begindate(StringUtils.isNotBlank(idCardInfoForm.getId_begindate()) ? idCardInfoForm.getId_begindate() : clobContentInfo.getId_card_info().getId_begindate());
        id_card_info.setId_enddate(StringUtils.isNotBlank(idCardInfoForm.getId_enddate()) ? idCardInfoForm.getId_enddate() : clobContentInfo.getId_card_info().getId_enddate());
        id_card_info.setAuxiliary_id_address(StringUtils.isNotBlank(idCardInfoForm.getAuxiliary_id_address()) ? idCardInfoForm.getAuxiliary_id_address() : clobContentInfo.getId_card_info().getAuxiliary_id_address());
        id_card_info.setAuxiliary_id_begindate(StringUtils.isNotBlank(idCardInfoForm.getAuxiliary_id_begindate()) ? idCardInfoForm.getAuxiliary_id_begindate() : clobContentInfo.getId_card_info().getAuxiliary_id_begindate());
        id_card_info.setAuxiliary_id_enddate(StringUtils.isNotBlank(idCardInfoForm.getAuxiliary_id_enddate()) ? idCardInfoForm.getAuxiliary_id_enddate() : clobContentInfo.getId_card_info().getAuxiliary_id_enddate());
        id_card_info.setNationality(StringUtils.isNotBlank(idCardInfoForm.getNationality()) ? idCardInfoForm.getNationality() : clobContentInfo.getId_card_info().getNationality());
        id_card_info.setEnglish_name(StringUtils.isNotBlank(idCardInfoForm.getEnglish_name()) ? idCardInfoForm.getEnglish_name() : clobContentInfo.getId_card_info().getEnglish_name());
        id_card_info.setBirthday(StringUtils.isNotBlank(idCardInfoForm.getBirthday()) ? idCardInfoForm.getBirthday() : clobContentInfo.getId_card_info().getBirthday());

        // 增持有证件号码会进行清空
        if (StringUtils.isNotBlank(idCardInfoForm.getPrev_id_number()) || StringUtils.isNotBlank(clobContentInfo.getId_card_info().getPrev_id_number())) {
            id_card_info.setPrev_id_number(idCardInfoForm.getPrev_id_number());
        }
        auditChangeForm.setId_card_info(id_card_info);
        auditChangeForm.setModify_link(businFlowRequest.getAnode_id());
        custModifyRecordService.saveParamsAndRecord(auditChangeForm);

        if (StringUtils.equalsAny(StringUtils.defaultIfBlank(clobContentInfo.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL), WskhConstant.BUSIN_TYPE_NORMAL)) {
            return aiAuditAchieveService.againExecuteAiaudit(request_no, "1");
        }

        return Lists.newArrayList();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<AiAuditRuleResp> handIdRetry(IdCardInfoForm idCardInfoForm) {
        String request_no = idCardInfoForm.getRequest_no();
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(request_no);
        if (StringUtils.equalsAny(IdKindEnum.ID_CARD.getCode(), clobContentInfo.getId_kind(), clobContentInfo.getAgent_id_kind())) {
            return aiAuditAchieveService.handAgainExecuteAiaudit(request_no, "1");
        }
        return CollectionUtil.newArrayList();
    }

    @Override
    public IdCardInfoResult getIdInfo(IdCardInfoForm idCardInfoForm) {
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(idCardInfoForm.getRequest_no());
        IDCardInfo id_card_info = clobContentInfo.getId_card_info();
        if (null == id_card_info) {
            return new IdCardInfoResult();
        }
        IdCardInfoResult idCardInfoResult = new IdCardInfoResult();
        BeanUtils.copyProperties(id_card_info, idCardInfoResult);
        String id_kind = clobContentInfo.getId_kind();
        idCardInfoResult.setId_kind_code(id_kind);
        String idCardBirthDay = "";
        if (IdKindEnum.ID_CARD.getCode().equals(id_kind)) {
            idCardBirthDay = IdentifyUtils.getIdCardBirthDay(id_card_info.getId_no());
        } else {
            idCardBirthDay = clobContentInfo.getBirthday();
        }
        idCardInfoResult.setBirthday(idCardBirthDay);
        idCardInfoResult.setUser_gender(clobContentInfo.getUser_gender());
        if (StringUtils.equalsAny(StringUtils.defaultIfBlank(clobContentInfo.getBusin_type(), WskhConstant.BUSIN_TYPE_NORMAL), WskhConstant.BUSIN_TYPE_NORMAL)) {
            // 外国人永居证不做年龄处理
            if (!StringUtils.equals(IdKindEnum.FOREIGN_PREV_PERMIT.getCode(), id_kind)) {
                Map<String, String> checkEffectDateMap = IdCardUtils.checkEffectDateMap(id_card_info.getId_begindate(), id_card_info.getId_enddate(), idCardBirthDay);
                idCardInfoResult.setIssued_age_expiration_date(checkEffectDateMap.get(Fields.ERROR_INFO));
            }
        }
        idCardInfoResult.setInitial_address_modify_flag(clobContentInfo.getInitial_address_modify_flag());
        return idCardInfoResult;
    }

    @Override
    public HandInfoResp getHandInfo(HandInfoForm handInfoForm) {
        Map<String, Object> clobMap = requestService.getParamContentByRequestNo(handInfoForm.getRequest_no());
        return JSON.parseObject(JSON.toJSONString(clobMap), HandInfoResp.class);
    }

    @Override
    public void validateCitizenDocument(ValidateCitizenDocumentForm validateCitizenDocumentForm, BusinFlowRequest businFlowRequest) {
        try {
            String request_no = validateCitizenDocumentForm.getRequest_no();
            ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(request_no);
            IDCardInfo id_card_info = clobContentInfo.getId_card_info();

            // 修改名称校验三要素
            VerifyPassPortRequest verifyPassPortRequest = new VerifyPassPortRequest();
            verifyPassPortRequest.setFull_name(id_card_info.getClient_name());
            verifyPassPortRequest.setId_kind(id_card_info.getId_kind());
            verifyPassPortRequest.setId_no(id_card_info.getId_no());
            verifyPassPortRequest.setBranch_no(clobContentInfo.getBranch_no());
            verifyPassPortRequest.setOp_branch_no(verifyPassPortRequest.getBranch_no());
            verifyPassPortRequest.setArea_code(id_card_info.getNationality());
            verifyPassPortRequest.setBirthday(id_card_info.getBirthday());
            verifyPassPortRequest.setId_begindate(id_card_info.getId_begindate());
            verifyPassPortRequest.setId_enddate(id_card_info.getId_enddate());
            verifyPassPortRequest.setBase64_image(electThirdFileService.getFileThirdId(clobContentInfo.getFile_80(), id_card_info.getId_no()));
            verifyPassPortRequest.setRealtime_flag("1");
            // 是否中登时间
            if (componentWorkTimeService.isWorkTime(WskhConstant.WORK_TIME_ZD_GA)) {
                verifyPassPortRequest.setGt_cxlx("13");
                verifyPassPortRequest.setGt_hclb("11");
            } else {
                verifyPassPortRequest.setGt_cxlx("14");
            }
            VerifyPassPortResult verifyPassPortResult = componentIdVerifyService.verifyPassPort(verifyPassPortRequest);
            log.info("外国人永居证三要素校验结果：{}", verifyPassPortResult);
            if (verifyPassPortResult == null || !StringUtils.equals(verifyPassPortResult.getStatus(), "1")) {
                saveParams(request_no, validateCitizenDocumentForm.getOperator_no(), "0", "外国人永居证三要素校验失败！");
                throw new BizException(ErrorEnum.PERMANENT_ID_CHECK_ERROR.getValue(), ErrorEnum.PERMANENT_ID_CHECK_ERROR.getDesc());
            }
            saveParams(request_no, validateCitizenDocumentForm.getOperator_no(), "1", "外国人永居证三要素校验成功！");
        } catch (Exception e) {
            log.error("外国人永居证三要素校验异常：", e);
            throw new BizException(ErrorEnum.PERMANENT_ID_CHECK_ERROR.getValue(), ErrorEnum.PERMANENT_ID_CHECK_ERROR.getDesc());
        }
    }

    private void saveParams(String request_no, String operator_no, String check_result, String remark) {
        Map<String, Object> params = new HashMap<>();
        params.put(Fields.PERMANENT_ID_INFO_CHECK_RESULT, check_result);
        params.put(Fields.BUSINESS_REMARK, remark);
        params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22387);
        params.put(Fields.OPERATOR_NO, operator_no);
        params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_OPERATE_TYPE);
        requestFlowService.saveParamsRecord(request_no, params);
    }

}
