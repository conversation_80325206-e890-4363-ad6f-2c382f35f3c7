<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cairh.cpe.backend.mapper.MybusinFlowTaskMapper">

    <sql id="task_condition_v1">
        <if test="queryForm.request_datetime_start != null and queryForm.request_datetime_start!=''">
            and c.request_datetime &gt;= ${queryForm.request_datetime_start}
        </if>
        <if test="queryForm.request_datetime_end != null and queryForm.request_datetime_end!=''">
            and c.request_datetime &lt;= ${queryForm.request_datetime_end}
        </if>
        <if test="queryForm.audit_datetime_start != null and queryForm.audit_datetime_start!=''">
            and c.audit_finish_datetime &gt;= ${queryForm.audit_datetime_start}
        </if>
        <if test="queryForm.audit_datetime_end != null and queryForm.audit_datetime_end!=''">
            and c.audit_finish_datetime &lt;= ${queryForm.audit_datetime_end}
        </if>
        <if test="queryForm.review_datetime_start != null and queryForm.review_datetime_start!=''">
            and c.review_finish_datetime &gt;= ${queryForm.review_datetime_start}
        </if>
        <if test="queryForm.review_datetime_end != null and queryForm.review_datetime_end!=''">
            and c.review_finish_datetime &lt;= ${queryForm.review_datetime_end}
        </if>
        <if test="queryForm.double_datetime_start != null and queryForm.double_datetime_start!=''">
            and c.double_finish_datetime &gt;= ${queryForm.double_datetime_start}
        </if>
        <if test="queryForm.double_datetime_end != null and queryForm.double_datetime_end!=''">
            and c.double_finish_datetime &lt;= ${queryForm.double_datetime_end}
        </if>
        <if test="queryForm.open_channel != null and queryForm.open_channel!=''">
            and c.open_channel = #{queryForm.open_channel}
        </if>
        <if test="queryForm.video_type != null and queryForm.video_type!=''">
            and c.video_type = #{queryForm.video_type}
        </if>
        <if test="queryForm.id_kind !=null and queryForm.id_kind != ''">
            and c.id_kind = #{queryForm.id_kind}
        </if>
        <if test="queryForm.client_name != null and queryForm.client_name!=''">
            and c.client_name= #{queryForm.client_name}
        </if>
        <if test="queryForm.id_no != null and queryForm.id_no!=''">
            and c.id_no = #{queryForm.id_no}
        </if>
        <if test="queryForm.mobile_tel != null and queryForm.mobile_tel!=''">
            and c.mobile_tel = #{queryForm.mobile_tel}
        </if>
        <if test="queryForm.broker_name != null and queryForm.broker_name!=''">
            and c.broker_name like '%' || #{queryForm.broker_name} || '%'
        </if>
        <if test="queryForm.channel_code != null and queryForm.channel_code!=''">
            and c.channel_code = #{queryForm.channel_code}
        </if>
        <if test="queryForm.channel_name != null and queryForm.channel_name!=''">
            and c.channel_name like '%' || #{queryForm.channel_name} || '%'
        </if>
        <if test="queryForm.activity_no != null and queryForm.activity_no!=''">
            and c.activity_no = #{queryForm.activity_no}
        </if>
        <if test="queryForm.activity_name != null and queryForm.activity_name!=''">
            and c.activity_name like '%' || #{queryForm.activity_name} || '%'
        </if>
        <if test="queryForm.marketing_team != null and queryForm.marketing_team!=''">
            and c.marketing_team like '%' || #{queryForm.marketing_team} || '%'
        </if>
        <if test="queryForm.audit_operator_no != null and queryForm.audit_operator_no!=''">
            and c.audit_operator_no = #{queryForm.audit_operator_no}
        </if>
        <if test="queryForm.audit_operator_name != null and queryForm.audit_operator_name!=''">
            and c.audit_operator_name like '%' || #{queryForm.audit_operator_name} || '%'
        </if>
        <if test="queryForm.review_operator_no != null and queryForm.review_operator_no!=''">
            and c.review_operator_no = #{queryForm.review_operator_no}
        </if>
        <if test="queryForm.review_operator_name != null and queryForm.review_operator_name!=''">
            and c.review_operator_name like '%' || #{queryForm.review_operator_name} || '%'
        </if>
        <if test="queryForm.double_operator_no != null and queryForm.double_operator_no!=''">
            and c.double_operator_no = #{queryForm.double_operator_no}
        </if>
        <if test="queryForm.double_operator_name != null and queryForm.double_operator_name!=''">
            and c.double_operator_name like '%' || #{queryForm.double_operator_name} || '%'
        </if>
        <if test="queryForm.operator_branch !=null and queryForm.operator_branch.size()!=0">
            and c.branch_no IN
            <foreach collection="queryForm.operator_branch" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryForm.task_status_type_list!=null and queryForm.task_status_type_list.size()!=0">
            and c.task_type in
            <foreach collection="queryForm.task_status_type_list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryForm.busin_types !=null and queryForm.busin_types.size()!=0">
            and c.busin_type IN
            <foreach collection="queryForm.busin_types" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryForm.app_ids !=null and queryForm.app_ids.size()!=0">
            and c.app_id IN
            <foreach collection="queryForm.app_ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryForm.task_source != null and queryForm.task_source!=''">
            and c.task_source = #{queryForm.task_source}
        </if>
        <if test="queryForm.match_label != null and queryForm.match_label!=''">
            and c.match_labels like '%' || #{queryForm.match_label} || '%'
        </if>
        <if test="queryForm.white_flag != null and queryForm.white_flag!=''">
            and c.white_flag = #{queryForm.white_flag}
        </if>
    </sql>


    <!--我的代办开启手动认领-->
    <select id="selectTaskListByPage" resultType="com.cairh.cpe.backend.form.resp.TaskDetailInfo">
        select /*+ index(b IDX_BFTASK_OPERATORNO_TASKSTATUS_PUSHFLAG)*/
        <include refid="task_field"/>
        from BUSINFLOWTASK b
        inner join USERQUERYEXTINFO u on b.REQUEST_NO = u.REQUEST_NO
        where b.task_status in ('1','2','a','b')
        and b.push_flag != '7'
        and instr(b.not_allow_auditor,#{queryForm.operator_no}) = 0
        <include refid="task_condition"/>
        order by decode(b.task_status,'a','6','2','5','1','4','1') desc, b.video_type desc, b.create_datetime
    </select>

    <!--我处理的-->
    <select id="selectTaskListByPageByTaskId" resultType="com.cairh.cpe.backend.form.resp.TaskDetailInfo">
        select
            c.*
        from(
            select
                b.match_labels,b.serial_id,b.request_no,b.create_datetime,b.deal_datetime,b.task_source,b.task_status,
                b.white_flag,b.task_type || '-' || b.task_status task_type,b.task_type task_type_code,b.operator_no,
                b.push_flag,b.not_allow_auditor,b.finish_datetime,
                'noshow' show_button,b.finish_datetime_new,
                (case when b.task_status in ('1','2','a','b') then u1.client_category
                when b.task_status in ('3','4','8') then u.client_category end ) CLIENT_CATEGORY,
                (case when b.task_status in ('1','2','a','b') then u1.CLIENT_NAME
                when b.task_status in ('3','4','8') then u.CLIENT_NAME end ) CLIENT_NAME,
                (case when b.task_status in ('1','2','a','b') then u1.broker_name
                when b.task_status in ('3','4','8') then u.broker_name end ) BROKER_NAME,
                (case when b.task_status in ('1','2','a','b') then u1.MOBILE_TEL
                when b.task_status in ('3','4','8') then u.MOBILE_TEL end ) MOBILE_TEL,
                (case when b.task_status in ('1','2','a','b') then u1.ID_NO
                when b.task_status in ('3','4','8') then u.ID_NO end ) ID_NO,
                (case when b.task_status in ('1','2','a','b') then u1.ID_KIND
                when b.task_status in ('3','4','8') then u.ID_KIND end ) ID_KIND,
                (case when b.task_status in ('1','2','a','b') then u1.CHANNEL_CODE
                when b.task_status in ('3','4','8') then u.CHANNEL_CODE end ) CHANNEL_CODE,
                (case when b.task_status in ('1','2','a','b') then u1.ACTIVITY_NO
                when b.task_status in ('3','4','8') then u.ACTIVITY_NO end ) ACTIVITY_NO,
                (case when b.task_status in ('1','2','a','b') then u1.ACTIVITY_NAME
                when b.task_status in ('3','4','8') then u.ACTIVITY_NAME end ) ACTIVITY_NAME,
                (case when b.task_status in ('1','2','a','b') then u1.MARKETING_TEAM
                when b.task_status in ('3','4','8') then u.MARKETING_TEAM end ) MARKETING_TEAM,
                (case when b.task_status in ('1','2','a','b') then u1.BRANCH_NO
                when b.task_status in ('3','4','8') then u.BRANCH_NO end ) branch_name,
                (case when b.task_status in ('1','2','a','b') then u1.branch_no
                when b.task_status in ('3','4','8') then u.branch_no end ) branch_no,
                (case when b.task_status in ('1','2','a','b') then u1.AUDIT_OPERATOR_NO
                when b.task_status in ('3','4','8') then u.AUDIT_OPERATOR_NO end ) AUDIT_OPERATOR_NO,
                (case when b.task_status in ('1','2','a','b') then u1.AUDIT_OPERATOR_NAME
                when b.task_status in ('3','4','8') then u.AUDIT_OPERATOR_NAME end ) AUDIT_OPERATOR_NAME,
                (case when b.task_status in ('1','2','a','b') then u1.audit_finish_datetime
                when b.task_status in ('3','4','8') then u.audit_finish_datetime end ) audit_finish_datetime,
                (case when b.task_status in ('1','2','a','b') then u1.REVIEW_OPERATOR_NO
                when b.task_status in ('3','4','8') then u.REVIEW_OPERATOR_NO end ) REVIEW_OPERATOR_NO,
                (case when b.task_status in ('1','2','a','b') then u1.REVIEW_OPERATOR_NAME
                when b.task_status in ('3','4','8') then u.REVIEW_OPERATOR_NAME end ) REVIEW_OPERATOR_NAME,
                (case when b.task_status in ('1','2','a','b') then u1.review_finish_datetime
                when b.task_status in ('3','4','8') then u.review_finish_datetime end ) review_finish_datetime,
                (case when b.task_status in ('1','2','a','b') then u1.double_operator_no
                when b.task_status in ('3','4','8') then u.double_operator_no end ) double_operator_no,
                (case when b.task_status in ('1','2','a','b') then u1.double_operator_name
                when b.task_status in ('3','4','8') then u.double_operator_name end ) double_operator_name,
                (case when b.task_status in ('1','2','a','b') then u1.double_finish_datetime
                when b.task_status in ('3','4','8') then u.double_finish_datetime end ) double_finish_datetime,
                (case when b.task_status in ('1','2','a','b') then u1.REQUEST_DATETIME
                when b.task_status in ('3','4','8') then u.REQUEST_DATETIME end ) REQUEST_DATETIME,
                (case when b.task_status in ('1','2','a','b') then u1.VIDEO_TYPE
                when b.task_status in ('3','4','8') then u.VIDEO_TYPE end ) VIDEO_TYPE,
                (case when b.task_status in ('1','2','a','b') then u1.mobile_location
                when b.task_status in ('3','4','8') then u.mobile_location end ) mobile_location,
                (case when b.task_status in ('1','2','a','b') then u1.open_channel
                when b.task_status in ('3','4','8') then u.open_channel end ) open_channel,
                (case when b.task_status in ('1','2','a','b') then u1.channel_name
                when b.task_status in ('3','4','8') then u.channel_name end ) channel_name,
                (case when b.task_status in ('1','2','a','b') then u1.app_id
                when b.task_status in ('3','4','8') then u.app_id end ) app_id,
                (case when b.task_status in ('1','2','a','b') then u1.busin_type
                when b.task_status in ('3','4','8') then u.busin_type end ) busin_type
            from (
                select
                    b.serial_id,b.request_no,b.create_datetime,b.deal_datetime,b.match_labels,b.task_source,b.task_status,
                    b.white_flag,b.task_type,b.operator_no,b.push_flag,b.not_allow_auditor,b.finish_datetime,
                    ROW_NUMBER() OVER (PARTITION BY TASK_ID ORDER BY CREATE_DATETIME DESC) AS rn,
                    max(case when b.operator_no=#{queryForm.operator_no} then b.finish_datetime else null end) over(partition by
                    b.task_id) finish_datetime_new
                from BUSINFLOWTASK b
                where b.TASK_ID in (select TASK_ID from BUSINFLOWTASK a where a.FINISH_DATETIME >= trunc(sysdate) and
                    a.operator_no=#{queryForm.operator_no} )
                ) b
            left join USERQUERYEXTINFO u on b.serial_id = u.REQUEST_NO
            and b.task_status in ('3','4','8')
            left join USERQUERYEXTINFO u1 on b.REQUEST_NO = u1.REQUEST_NO
            and b.task_status in ('1','2','a','b')
            where b.rn = 1
            <if test="queryForm.client_category != null and queryForm.client_category!=''">
                and (u.client_category = #{queryForm.client_category}
                or u1.client_category = #{queryForm.client_category})
            </if>
        )c
        where 1=1
            <include refid="task_condition_v1"/>
        order by
            c.finish_datetime_new desc
    </select>
    <!--我的代办未开启手动认领-->
    <select id="selectTaskList" resultType="com.cairh.cpe.backend.form.resp.TaskDetailInfo">
        select
        <include refid="task_field"/>
        from BUSINFLOWTASK b
        inner join USERQUERYEXTINFO u on b.REQUEST_NO = u.REQUEST_NO
        where b.task_status in ('1','2','a','b')
        and b.push_flag != '7'
        and instr(b.not_allow_auditor,#{queryForm.operator_no}) = 0
        <include refid="task_condition"/>
    </select>
    <!--我的代办未开启手动认领,查询数量count-->
    <select id="taskListCount" resultType="java.lang.Integer">
        select count(1)
        from BUSINFLOWTASK b
        inner join USERQUERYEXTINFO u on b.REQUEST_NO = u.REQUEST_NO
        where b.task_status in ('1','2','a','b')
        and b.push_flag != '7'
        and instr(b.not_allow_auditor,#{queryForm.operator_no}) = 0
        <include refid="task_condition"/>
    </select>
    <!--我的代办未开启手动认领-->
    <select id="selectTaskAgentList" resultType="com.cairh.cpe.backend.form.resp.TaskDetailInfoExport">
        select
        <include refid="task_field"/>
        from BUSINFLOWTASK b
        inner join USERQUERYEXTINFO u on b.REQUEST_NO = u.REQUEST_NO
        where b.task_status in ('1','2','a','b')
        and b.push_flag != '7'
        and instr(b.not_allow_auditor,#{queryForm.operator_no}) = 0
        <include refid="task_condition"/>
    </select>


    <sql id="task_field">
        u.client_name,
        u.mobile_tel,
        u.id_no,
        u.id_kind,
        u.channel_code,
        u.activity_no,
        u.activity_name,
        u.marketing_team,
        u.branch_no branch_name,
        u.branch_no,
        u.audit_operator_no,
        u.audit_operator_name,
        u.audit_finish_datetime,
        u.review_operator_no,
        u.review_operator_name,
        u.review_finish_datetime,
        u.double_operator_no,
        u.double_operator_name,
        u.double_finish_datetime,
        u.request_datetime,
        u.video_type,
        u.mobile_location,
        u.open_channel,
        u.channel_name,
        u.app_id,
        u.broker_name,
        u.busin_type,
        u.client_category,
        b.serial_id,
        b.request_no,
        b.create_datetime,
        b.deal_datetime,
        b.white_flag,
        b.task_id,
        b.task_source,
        b.task_status,
        b.task_type || '-' || b.task_status task_type,
        b.task_type task_type_code,
        b.operator_no,
        b.push_flag,
        b.not_allow_auditor,
        b.finish_datetime,
        b.match_labels,
        'show' show_button
    </sql>

    <sql id="task_condition">
        <if test="queryForm.request_datetime_start != null and queryForm.request_datetime_start!=''">
            and u.request_datetime &gt;= ${queryForm.request_datetime_start}
        </if>
        <if test="queryForm.request_datetime_end != null and queryForm.request_datetime_end!=''">
            and u.request_datetime &lt;= ${queryForm.request_datetime_end}
        </if>
        <if test="queryForm.audit_datetime_start != null and queryForm.audit_datetime_start!=''">
            and u.audit_finish_datetime &gt;= ${queryForm.audit_datetime_start}
        </if>
        <if test="queryForm.audit_datetime_end != null and queryForm.audit_datetime_end!=''">
            and u.audit_finish_datetime &lt;= ${queryForm.audit_datetime_end}
        </if>
        <if test="queryForm.review_datetime_start != null and queryForm.review_datetime_start!=''">
            and u.review_finish_datetime &gt;= ${queryForm.review_datetime_start}
        </if>
        <if test="queryForm.review_datetime_end != null and queryForm.review_datetime_end!=''">
            and u.review_finish_datetime &lt;= ${queryForm.review_datetime_end}
        </if>
        <if test="queryForm.double_datetime_start != null and queryForm.double_datetime_start!=''">
            and u.double_finish_datetime &gt;= ${queryForm.double_datetime_start}
        </if>
        <if test="queryForm.double_datetime_end != null and queryForm.double_datetime_end!=''">
            and u.double_finish_datetime &lt;= ${queryForm.double_datetime_end}
        </if>
        <if test="queryForm.open_channel != null and queryForm.open_channel!=''">
            and u.open_channel = #{queryForm.open_channel}
        </if>
        <if test="queryForm.video_type != null and queryForm.video_type!=''">
            and u.video_type = #{queryForm.video_type}
        </if>
        <if test="queryForm.client_name != null and queryForm.client_name!=''">
            and u.client_name= #{queryForm.client_name}
        </if>
        <if test="queryForm.id_kind !=null and queryForm.id_kind != ''">
            and u.id_kind = #{queryForm.id_kind}
        </if>
        <if test="queryForm.id_no != null and queryForm.id_no!=''">
            and u.id_no = #{queryForm.id_no}
        </if>
        <if test="queryForm.mobile_tel != null and queryForm.mobile_tel!=''">
            and u.mobile_tel = #{queryForm.mobile_tel}
        </if>
        <if test="queryForm.broker_name != null and queryForm.broker_name!=''">
            and u.broker_name like '%' || #{queryForm.broker_name} || '%'
        </if>
        <if test="queryForm.channel_code != null and queryForm.channel_code!=''">
            and u.channel_code = #{queryForm.channel_code}
        </if>
        <if test="queryForm.channel_name != null and queryForm.channel_name!=''">
            and u.channel_name like '%' || #{queryForm.channel_name} || '%'
        </if>
        <if test="queryForm.activity_no != null and queryForm.activity_no!=''">
            and u.activity_no = #{queryForm.activity_no}
        </if>
        <if test="queryForm.activity_name != null and queryForm.activity_name!=''">
            and u.activity_name like '%' || #{queryForm.activity_name} || '%'
        </if>
        <if test="queryForm.marketing_team != null and queryForm.marketing_team!=''">
            and u.marketing_team like '%' || #{queryForm.marketing_team} || '%'
        </if>
        <if test="queryForm.audit_operator_no != null and queryForm.audit_operator_no!=''">
            and u.audit_operator_no = #{queryForm.audit_operator_no}
        </if>
        <if test="queryForm.audit_operator_name != null and queryForm.audit_operator_name!=''">
            and u.audit_operator_name like '%' || #{queryForm.audit_operator_name} || '%'
        </if>
        <if test="queryForm.review_operator_no != null and queryForm.review_operator_no!=''">
            and u.review_operator_no = #{queryForm.review_operator_no}
        </if>
        <if test="queryForm.review_operator_name != null and queryForm.review_operator_name!=''">
            and u.review_operator_name like '%' || #{queryForm.review_operator_name} || '%'
        </if>
        <if test="queryForm.double_operator_no != null and queryForm.double_operator_no!=''">
            and u.double_operator_no = #{queryForm.double_operator_no}
        </if>
        <if test="queryForm.double_operator_name != null and queryForm.double_operator_name!=''">
            and u.double_operator_name like '%' || #{queryForm.double_operator_name} || '%'
        </if>
        <if test="queryForm.operator_branch !=null and queryForm.operator_branch.size()!=0">
            and u.branch_no IN
            <foreach collection="queryForm.operator_branch" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryForm.task_status_type_list!=null and queryForm.task_status_type_list.size()!=0">
            and b.task_type || '-' || b.task_status in
            <foreach collection="queryForm.task_status_type_list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryForm.task_types_role!=null and queryForm.task_types_role.size()!=0">
            and b.task_type in
            <foreach collection="queryForm.task_types_role" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="queryForm.busin_types !=null and queryForm.busin_types.size()!=0">
            and u.busin_type IN
            <foreach collection="queryForm.busin_types" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryForm.app_ids !=null and queryForm.app_ids.size()!=0">
            and u.app_id IN
            <foreach collection="queryForm.app_ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- 待处理tab -->
        <if test="queryForm.is_task_id_data =='0'.toString()">
            <choose>
                <!--  派单开启  自动派发 新增不参加派单的业务类型-->
                <when test="queryForm.isOpenDispatch == '1'.toString()  and queryForm.manual_claim_data=='0'.toString()">
                    and b.operator_no = #{queryForm.operator_no}
                </when>
                <!-- 手动领取列表 获取分配给自己的 已经 已推送还没有分配的见证任务-->
                <when test="queryForm.manual_claim_data != null and queryForm.manual_claim_data =='1'.toString()">
                    and (b.operator_no = #{queryForm.operator_no} or (b.operator_no = ' ' and b.push_flag='8'))
                </when>
                <!-- 派单关闭 列表查询 -->
                <otherwise>
                    and (b.operator_no = #{queryForm.operator_no} or (b.operator_no = ' ' and b.push_flag !='8'))
                </otherwise>
            </choose>
        </if>
        <if test="queryForm.client_category != null and queryForm.client_category!=''">
            and u.client_category = #{queryForm.client_category}
        </if>
        <if test="queryForm.task_source != null and queryForm.task_source!=''">
            and b.task_source = #{queryForm.task_source}
        </if>
        <if test="queryForm.match_label != null and queryForm.match_label!=''">
            and b.match_labels like '%' || #{queryForm.match_label} || '%'
        </if>
        <if test="queryForm.white_flag != null and queryForm.white_flag!=''">
            and b.white_flag = #{queryForm.white_flag}
        </if>
    </sql>


    <select id="countTaskNum" resultType="com.cairh.cpe.backend.form.resp.TaskFinishCountNum">
        select count(1) as task_num,
        NVL(sum(decode(b.task_type,'audit',1,0)),0) as audit_num,
        NVL(sum(decode(b.task_type,'review',1,0)),0) as review_num,
        NVL(sum(decode(b.task_type,'secondary_review',1,0)),0) as secondary_review_num,
        NVL(sum(decode(decode(b.task_type,'audit',1,0),'1',decode(b.task_status,'3','1','0'),'0')),0) as audit_pass_num,
        NVL(sum(decode(decode(b.task_type,'audit',1,0),'1',decode(b.task_status,'4','1','0'),'0')),0) as
        audit_notpass_num,
        NVL(sum(decode(decode(b.task_type,'review',1,0),'1',decode(b.task_status,'3','1','0'),'0')),0) as
        review_pass_num,
        NVL(sum(decode(decode(b.task_type,'review',1,0),'1',decode(b.task_status,'4','1','0'),'0')),0) as
        review_notpass_num,
        NVL(sum(decode(decode(b.task_type,'secondary_review',1,0),'1',decode(b.task_status,'3','1','0'),'0')),0) as
        secondary_review_pass_num,
        NVL(sum(decode(decode(b.task_type,'secondary_review',1,0),'1',decode(b.task_status,'4','1','0'),'0')),0) as
        secondary_review_notpass_name
        from businflowtask b where 1 = 1
        <if test="queryForm.operator_no != null and queryForm.operator_no !=''">
            and b.operator_no = #{queryForm.operator_no}
        </if>
        <if test="queryForm.current_time_start != null and queryForm.current_time_start !=''">
            and b.finish_datetime &gt;= TO_DATE(#{queryForm.current_time_start},'yyyy-MM-dd')
        </if>
    </select>

    <select id="countAuditPendingNum" resultType="com.cairh.cpe.backend.form.resp.TaskPendingCountNum">
        select
        COUNT(1) AS audit_pending_num,
        NVL(SUM(CASE WHEN b.task_type = 'audit' THEN 1 ELSE 0 END),0) AS audit_pend_num,
        NVL(SUM(CASE WHEN b.task_type = 'review' THEN 1 ELSE 0 END),0)AS review_pend_num,
        NVL(SUM(CASE WHEN b.task_type = 'secondary_review' THEN 1 ELSE 0 END),0) AS secondary_review_pend_num
        from businflowtask b
        where b.task_status in ('1', '2', 'a', 'b')
        <if test="queryForm.operator_no != null and queryForm.operator_no !=''">
            and b.operator_no = #{queryForm.operator_no}
        </if>
    </select>

    <select id="countCallsNum" resultType="int">
        select count(1)
        from CALLDETAILS
        where call_status = '1' and is_answer = '1'
        and create_datetime >= TRUNC(SYSDATE)
        <if test="queryForm.operator_no != null and queryForm.operator_no !=''">
            and STAFFID = #{queryForm.operator_no}
        </if>
    </select>

    <select id="claimTaskQuery" resultType="com.cairh.cpe.backend.form.resp.TaskDetailInfo">
        SELECT
            c.serial_id,
            c.not_allow_auditor,
            c.request_no,
            c.task_status,
            c.task_type || '-' || c.task_status as task_type,
            c.task_type task_type_str,
            c.operator_no,
            c.operator_name,
            u.request_datetime,
            u.video_type,
            u.client_name,
            u.mobile_tel,
            u.id_no,
            u.id_kind,
            u.channel_code,
            u.mobile_location,
            u.branch_no branch_name,
            u.branch_no branch_no,
            u.channel_name,
            u.audit_operator_no,
            u.audit_operator_name,
            u.review_operator_no,
            u.review_operator_name,
            c.push_flag
        FROM
        (
            SELECT
                b.serial_id,
                b.task_status,
                b.request_no,
                b.operator_no,
                b.task_type,
                b.create_datetime,
                b.operator_name,
                b.not_allow_auditor,
                b.push_flag,
                ROW_NUMBER () OVER (PARTITION BY b.request_no ORDER BY b.create_datetime DESC) AS rn
            FROM
                BUSINFLOWTASK b
            WHERE
            b.create_datetime >= TO_DATE(TO_CHAR(SYSDATE - 7, 'YYYY-MM-DD'), 'YYYY-MM-DD')
            and b.push_flag != '7') c
            LEFT JOIN USERQUERYEXTINFO u ON c.request_no = u.request_no
        WHERE
            c.rn = 1
            <include refid = "claimTaskQuery_condition" />
        ORDER BY
            c.create_datetime DESC
    </select>

    <sql id="claimTaskQuery_condition">
        <if test="queryForm.queryType != null and queryForm.queryType == 1">
            and u.client_name = #{queryForm.client_name}
        </if>
        <if test="queryForm.queryType != null and queryForm.queryType == 2">
            and u.mobile_tel = #{queryForm.client_name}
        </if>
        <if test="queryForm.queryType != null and queryForm.queryType == 3">
            and u.id_no = #{queryForm.client_name}
        </if>
        <if test="queryForm.operator_branch !=null and queryForm.operator_branch.size()!=0">
            and u.branch_no IN
            <foreach collection="queryForm.operator_branch" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>

</mapper>