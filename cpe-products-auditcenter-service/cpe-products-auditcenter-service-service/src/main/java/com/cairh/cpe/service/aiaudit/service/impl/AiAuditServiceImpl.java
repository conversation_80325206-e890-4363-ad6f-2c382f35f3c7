package com.cairh.cpe.service.aiaudit.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.cairh.cpe.aiaudit.apply.AiAuditApplyDubboService;
import com.cairh.cpe.aiaudit.apply.req.ApplyAuditCodeRequest;
import com.cairh.cpe.aiaudit.apply.resp.ApplyAuditCodeResponse;
import com.cairh.cpe.aiaudit.audio.AiauditAudioDubboService;
import com.cairh.cpe.aiaudit.audio.req.AuditAudioIdentifyRequest;
import com.cairh.cpe.aiaudit.audio.req.AuditAudioRequest;
import com.cairh.cpe.aiaudit.audio.resp.AuditAudioIdentityResponse;
import com.cairh.cpe.aiaudit.client.AiauditClientDubboService;
import com.cairh.cpe.aiaudit.client.req.AuditClientRequest;
import com.cairh.cpe.aiaudit.common.AiauditMaterialTypeSource;
import com.cairh.cpe.aiaudit.common.ImageCategory;
import com.cairh.cpe.aiaudit.common.rule.AiAuditRuleResult;
import com.cairh.cpe.aiaudit.face.AiAuditFaceDubboService;
import com.cairh.cpe.aiaudit.face.req.AuditFaceRequest;
import com.cairh.cpe.aiaudit.ocr.AiAuditOcrDubboService;
import com.cairh.cpe.aiaudit.ocr.req.*;
import com.cairh.cpe.aiaudit.query.AiAuditQueryDubboService;
import com.cairh.cpe.aiaudit.query.req.AuditBusinRecordQueryRequest;
import com.cairh.cpe.aiaudit.query.req.AuditResultQueryRequest;
import com.cairh.cpe.aiaudit.query.resp.AuditBusinRecordQueryResponse;
import com.cairh.cpe.aiaudit.query.resp.AuditResultQueryResponse;
import com.cairh.cpe.aiaudit.video.AiAuditVideoDubboService;
import com.cairh.cpe.aiaudit.video.req.AuditVideoRequest;
import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.common.service.IRepulsetReasonConfigService;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.rpc.CpeRpcException;
import com.cairh.cpe.service.aiaudit.request.*;
import com.cairh.cpe.service.aiaudit.response.AiAuditRuleResp;
import com.cairh.cpe.service.aiaudit.response.AuditAudioIdentityResp;
import com.cairh.cpe.service.aiaudit.response.AuditBusinRecordQueryResp;
import com.cairh.cpe.service.aiaudit.service.IAiAuditService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * * 智能审核调用dubbo接口
 */
@Service
@Slf4j
public class AiAuditServiceImpl implements IAiAuditService {

    @DubboReference(check = false)
    AiAuditQueryDubboService aiAuditQueryDubboService;
    @DubboReference(check = false)
    AiauditClientDubboService aiauditClientDubboService;
    @DubboReference(check = false)
    AiAuditApplyDubboService aiAuditApplyDubboService;
    @DubboReference(check = false)
    AiAuditOcrDubboService aiAuditOcrDubboService;
    @DubboReference(check = false)
    AiAuditFaceDubboService aiAuditFaceDubboService;
    @DubboReference(check = false, timeout = 120000)
    AiAuditVideoDubboService aiAuditVideoDubboService;
    @DubboReference(check = false, timeout = 120000)
    AiauditAudioDubboService aiauditAudioDubboService;
    @Resource
    private IRepulsetReasonConfigService repulsetReasonConfigService;


    @Override
    public List<AuditBusinRecordQueryResp> queryAuditBusinRecord(AuditBusinRecordQueryReq auditBusinRecordQueryReq) {
        try {
            AuditBusinRecordQueryRequest req = new AuditBusinRecordQueryRequest();
            BeanUtil.copyProperties(auditBusinRecordQueryReq, req);
            List<AuditBusinRecordQueryResponse> respList = aiAuditQueryDubboService.queryAuditBusinRecord(req);
            if (CollectionUtils.isNotEmpty(respList)) {
                List<AuditBusinRecordQueryResp> resultList = new ArrayList<>();
                respList.forEach(resp -> {
                    AuditBusinRecordQueryResp result = new AuditBusinRecordQueryResp();
                    BeanUtil.copyProperties(resp, result);
                    resultList.add(result);
                });
                return resultList;
            }
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_查询智能审核结果异常", e);
            throw new BizException("-1", "组件dubbo服务_查询智能审核结果异常");
        }
        return null;
    }


    @Override
    public String queryUnitedAuditResult(String audit_code) {
        try {
            AuditResultQueryRequest req = new AuditResultQueryRequest();
            req.setAudit_code(audit_code);
            AuditResultQueryResponse resp = aiAuditQueryDubboService.queryUnitedAuditResult(req);
            if (resp != null) {
                return resp.getMatch_result();
            }
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_查询整体结果异常", e);
            throw new BizException("-1", "组件dubbo服务_查询整体结果异常");
        }
        return null;
    }

    @Override
    public String applyAuditCode(ApplyAuditCodeReq applyAuditCodeReq) {
        try {
            ApplyAuditCodeRequest req = new ApplyAuditCodeRequest();
            BeanUtil.copyProperties(applyAuditCodeReq, req);
            ApplyAuditCodeResponse resp = aiAuditApplyDubboService.applyAuditCode(req);
            if (resp != null) {
                return resp.getAudit_code();
            }
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_智能审核申请编号异常", e);
            throw new BizException("-1", "组件dubbo服务_智能审核申请编号异常");
        }
        return null;
    }

    @Override
    public List<AiAuditRuleResp> auditClient(AuditClientReq auditClientReq) {
        try {
            AuditClientRequest req = new AuditClientRequest();
            BeanUtil.copyProperties(auditClientReq, req);
            List<AiAuditRuleResult> respList = aiauditClientDubboService.audit(req);
            if (CollectionUtils.isNotEmpty(respList)) {
                List<AiAuditRuleResp> resultList = new ArrayList<>();
                respList.forEach(resp -> {
                    AiAuditRuleResp result = new AiAuditRuleResp();
                    BeanUtil.copyProperties(resp, result);
                    resultList.add(result);
                });
                return resultList;
            }
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_智能客户基本信息审核异常", e);
            throw new BizException("-1", "组件dubbo服务_智能客户基本信息审核异常");
        }
        return null;
    }

    @Override
    public List<AiAuditRuleResp> auditIdCard(AuditOcrIdCardReq auditOcrIdCardReq) {
        try {
            AuditOcrIdCardRequest req = new AuditOcrIdCardRequest();
            BeanUtil.copyProperties(auditOcrIdCardReq, req);
            List<AiAuditRuleResult> respList = aiAuditOcrDubboService.auditIdCard(req);
            if (CollectionUtils.isNotEmpty(respList)) {
                List<AiAuditRuleResp> resultList = new ArrayList<>();
                respList.forEach(resp -> {
                    AiAuditRuleResp result = new AiAuditRuleResp();
                    BeanUtil.copyProperties(resp, result);
                    resultList.add(result);
                });
                return resultList;
            }
            log.info("auditIdCard返回结果为[{}]", JSON.toJSONString(respList));
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_智能OCR身份证审核异常", e);
            throw new BizException("-1", "组件dubbo服务_智能OCR身份证审核异常");
        }
        return null;
    }

    @Override
    public List<AiAuditRuleResp> auditGATPassInfo(AuditOcrGATPassInfoRequest auditOcrGATPassInfoRequest) {
        try {
            List<AiAuditRuleResult> respList = aiAuditOcrDubboService.auditGATPassInfo(auditOcrGATPassInfoRequest);
            if (CollectionUtils.isNotEmpty(respList)) {
                List<AiAuditRuleResp> resultList = new ArrayList<>();
                respList.forEach(resp -> {
                    AiAuditRuleResp result = new AiAuditRuleResp();
                    BeanUtil.copyProperties(resp, result);
                    resultList.add(result);
                });
                return resultList;
            }
            log.info("auditGATPassInfo返回结果为[{}]", JSON.toJSONString(respList));
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_智能OCR港澳台通行证审核异常", e);
            throw new BizException("-1", "组件dubbo服务_智能OCR港澳台通行证审核异常");
        }
        return null;
    }

    @Override
    public List<AiAuditRuleResp> auditGATResidenceInfo(AuditOcrGATResidenceInfoRequest auditOcrGATResidenceInfoRequest) {
        try {
            List<AiAuditRuleResult> respList = aiAuditOcrDubboService.auditGATResidenceInfo(auditOcrGATResidenceInfoRequest);
            if (CollectionUtils.isNotEmpty(respList)) {
                List<AiAuditRuleResp> resultList = new ArrayList<>();
                respList.forEach(resp -> {
                    AiAuditRuleResp result = new AiAuditRuleResp();
                    BeanUtil.copyProperties(resp, result);
                    resultList.add(result);
                });
                return resultList;
            }
            log.info("auditGATResidenceInfo返回结果为[{}]", JSON.toJSONString(respList));
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_智能OCR港澳台居住证审核异常", e);
            throw new BizException("-1", "组件dubbo服务_智能OCR港澳台居住证审核异常");
        }
        return null;
    }

    @Override
    public List<AiAuditRuleResp> auditForeignResidenceInfo(AuditOcrForeignResidenceInfoRequest auditOcrForeignResidenceInfoRequest) {
        try {
            List<AiAuditRuleResult> respList = aiAuditOcrDubboService.auditForeignResidenceInfo(auditOcrForeignResidenceInfoRequest);
            if (CollectionUtils.isNotEmpty(respList)) {
                List<AiAuditRuleResp> resultList = new ArrayList<>();
                respList.forEach(resp -> {
                    AiAuditRuleResp result = new AiAuditRuleResp();
                    BeanUtil.copyProperties(resp, result);
                    resultList.add(result);
                });
                return resultList;
            }
            log.info("auditForeignResidenceInfo返回结果为[{}]", JSON.toJSONString(respList));
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_智能OCR外国人永居证审核异常", e);
            throw new BizException("-1", "组件dubbo服务_智能OCR外国人永居证审核异常");
        }
        return null;
    }

    @Override
    public List<AiAuditRuleResp> auditImageQuality(AuditOcrImageQualityReq auditOcrImageQualityReq) {
        try {
            AuditOcrImageQualityRequest req = new AuditOcrImageQualityRequest();
            BeanUtil.copyProperties(auditOcrImageQualityReq, req);
            req.setSource(AiauditMaterialTypeSource.ARCH);
            if ("6A".equals(auditOcrImageQualityReq.getImage_no())) {
                req.setCategory(ImageCategory.FRONT);
            } else {
                req.setCategory(ImageCategory.BACK);
            }
            List<AiAuditRuleResult> respList = aiAuditOcrDubboService.auditImageQuality(req);
            if (CollectionUtils.isNotEmpty(respList)) {
                List<AiAuditRuleResp> resultList = new ArrayList<>();
                respList.forEach(resp -> {
                    AiAuditRuleResp result = new AiAuditRuleResp();
                    BeanUtil.copyProperties(resp, result);
                    resultList.add(result);
                });
                return resultList;
            }
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_智能OCR图像质检异常", e);
            throw new BizException("-1", "组件dubbo服务_智能OCR图像质检异常");
        }
        return null;
    }

    @Override
    public List<AiAuditRuleResp> auditFace(AuditFaceReq auditFaceReq) {
        // 3个图片缺少2个不再做智能审核
        List<String> imageLists = Arrays.asList(auditFaceReq.getFaceImage(), auditFaceReq.getPoliceImage(), auditFaceReq.getCardImage());
        long emptyCount = imageLists.stream()
                .filter(field -> StringUtils.isBlank(field))
                .count();
        if (emptyCount >= Constant.TWO) {
            return Lists.newArrayList();
        }
        try {
            AuditFaceRequest req = new AuditFaceRequest();
            BeanUtil.copyProperties(auditFaceReq, req);
            req.setSource(AiauditMaterialTypeSource.ARCH);
            List<AiAuditRuleResult> respList = aiAuditFaceDubboService.audit(req);
            if (CollectionUtils.isNotEmpty(respList)) {
                List<AiAuditRuleResp> resultList = new ArrayList<>();
                respList.forEach(resp -> {
                    AiAuditRuleResp result = new AiAuditRuleResp();
                    BeanUtil.copyProperties(resp, result);
                    resultList.add(result);
                });
                return resultList;
            }
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_智能人像审核异常", e);
            throw new BizException("-1", "组件dubbo服务_智能人像审核异常");
        }
        return null;
    }

    @Override
    public List<AiAuditRuleResp> auditVideo(AuditVideoReq auditVideoReq) {
        try {
            AuditVideoRequest req = new AuditVideoRequest();
            BeanUtil.copyProperties(auditVideoReq, req);
            req.setImageSource(AiauditMaterialTypeSource.ARCH);
            req.setVideoSource(AiauditMaterialTypeSource.ARCH);
            List<AiAuditRuleResult> respList = aiAuditVideoDubboService.audit(req);

            if (CollectionUtils.isNotEmpty(respList)) {
                List<AiAuditRuleResp> resultList = new ArrayList<>();
                respList.forEach(resp -> {
                    AiAuditRuleResp result = new AiAuditRuleResp();
                    BeanUtil.copyProperties(resp, result);
                    resultList.add(result);
                });
                return resultList;
            }
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_智能视频审核异常", e);
            throw new BizException("-1", "组件dubbo服务_智能视频审核异常");
        }
        return null;
    }

    @Override
    public AuditAudioIdentityResp auditAudioIdentify(AuditAudioIdentifyReq auditAudioIdentifyReq) {
        try {
            AuditAudioIdentifyRequest req = new AuditAudioIdentifyRequest();
            BeanUtil.copyProperties(auditAudioIdentifyReq, req);
            req.setSource(AiauditMaterialTypeSource.ARCH);
            AuditAudioIdentityResponse resp = aiauditAudioDubboService.auditIdentify(req);
            if (resp != null) {
                AuditAudioIdentityResp result = new AuditAudioIdentityResp();
                BeanUtil.copyProperties(resp, result);
                return result;
            }
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_智能语音发起异常", e);
            throw new BizException("-1", "组件dubbo服务_智能语音发起异常");
        }
        return null;
    }

    @Override
    public List<AiAuditRuleResp> auditAudio(AuditAudioReq auditAudioReq) {
        try {
            AuditAudioRequest req = new AuditAudioRequest();
            BeanUtil.copyProperties(auditAudioReq, req);
            List<AiAuditRuleResult> respList = aiauditAudioDubboService.audit(req);
            if (CollectionUtils.isNotEmpty(respList)) {
                List<AiAuditRuleResp> resultList = new ArrayList<>();
                respList.forEach(resp -> {
                    AiAuditRuleResp result = new AiAuditRuleResp();
                    BeanUtil.copyProperties(resp, result);
                    resultList.add(result);
                });
                return resultList;
            }
        } catch (CpeRpcException e) {
            log.error("组件dubbo服务异常", e.getError_info());
            throw new BizException(e.getError_no(), e.getError_info());
        } catch (Exception e) {
            log.error("组件dubbo服务_智能语音审核异常", e);
            throw new BizException("-1", "组件dubbo服务_智能语音审核异常");
        }
        return null;
    }
}
