package com.cairh.cpe.service.ac.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.IdKindEnum;
import com.cairh.cpe.common.constant.PropKeyConstant;
import com.cairh.cpe.common.entity.RepulsetReasonConfig;
import com.cairh.cpe.common.service.IRepulsetReasonConfigService;
import com.cairh.cpe.common.util.BaseBeanUtil;
import com.cairh.cpe.service.ac.form.RepulsetReasonForm;
import com.cairh.cpe.service.ac.form.RepulsetReasonResp;
import com.cairh.cpe.service.ac.service.IRepulsetReasonConfigHandleService;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class RepulsetReasonConfigHandleServiceImpl implements IRepulsetReasonConfigHandleService {

    @Resource
    private IRepulsetReasonConfigService repulsetReasonConfigService;

    @Override
    public List<RepulsetReasonResp> getRepulsetReason(RepulsetReasonForm repulsetReasonForm) {
        LambdaQueryChainWrapper<RepulsetReasonConfig> chainWrapper = repulsetReasonConfigService.lambdaQuery();
        String busin_type_mapping = PropertySource.get(PropKeyConstant.WSKH_AIAUDIT_BUSIN_TYPE_MAPPING, "");
        Map<String, String> businTypeMap = JSON.parseObject(busin_type_mapping, Map.class);
        Map<String, String> invertedMap = MapUtils.invertMap(businTypeMap);
        String new_busin_type = invertedMap.getOrDefault(repulsetReasonForm.getBusin_type(), repulsetReasonForm.getBusin_type());
        // 默认身份证
        String id_kind = IdKindEnum.ID_CARD.getCode();
        if (StringUtils.isNotBlank(repulsetReasonForm.getId_kind())) {
            id_kind = repulsetReasonForm.getId_kind();
        }
        if (StringUtils.contains(new_busin_type, "-")) {
            String[] businTypesplit = new_busin_type.split("-");
            new_busin_type = businTypesplit[0];
            id_kind = businTypesplit[1];
        }
        chainWrapper.eq(RepulsetReasonConfig::getBusin_type, new_busin_type);
        chainWrapper.eq(RepulsetReasonConfig::getId_kind, id_kind);
        if (CollectionUtil.isNotEmpty(repulsetReasonForm.getCause_groups())) {
            chainWrapper.in(RepulsetReasonConfig::getCause_group, repulsetReasonForm.getCause_groups());
        }
        chainWrapper.orderByAsc(RepulsetReasonConfig::getAudit_type, RepulsetReasonConfig::getCause_group);
        List<RepulsetReasonConfig> reasonConfigList = chainWrapper.list();
        return reasonConfigList.stream().filter(e -> StringUtils.isBlank(e.getUn_serial_id()))
                .map(item -> {
                    RepulsetReasonResp res = new RepulsetReasonResp();
                    BaseBeanUtil.copyProperties(item, res);
                    res.setChildren_config(BaseBeanUtil.copyToList(reasonConfigList.stream().
                            filter(rec -> StringUtils.equals(rec.getUn_serial_id(), res.getSerial_id()))
                            .sorted(Comparator.comparing(RepulsetReasonConfig::getCause_name))
                            .collect(Collectors.toList()), RepulsetReasonResp.class));
                    return res;
                }).collect(Collectors.toList());
    }
}