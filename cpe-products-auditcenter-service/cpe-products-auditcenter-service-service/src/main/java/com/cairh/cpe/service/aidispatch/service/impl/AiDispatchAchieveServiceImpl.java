package com.cairh.cpe.service.aidispatch.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.cairh.cpe.businflow.service.impl.StagingTaskRuleHandleStrategy;
import com.cairh.cpe.cache.component.ComponentWorkTimeService;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.dto.BaseRuleInfo;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.service.IBusinFlowRecordService;
import com.cairh.cpe.common.service.IBusinFlowRequestService;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.common.util.BaseBeanUtil;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.esb.dispatch.dto.req.TaskChangePauseFlagRequest;
import com.cairh.cpe.esb.dispatch.dto.req.TaskPopUpMsgRequest;
import com.cairh.cpe.esb.dispatch.dto.resp.DispatchTaskDetail;
import com.cairh.cpe.service.aidispatch.request.*;
import com.cairh.cpe.service.aidispatch.response.DispatchTaskDetailResp;
import com.cairh.cpe.service.aidispatch.service.IAiDispatchAchieveService;
import com.cairh.cpe.service.aidispatch.service.IAiDispatchService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class AiDispatchAchieveServiceImpl implements IAiDispatchAchieveService {

    @Autowired
    private IAiDispatchService aiDispatchService;
    @Autowired
    private IRequestService requestService;
    @Autowired
    private IBusinFlowTaskService businFlowTaskService;
    @Resource
    private ComponentWorkTimeService componentWorkTimeService;
    @Resource
    private IBusinFlowRequestService businFlowRequestService;
    @Resource
    private IBusinFlowRecordService businFlowRecordService;
    @Resource
    private RedissonUtil redissonUtil;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private StagingTaskRuleHandleStrategy stagingTaskRuleHandleStrategy;
    @Value("${dis.error.limit:5}")
    private Integer errorLimit;

    @Override
    public String submitDispatchData(String request_no, String not_allow_auditor, String flowtask_id, Date create_datetime) {
        if (PropertySource.get(PropKeyConstant.WSKH_ISOPEN_DISPATCH).equals("1")) {
            BusinFlowRequest newFlowRequest = requestService.getByRequestNo(request_no);
            ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(request_no);
            BusinFlowTask newFlowTask = businFlowTaskService.getById(flowtask_id);
            DispatchReq dispatchReq = new DispatchReq();
            dispatchReq.setSubsys_id(Long.valueOf(WskhConstant.SUBSYS_ID));
            dispatchReq.setBusin_type(Long.valueOf(newFlowRequest.getBusin_type()));
            dispatchReq.setBusin_name("集中审核业务");
            dispatchReq.setBusi_serial_no(request_no);
            dispatchReq.setClient_name(newFlowRequest.getClient_name());
            dispatchReq.setMobile_tel(newFlowRequest.getMobile_tel());
            dispatchReq.setId_kind(newFlowRequest.getId_kind());
            dispatchReq.setId_no(newFlowRequest.getId_no());
            dispatchReq.setBranch_no(newFlowRequest.getBranch_no());
            dispatchReq.setTask_type(getTaskType(newFlowTask.getTask_type()));
            dispatchReq.setCreate_datetime(newFlowTask.getCreate_datetime());
            String url = "/cpe-view-main-backend/#/task/detail?request_no=" + request_no + "&flowtask_id=" + flowtask_id;
            dispatchReq.setAction_url(url);
            dispatchReq.setBusi_content("");
            dispatchReq.setActivity_no(clobContentInfo.getActivity_no());
            dispatchReq.setActivity_name(clobContentInfo.getActivity_name());
            dispatchReq.setMarketing_team(clobContentInfo.getMarketing_team());
            dispatchReq.setVideo_auth_type(clobContentInfo.getVideo_type());
            dispatchReq.setChannel_code(clobContentInfo.getChannel_code());
            dispatchReq.setChannel_name(clobContentInfo.getChannel_name());
            dispatchReq.setAnti_operator_nos(not_allow_auditor);
            dispatchReq.setOpen_channel(clobContentInfo.getOpen_channel());
            dispatchReq.setMobile_location(clobContentInfo.getMobile_location());
            dispatchReq.setApp_id(clobContentInfo.getApp_id());

            String task_id = aiDispatchService.submitDispatchData(dispatchReq);
            // 任务修改
            LambdaUpdateWrapper<BusinFlowTask> updatePushFlag = new LambdaUpdateWrapper<>();
            updatePushFlag.set(BusinFlowTask::getPush_flag, WskhConstant.ALREADY_PUSH_FLAG);
            updatePushFlag.set(BusinFlowTask::getDispatch_task_id, task_id);
            updatePushFlag.eq(BusinFlowTask::getSerial_id, flowtask_id);
            businFlowTaskService.update(updatePushFlag);

            HashMap<String, Object> params = new HashMap<>();
            params.put(newFlowRequest.getAnode_id() + "_task_id", task_id);
            params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22383);
            params.put(Fields.BUSINESS_REMARK, "智能派单任务id绑定用户,task_id为：" + task_id);
            params.put(Fields.RECORD_TYPE, FlowStatusConst.SYSTEM_RECORD_TYPE);
            businFlowRecordService.saveBusinFlowRecord(newFlowRequest, params);
            return "success";
        }
        return "fail";
    }

    private String getTaskType(String anode_id) {
        String dispatchTaskType = "";
        if (StringUtils.equals(anode_id, FlowNodeConst.AUDIT)) {
            dispatchTaskType = "5";
        } else if (StringUtils.equals(anode_id, FlowNodeConst.REVIEW)) {
            dispatchTaskType = "6";
        } else if (StringUtils.equals(anode_id, FlowNodeConst.SECONDARY_REVIEW)) {
            dispatchTaskType = "7";
        }
        return dispatchTaskType;
    }

    @Override
    public void handleAuditDispatchTask(String dispatch_task_id, String operator_no) {
        if (PropertySource.get(PropKeyConstant.WSKH_ISOPEN_DISPATCH).equals("1")) {
            HandleDispatchReq handleDispatchReq = new HandleDispatchReq();
            handleDispatchReq.setTask_id(dispatch_task_id);
            handleDispatchReq.setOperator_no(operator_no);
            aiDispatchService.handleDispatchTask(handleDispatchReq);
        }
    }

    @Override
    public void finishDispatchTask(String dispatch_task_id, String operator_no) {
        if (PropertySource.get(PropKeyConstant.WSKH_ISOPEN_DISPATCH).equals("1")) {
            FinishDispatchReq finishDispatchReq = new FinishDispatchReq();
            finishDispatchReq.setTask_id(dispatch_task_id);
            finishDispatchReq.setOperator_no(operator_no);
            aiDispatchService.finishDispatchTask(finishDispatchReq);
        }
    }

    @Override
    public void changeOperatorStatus(String dispatchStatus, String operator_no) {
        DispatchOperator dispatch = new DispatchOperator();
        dispatch.setOperator_no(operator_no);
        if (StringUtils.equals(dispatchStatus, "0")) {
            aiDispatchService.switchToRest(dispatch);
        } else if (StringUtils.equals(dispatchStatus, "1")) {
            aiDispatchService.switchToWork(dispatch);
        }
    }

    @Override
    public boolean getOperatorStatus(String operator_no) {
        DispatchOperator dispatch = new DispatchOperator();
        dispatch.setOperator_no(operator_no);
        return aiDispatchService.isWorking(dispatch);
    }

    @Override
    public void claimTask(String dispatch_task_id, String operator_no, String pop_msg_model) {
        if (PropertySource.get(PropKeyConstant.WSKH_ISOPEN_DISPATCH).equals("1")) {
            TaskAssignOperatorReq taskAssignOperatorReq = new TaskAssignOperatorReq();
            taskAssignOperatorReq.setTask_id(dispatch_task_id);
            taskAssignOperatorReq.setSubsys_id(Long.valueOf(WskhConstant.SUBSYS_ID));
            taskAssignOperatorReq.setOperator_no(operator_no);
            taskAssignOperatorReq.setCurrent_operator_no(operator_no);
            taskAssignOperatorReq.setPop_msg_model(pop_msg_model);
            aiDispatchService.claimTask(taskAssignOperatorReq);
        }
    }


    @Override
    public void cancelTask(String dispatch_task_id, String operator_no, String pop_msg_model) {
        if (PropertySource.get(PropKeyConstant.WSKH_ISOPEN_DISPATCH).equals("1")) {
            CancelTaskReq cancelTaskReq = new CancelTaskReq();
            cancelTaskReq.setTask_id(dispatch_task_id);
            cancelTaskReq.setSubsys_id(Long.valueOf(WskhConstant.SUBSYS_ID));
            cancelTaskReq.setCurrent_operator_no(operator_no);
            cancelTaskReq.setPop_msg_model(pop_msg_model);
            aiDispatchService.cancelTask(cancelTaskReq);
        }
    }

    @Override
    public void taskChangeOperator(String dispatch_task_id, String operator_no, String remove_operator) {
        if (PropertySource.get(PropKeyConstant.WSKH_ISOPEN_DISPATCH).equals("1")) {
            TaskChangeOperatorReq taskChangeOperatorReq = new TaskChangeOperatorReq();
            taskChangeOperatorReq.setSubsys_id(Long.valueOf(WskhConstant.SUBSYS_ID));
            taskChangeOperatorReq.setTask_id(dispatch_task_id);
            taskChangeOperatorReq.setOperator_no(remove_operator);
            taskChangeOperatorReq.setCurrent_operator_no(operator_no);
            aiDispatchService.taskChangeOperator(taskChangeOperatorReq);
        }
    }


    @Override
    public DispatchTaskDetailResp queryTaskDetail(String dispatch_task_id) {
        DispatchTaskDetail dispatchTaskDetail = aiDispatchService.queryTaskDetail(dispatch_task_id);
        DispatchTaskDetailResp dispatchTaskDetailResp = new DispatchTaskDetailResp();
        BaseBeanUtil.copyProperties(dispatchTaskDetail, dispatchTaskDetailResp);
        return dispatchTaskDetailResp;
    }

    /**
     * 判断
     *
     * @param clobContentInfo
     * @param currTaskType
     * @return
     */
    private boolean checkIsAttendDispatch(ClobContentInfo clobContentInfo, BusinFlowTask currTaskType, boolean isWorkTime) {
        if (StringUtils.isNotBlank(clobContentInfo.getFile_82())) {
            return true;
        }
        //跨境理财 复审推送派单
        if (StringUtils.equalsAny(clobContentInfo.getBusin_type(), WskhConstant.CROSS_BUSIN_TYPE)) {
            return true;
        }
        // 网厅业务办理类型 复审推送派单
        if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, clobContentInfo.getBusin_type())) {
            return true;
        }
        // 港澳台居民居住证or外国人永居证
        if (StringUtils.equals(WskhConstant.BUSIN_TYPE_NORMAL, clobContentInfo.getBusin_type()) &&
                StringUtils.equalsAny(clobContentInfo.getId_kind(), IdKindEnum.GAT_RESIDENCE_PERMIT.getCode(), IdKindEnum.FOREIGN_PREV_PERMIT.getCode(),
                        IdKindEnum.TAIWAN_PASS_CARD.getCode(), IdKindEnum.GA_PASS_CARD.getCode())) {
            return true;
        }
        //不存在公安照
        int face_score = StringUtils.isNotBlank(clobContentInfo.getFace_score()) ? Double.valueOf(clobContentInfo.getFace_score()).intValue() : 0;
        boolean isopenEmergency = PropertySource.get(PropKeyConstant.WSKH_ISOPEN_EMERGENCY_CHANNEL, "0").equals("1");

        String scores_zt = PropertySource.get(PropKeyConstant.WSKH_ZT_SCORE_CONFIDENCE_RANGE, "40,60");
        int maxScore_zt = Integer.parseInt(scores_zt.split(",")[1]);

        // 修改逻辑  如果开启“启用无公安头像，人像比对分数达标允许复核提交”  然后在判断 开放渠道以及 业务类型的判断
        if (isopenEmergency) {
            boolean specialOpen = ("," + PropertySource.get(PropKeyConstant.WSKH_SPECIA_CHANNEL_OPEN, "0") + ",").contains("," + clobContentInfo.getOpen_channel() + ",");
            boolean businTypeBool = ("," + PropertySource.get(PropKeyConstant.WSKH_SPECIA_BUSIN_TYPE_OPEN, "0") + ",").contains("," + clobContentInfo.getBusin_type() + ",");
            if (specialOpen || businTypeBool) {
                if (face_score >= maxScore_zt) {
                    return true;
                }
            }
        }
        //将任务状态更改为待推送状态
        if (!isWorkTime) {
            UpdateWrapper<BusinFlowTask> updatePushFlag = new UpdateWrapper<>();
            updatePushFlag.set("push_flag", WskhConstant.NEED_PUSH_FLAG);
            updatePushFlag.eq("serial_id", currTaskType.getSerial_id());
            businFlowTaskService.update(updatePushFlag);
            return false;
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handleDispatchTask(String request_no, String serial_id) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, request_no);
        try {
            boolean isLock = redissonUtil.tryLock(lockKey, 0, 10, TimeUnit.SECONDS);
            if (!isLock) {
                log.info("任务推送派单未获取到锁，request_no={},serial_id={}", request_no, serial_id);
                return;
            }
            BusinFlowTask businFlowTask = businFlowTaskService.getById(serial_id);
            if (StringUtils.equals(businFlowTask.getPush_flag(), WskhConstant.ALREADY_PUSH_FLAG)) {
                return;
            }
            if (StringUtils.equalsAny(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_PASS, FlowStatusConst.AUDIT_NO_PASS, FlowStatusConst.AUDIT_INVALIDATE)) {
                return;
            }
            //公安照
            ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(businFlowTask.getRequest_no());
            //公安照
            boolean isWorkTime = componentWorkTimeService.isWorkTime(WskhConstant.WORK_TIME_ZD_GA);
            if (StringUtils.equalsAny(businFlowTask.getTask_type(), FlowNodeConst.REVIEW, FlowNodeConst.SECONDARY_REVIEW)) {
                boolean check = checkIsAttendDispatch(clobContentInfo, businFlowTask, isWorkTime);
                if (!check) {
                    return;
                }
            }

            // 双向视频&网厅业务办理，非中登时间可以推送见证任务
            boolean checkDouble = StringUtils.equals(businFlowTask.getVideo_type(), WskhConstant.VIDEO_TYPE_2);
            // 港澳台居民居住证or外国人永居证or港澳台内地往来通行证 支持7*24 派单
            boolean submitFlag = StringUtils.equals(WskhConstant.BUSIN_TYPE_NORMAL, clobContentInfo.getBusin_type()) &&
                    StringUtils.equalsAny(clobContentInfo.getId_kind(), IdKindEnum.GAT_RESIDENCE_PERMIT.getCode(), IdKindEnum.FOREIGN_PREV_PERMIT.getCode(),
                            IdKindEnum.TAIWAN_PASS_CARD.getCode(), IdKindEnum.GA_PASS_CARD.getCode());
            boolean staging_open_result = PropertySource.getBoolean(PropKeyConstant.WSKH_TASK_STAGING_SWITCH);
            if (StringUtils.isNotBlank(clobContentInfo.getFile_82())
                    || StringUtils.isNotBlank(clobContentInfo.getFace_score())
                    || isWorkTime
                    || StringUtils.equalsAny(clobContentInfo.getBusin_type(), WskhConstant.CROSS_BUSIN_TYPE)
                    || ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, clobContentInfo.getBusin_type())
                    || checkDouble
                    || submitFlag) {
                // 暂存开关开启以及视频类型为1
                if (staging_open_result && StringUtils.equals(WskhConstant.VIDEO_TYPE_1, clobContentInfo.getVideo_type())) {
                    BaseRuleInfo stagingTaskRule = stagingTaskRuleHandleStrategy.ruleMatch(businFlowTask, clobContentInfo);
                    if (stagingTaskRule != null) {
                        // 任务暂存
                        businFlowTaskService.taskStagingHandle(businFlowTask, stagingTaskRule);
                        return;
                    }
                }
                // 提交智能派单
                submitDispatchData(businFlowTask.getRequest_no(), businFlowTask.getNot_allow_auditor(), businFlowTask.getSerial_id(), null);
            } else {
                // 非工作时间 并且 无证通分  将push_flag 修改为 1 重新推送
                UpdateWrapper<BusinFlowTask> updatePushFlag = new UpdateWrapper<>();
                updatePushFlag.set("push_flag", WskhConstant.NEED_PUSH_FLAG);
                updatePushFlag.eq("serial_id", businFlowTask.getSerial_id());
                businFlowTaskService.update(updatePushFlag);
                log.info("推送给派单系统-不满足推送条件，不推送给派单[{}]", businFlowTask.getRequest_no());
            }
        } catch (Exception e) {
            log.error("推送派单系统发生异常信息 request_no={},serial_id={}", request_no, serial_id, e);
            exceptionHandler(serial_id);
            throw new BizException(ErrorEnum.PUSH_DISPATCH_ERROR.getValue(), "推送派单系统发生异常信息 serial_id={}" + serial_id);
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelReviewTask(String request_no, String serial_id) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, request_no);
        try {
            boolean isLock = redissonUtil.tryLock(lockKey, 0, 20, TimeUnit.SECONDS);
            if (!isLock) {
                log.info("非中登时间复审任务回收任务开始，request_no={},serial_id={}", request_no, serial_id);
                return;
            }
            BusinFlowTask businFlowTask = businFlowTaskService.getById(serial_id);
            if (!StringUtils.equals(businFlowTask.getTask_type(), FlowNodeConst.REVIEW)) {
                return;
            }
            if (!StringUtils.equals(businFlowTask.getPush_flag(), WskhConstant.ALREADY_PUSH_FLAG)) {
                return;
            }
            if (!StringUtils.equalsAny(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_PENDING, FlowStatusConst.AUDIT_SUSPEND)) {
                return;
            }
            Map<String, Object> clobParamsMap = requestService.getParamContentByRequestNoV1(businFlowTask.getRequest_no());
            Object file_82 = clobParamsMap.get("file_82");
            if (Objects.nonNull(file_82)) {
                return;
            }
            //如果无公安照  那么就行回收   派单关闭

            if (Objects.isNull(businFlowTask.getDispatch_task_id())) {
                return;
            }

            CancelTaskReq cancelTaskReq = new CancelTaskReq();
            cancelTaskReq.setTask_id(businFlowTask.getDispatch_task_id());
            cancelTaskReq.setCurrent_operator_no(businFlowTask.getOperator_no());
            cancelTaskReq.setSubsys_id(Long.valueOf(WskhConstant.SUBSYS_ID));
            cancelTaskReq.setPop_msg_model("");
            aiDispatchService.cancelTask(cancelTaskReq);

            // 收回人员信息
            LambdaUpdateWrapper<BusinFlowTask> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(BusinFlowTask::getPush_flag, WskhConstant.NEED_PUSH_FLAG);
            updateWrapper.set(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_PENDING);
            updateWrapper.set(BusinFlowTask::getSuspend_remind_num, Integer.parseInt("0"));
            updateWrapper.set(BusinFlowTask::getOperator_no, " ");
            updateWrapper.set(BusinFlowTask::getOperator_name, " ");
            updateWrapper.set(BusinFlowTask::getDeal_datetime, null);
            updateWrapper.set(BusinFlowTask::getAllow_auditor, " ");
            updateWrapper.set(BusinFlowTask::getTask_source, " ");
            updateWrapper.eq(BusinFlowTask::getSerial_id, serial_id);
            businFlowTaskService.update(updateWrapper);

            //更新申请表
            LambdaUpdateWrapper<BusinFlowRequest> requestWapper = new LambdaUpdateWrapper<>();
            requestWapper.set(BusinFlowRequest::getOperator_no, " ")
                    .set(BusinFlowRequest::getRequest_status, FlowStatusConst.AUDIT_PENDING)
                    .eq(BusinFlowRequest::getRequest_no, request_no);
            businFlowRequestService.update(requestWapper);

            //记录流水
            HashMap<String, Object> params = new HashMap<>();
            params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22399);
            params.put(Fields.BUSINESS_REMARK, "复审无公安照非中登时间回收自动回收任务");
            params.put(Fields.RECORD_TYPE, FlowStatusConst.SYSTEM_RECORD_TYPE);
            businFlowRecordService.saveBusinFlowRecord(businFlowTask.getRequest_no(), params);
        } catch (Exception e) {
            log.error("非中登时间复审任务回收任务开始 request_no={},serial_id={}", request_no, serial_id, e);
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handleNeedPushFlagDispatchTask(String request_no, String serial_id) {
        BusinFlowTask businFlowTask = businFlowTaskService.getById(serial_id);
        if (StringUtils.equals(businFlowTask.getPush_flag(), WskhConstant.ALREADY_PUSH_FLAG)) {
            return;
        }
        if (StringUtils.equalsAny(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_PASS, FlowStatusConst.AUDIT_NO_PASS, FlowStatusConst.AUDIT_INVALIDATE)) {
            return;
        }
        submitDispatchData(businFlowTask.getRequest_no(), businFlowTask.getNot_allow_auditor(), businFlowTask.getSerial_id(), null);
        log.info("中登异常推送给派单系统-已成功推送派单[{}]", businFlowTask.getRequest_no());
    }

    private void exceptionHandler(String serial_id) {
        String redisKey = String.format(RedisKeyConstant.WSKH_AC_SUBMIT_PARAMS_FAIL_COUNT, serial_id);
        Long increment = redisTemplate.opsForValue().increment(redisKey, 1);
        if (increment == 1) {
            redisTemplate.expire(redisKey, 10, TimeUnit.MILLISECONDS);
        }
        if (increment >= errorLimit) {
            LambdaUpdateWrapper<BusinFlowTask> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(BusinFlowTask::getPush_flag, WskhConstant.BLANK_PUSH_FLAG);
            updateWrapper.eq(BusinFlowTask::getSerial_id, serial_id);
            businFlowTaskService.update(updateWrapper);
            log.error("由于推送异常次数太多将push_flag 修改为空 -serial_id ={}", serial_id);
        }
    }


    @Override
    public void popUpMsg(Long subsys_id, String operator_no) {
        TaskPopUpMsgRequest popUpMsgRequest = new TaskPopUpMsgRequest();
        popUpMsgRequest.setSubsys_id(subsys_id);
        popUpMsgRequest.setOperator_no(operator_no);
        aiDispatchService.popUpMsg(popUpMsgRequest);
    }

    @Override
    public void handUpNotice(String task_id, String pause_flag) {
        if (PropertySource.get(PropKeyConstant.WSKH_ISOPEN_DISPATCH).equals("1")) {
            TaskChangePauseFlagRequest pauseFlagRequest = new TaskChangePauseFlagRequest();
            //挂起标志 必填 0：正常 1： 挂起
            pauseFlagRequest.setPause_flag(pause_flag);
            pauseFlagRequest.setTask_id(task_id);
            aiDispatchService.taskChangePauseFlag(pauseFlagRequest);
        }
    }

    @Override
    public void invalidTask(String dispatch_task_id, String operator_no, String push_flag) {
        if (PropertySource.get(PropKeyConstant.WSKH_ISOPEN_DISPATCH).equals("1")) {
            InvalidTaskReq invalidTaskReq = new InvalidTaskReq();
            invalidTaskReq.setSubsys_id(Long.valueOf(WskhConstant.SUBSYS_ID));
            invalidTaskReq.setTask_id(dispatch_task_id);
            invalidTaskReq.setOperator_no(operator_no);
            aiDispatchService.invalidTask(invalidTaskReq);
        }
    }
}
