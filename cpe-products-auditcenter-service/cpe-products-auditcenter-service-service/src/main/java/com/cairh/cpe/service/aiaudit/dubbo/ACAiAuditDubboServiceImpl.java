package com.cairh.cpe.service.aiaudit.dubbo;

import cn.hutool.core.lang.Assert;
import com.cairh.cpe.api.aiaudit.ACAiAuditDubboService;
import com.cairh.cpe.api.aiaudit.resp.MaterialInfo;
import com.cairh.cpe.common.constant.ClientCategoryEnum;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.service.IRepulsetReasonConfigService;
import com.cairh.cpe.common.service.IRequestService;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

@DubboService
public class ACAiAuditDubboServiceImpl implements ACAiAuditDubboService {


    @Autowired
    private IRequestService requestService;
    @Autowired
    private IRepulsetReasonConfigService repulsetReasonConfigService;


    @Override
    public MaterialInfo getUserMaterialInfo(String request_no) {
        Assert.notBlank(request_no, "业务编号不能为空");

        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(request_no);
        if (clobContentInfo == null) {
            clobContentInfo = requestService.getHisAllDataByRequestNo(request_no);
        }
        MaterialInfo materialInfo = new MaterialInfo();

        materialInfo.setId_kind(clobContentInfo.getId_kind());
        materialInfo.setId_no(clobContentInfo.getId_no());
        materialInfo.setClient_name(clobContentInfo.getClient_name());

        // 网厅业务办理
        if (ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, clobContentInfo.getBusin_type())) {
            if (StringUtils.equals(clobContentInfo.getClient_category(), ClientCategoryEnum.CLIENT_PERSON.getCode())) {
                materialInfo.setFile_6A(clobContentInfo.getPhoto_front());
                materialInfo.setFile_6B(clobContentInfo.getPhoto_back());
            } else {
                materialInfo.setFile_6A(clobContentInfo.getAgent_photo_front());
                materialInfo.setFile_6B(clobContentInfo.getAgent_photo_back());
            }
        } else {
            materialInfo.setFile_6A(clobContentInfo.getFile_6A());
            materialInfo.setFile_6B(clobContentInfo.getFile_6B());
        }
        materialInfo.setFile_80(clobContentInfo.getFile_80());
        materialInfo.setFile_82(clobContentInfo.getFile_82());
        materialInfo.setFile_8A(clobContentInfo.getFile_8A());


        return materialInfo;
    }
}
