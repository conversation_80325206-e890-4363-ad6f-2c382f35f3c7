package com.cairh.cpe.service.ac.service;

import com.cairh.cpe.service.ac.form.RepulsetReasonForm;
import com.cairh.cpe.service.ac.form.RepulsetReasonResp;

import java.util.List;

public interface IRepulsetReasonConfigHandleService {


    /**
     * 驳回原因列表
     *
     * @param repulsetReasonForm
     * @return
     */
    List<RepulsetReasonResp> getRepulsetReason(RepulsetReasonForm repulsetReasonForm);
}