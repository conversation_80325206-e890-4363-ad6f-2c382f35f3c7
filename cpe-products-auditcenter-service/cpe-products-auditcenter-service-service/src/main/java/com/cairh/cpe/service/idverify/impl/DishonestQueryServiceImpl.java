package com.cairh.cpe.service.idverify.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.cairh.cpe.common.constant.RedisKeyConstant;
import com.cairh.cpe.common.entity.request.QueryDishonestReq;
import com.cairh.cpe.common.entity.response.QueryDishonestResult;
import com.cairh.cpe.common.util.BaseBeanUtil;
import com.cairh.cpe.esb.base.rpc.IVBaseDishonestBlackListDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseQueryDishonestListReq;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseDishonestListQryResp;
import com.cairh.cpe.service.idverify.IDishonestQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class DishonestQueryServiceImpl implements IDishonestQueryService {


    @DubboReference(check = false)
    IVBaseDishonestBlackListDubboService ivBaseDishonestBlackListDubboService;

    @Resource
    private RedisTemplate<String, String> redisTemplate;


    @Override
    public QueryDishonestResult queryDishonestBlackList(QueryDishonestReq queryDishonestReq) {
        // 先从缓存中取值,如果获取不到则从接口中获取
        String redisKey = String.format(RedisKeyConstant.WSKH_DISHONEST_RECORD, queryDishonestReq.getId_no());
        if (redisTemplate.hasKey(redisKey)) {
            return JSON.parseObject(redisTemplate.opsForValue().get(redisKey), QueryDishonestResult.class);
        }
        QueryDishonestResult queryDishonestResult = new QueryDishonestResult();
        try {
            VBaseQueryDishonestListReq req = new VBaseQueryDishonestListReq();
            req.setClient_name(queryDishonestReq.getClient_name());
            req.setGt_zjbh(queryDishonestReq.getId_no());
            req.setId(queryDishonestReq.getId());
            req.setBranch_no(queryDishonestReq.getBranch_no());
            req.setOp_branch_no(queryDishonestReq.getBranch_no());
            List<VBaseDishonestListQryResp> qryResps = ivBaseDishonestBlackListDubboService.queryDishonestBlackList(req);
            log.info("用户[{}]失信记录查询返回结果为[{}]", queryDishonestReq.getClient_name(), JSON.toJSONString(qryResps));
            if (CollectionUtil.isEmpty(qryResps)) {
                log.info("用户[{}]失信记录查询返回结果为空", queryDishonestReq.getClient_name());
                queryDishonestResult.setResult_info_gt("组件dubbo服务_查询诚信记录异常");
                return queryDishonestResult;
            }
            VBaseDishonestListQryResp vBaseDishonestListQryResp = qryResps.get(0);
            BaseBeanUtil.copyProperties(vBaseDishonestListQryResp, queryDishonestResult);
            queryDishonestResult.setId_no(vBaseDishonestListQryResp.getGt_zjbh());
            //保留1天
            redisTemplate.opsForValue().set(redisKey, JSON.toJSONString(queryDishonestResult), 1, TimeUnit.DAYS);
            return queryDishonestResult;

        } catch (Exception e) {
            queryDishonestResult.setResult_info_gt(e.getMessage());
            log.error("组件dubbo服务_查询诚信记录异常", e);
        }
        return queryDishonestResult;
    }
}
