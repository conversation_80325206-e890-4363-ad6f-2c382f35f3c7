package com.cairh.cpe.service.ac.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cairh.cpe.businflow.entity.request.AuditResultNotifyReq;
import com.cairh.cpe.businflow.service.IAuditCallbackService;
import com.cairh.cpe.cache.service.CacheDict;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.constant.flow.FlowRecordEnum;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.dto.RejectReason;
import com.cairh.cpe.common.entity.*;
import com.cairh.cpe.common.entity.clob.AuditReason;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.response.DictInfo;
import com.cairh.cpe.common.service.*;
import com.cairh.cpe.common.util.BeanMapUtil;
import com.cairh.cpe.common.util.KHDateUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.db.config.IdGenerator;
import com.cairh.cpe.service.ac.form.AutoRejectHandleHandleReq;
import com.cairh.cpe.service.ac.service.IAutoRejectHandleService;
import com.cairh.cpe.service.aidispatch.service.IAiDispatchAchieveService;
import com.cairh.cpe.service.config.AiAuditThreadPoolTaskExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/22 13:16
 */
@Slf4j
@Service
public class AutoRejectHandleServiceImpl implements IAutoRejectHandleService {

    @Resource
    private IBusinFlowTaskService businFlowTaskService;
    @Resource
    private IBusinFlowRequestService businFlowRequestService;
    @Resource
    private IAiDispatchAchieveService aiDispatchAchieveService;
    @Resource
    private IBusinFlowRecordService businFlowRecordService;
    @Resource
    private ITaskReasonRecordService taskReasonRecordService;
    @Resource
    private IBusinFlowParamsService businFlowParamsService;
    @Resource
    private IUserQueryExtInfoService userQueryExtInfoService;
    @Resource
    private IBusinProcessRequestAuditTrailService businProcessRequestAuditTrailService;
    @Resource
    private IdGenerator idGenerator;
    @Resource
    private IAutoRejectRecordService autoRejectRecordService;
    @Resource
    private CacheDict cacheDict;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private AiAuditThreadPoolTaskExecutor aiAuditThreadPoolTaskExecutor;
    @Resource
    private IAuditCallbackService auditCallbackService;
    @Resource
    private IRepulsetReasonConfigService repulsetReasonConfigService;
    @Resource
    private IRequestService requestService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void autoRejectHandle(AutoRejectHandleHandleReq req) {
        log.info("开始执行自动驳回, request_no: {} flow_task_id: {}", req.getRequest_no(), req.getFlow_task_id());
        BusinFlowTask businFlowTask = businFlowTaskService.getById(req.getFlow_task_id());
        String typeValue = req.getTypEnum().getValue();
        if (StringUtils.isNotBlank(businFlowTask.getOperator_no())) {
            log.info("[自动驳回-{}]任务已分配操作员={}，无需处理，request_no={} flow_task_id={}", typeValue,
                    businFlowTask.getOperator_no(), req.getRequest_no(), businFlowTask.getSerial_id());
            return;
        }
        BusinFlowRequest businFlowRequest = businFlowRequestService.getById(req.getRequest_no());
        if (StringUtils.equals(businFlowTask.getPush_flag(), WskhConstant.ALREADY_PUSH_FLAG)
                && StringUtils.isNotBlank(businFlowTask.getDispatch_task_id())) {
            log.info("[自动驳回-{}]通知任务派单, request_no: {} flow_task_id: {}", typeValue, businFlowTask.getRequest_no(), businFlowTask.getSerial_id());
            // 派单取消
            aiDispatchAchieveService.cancelTask(businFlowTask.getDispatch_task_id(), WskhConstant.SYSTEM, "manual");
        }
        log.info("[自动驳回-{}]审核不通过同步更新原始数据 requestNo ={}", typeValue, req.getRequest_no());
        // 查询驳回原因
        List<RejectReason> rejectReasonList = req.getRejectReasonList();
        //转换成 map，方便数据查询
        Map<String, String> rejectReasonMap = rejectReasonList.stream().collect(Collectors.toMap(RejectReason::getReject_reason_id, RejectReason::getReject_reason_type));
        List<String> serialIdList = rejectReasonList.stream().map(RejectReason::getReject_reason_id).collect(Collectors.toList());
        LambdaUpdateWrapper<RepulsetReasonConfig> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.in(RepulsetReasonConfig::getSerial_id, serialIdList);
        List<DictInfo> dictInfos = cacheDict.getDictListByDictCode(DicConstant.REJECT_REASON_GROUP);
        Map<String, DictInfo> dictInfoMap = dictInfos.stream().collect(Collectors.toMap(DictInfo::getSub_code, dictInfo -> dictInfo));
        List<RepulsetReasonConfig> repulsetReasonConfigList = repulsetReasonConfigService.list(queryWrapper);
        List<AuditReason> auditReasonList = repulsetReasonConfigList.stream().map(item -> {
            AuditReason auditReason = new AuditReason();
            String rejectReasonType = rejectReasonMap.get(item.getSerial_id());
            auditReason.setReason_type(StringUtils.isBlank(rejectReasonType) ? WskhConstant.REJECT : rejectReasonType);
            auditReason.setReason_desc(item.getCause_content());
            auditReason.setReason_name(item.getCause_name());
            auditReason.setReason_group(dictInfoMap.getOrDefault(item.getCause_group(), new DictInfo()).getSub_name());
            return auditReason;
        }).collect(Collectors.toList());

        // 更新任务BusinFlowTask
        businFlowTask.setDeal_datetime(Objects.isNull(businFlowTask.getDeal_datetime()) ? new Date() : businFlowTask.getDeal_datetime());
        businFlowTask.setFinish_datetime(new Date());
        businFlowTask.setTask_status(FlowStatusConst.AUDIT_NO_PASS);
        businFlowTask.setOperator_no(WskhConstant.SUPER_USER);
        businFlowTask.setOperator_name(WskhConstant.SUPER_USER);
        businFlowTask.setOp_content(CollectionUtils.isNotEmpty(auditReasonList) ? JSON.toJSONString(auditReasonList) : " ");
        boolean taskUpdate = businFlowTaskService.updateById(businFlowTask);
        if (!taskUpdate) {
            log.warn("[自动驳回-{}]更新任务失败, request_no: {} flow_task_id: {}", typeValue, req.getRequest_no(), req.getFlow_task_id());
        }

        // 更新申请数据BusinFlowRequest
        businFlowRequest.setUpdate_datetime(new Date());
        businFlowRequest.setRequest_status(FlowStatusConst.REQUEST_STATUS_AUDIT_FAIL);
        businFlowRequest.setAnode_id(businFlowTask.getTask_type());
        businFlowRequest.setOperator_no(WskhConstant.SUPER_USER);
        boolean requestUpdate = businFlowRequestService.updateById(businFlowRequest);
        if (!requestUpdate) {
            log.warn("[自动驳回-{}]更新申请失败, request_no: {} flow_task_id: {}", typeValue, req.getRequest_no(), req.getFlow_task_id());
        }

        // 不通过更新审核跟踪记录表
        businProcessRequestAuditTrailService.updateBusinProcessRequestAuditTrail(businFlowTask, null,
                UpdateBusinProcessRequestAuditTrailSourceEnum.SOURCE_TASK_AUDITNOPASS);

        // 更新大字段BusinFlowParams
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(businFlowTask.getRequest_no());
        String auditReasonDesc = auditReasonList.stream().map(AuditReason::getReason_desc).collect(Collectors.joining(""));
        Map<String, Object> params = BeanMapUtil.beanToMap(clobContentInfo);
        params.put(Fields.OPERATOR_NO, WskhConstant.SUPER_USER);
        params.put(Fields.OPERATOR_NAME, WskhConstant.SUPER_USER);
        params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_RECORD_TYPE);
        params.put(Fields.ANODE_ID, businFlowTask.getTask_type());
        params.put(Fields.FINISH_NODE, FlowNodeConst.START + "," + businFlowTask.getTask_type());
        params.put(Fields.REQUEST_STATUS, FlowStatusConst.REQUEST_STATUS_AUDIT_FAIL);
        params.put(Fields.BUSINESS_FLAG, FlowRecordEnum.NOPASS.get(businFlowTask.getTask_type()).getValue());
        params.put(Fields.BUSINESS_REMARK, auditReasonDesc);
        params.put(Fields.COST_SECOND, DateUtil.between(businFlowTask.getDeal_datetime(), businFlowTask.getFinish_datetime(), DateUnit.SECOND));
        params.put(Fields.RECTIFICATION_ITEM, auditReasonList);
        params.put(Fields.UPDATE_DATETIME, KHDateUtil.formatDate(new Date(), KHDateUtil.DATE_TIME_FORMAT));
        businFlowParamsService.saveParams(businFlowRequest.getRequest_no(), params);

        // 保存任务流水信息
        saveOperatorRecord(req, auditReasonDesc);

        // 更新userExtQueryInfo
        UserQueryExtInfo userQueryExtInfo = userQueryExtInfoService.getById(businFlowTask.getRequest_no());
        if (Objects.nonNull(userQueryExtInfo)) {
            userQueryExtInfo.setRequest_status(FlowStatusConst.REQUEST_STATUS_AUDIT_FAIL);
            userQueryExtInfo.setAudit_operator_no(WskhConstant.SUPER_USER);
            userQueryExtInfo.setAudit_operator_name(WskhConstant.SUPER_USER);
            userQueryExtInfo.setAnode_id(businFlowTask.getTask_type());
            userQueryExtInfo.setUpdate_datetime(new Date());
            userQueryExtInfo.setAudit_finish_datetime(new Date());
            log.info("任务作废更新userQueryExtInfo表，request_no={}, serial_id={},更改信息{}", businFlowTask.getRequest_no(), businFlowTask.getSerial_id(), userQueryExtInfo);
            boolean userUpdate = userQueryExtInfoService.updateById(userQueryExtInfo);
            if (!userUpdate) {
                log.warn("[自动驳回-{}]更新userQueryExtInfo失败, request_no: {} flow_task_id: {}", typeValue, req.getRequest_no(), req.getFlow_task_id());
            }
        } else {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    redisTemplate.opsForList().leftPush(QueueConstant.QUERY_CLOB_QUEUE, businFlowTask.getRequest_no());
                }
            });
        }

        // 保存快照
        log.info("[自动驳回-{}]BUSINFLOWPARAMS-USERQUERYEXTINFO快照 requestNo ={}", typeValue, req.getRequest_no());
        businFlowParamsService.saveSnapshot(req.getRequest_no(), "notPass");
        userQueryExtInfoService.saveSnapshot(req.getRequest_no(), "notPass");

        // 驳回原因记录
        BaseUser baseUser = new BaseUser();
        baseUser.setStaff_no(WskhConstant.SUPER_USER);
        baseUser.setUser_name(WskhConstant.SUPER_USER);
        taskReasonRecordService.saveTaskReasonRecord(businFlowTask, auditReasonList, baseUser);

        // 保存自动驳回记录
        saveAutoRejectRecord(req);
        log.info("任务自动驳回完成, request_no: {} flow_task_id: {}", req.getRequest_no(), req.getFlow_task_id());

        // 异步通知
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                aiAuditThreadPoolTaskExecutor.execute(() -> {
                    log.info("[自动驳回]回调国泰通知开始------ requestNo ={}", req.getRequest_no());
                    BusinFlowTask businFlowTask = businFlowTaskService.getById(req.getFlow_task_id());
                    AuditResultNotifyReq auditResultNotifyReq = new AuditResultNotifyReq();
                    auditResultNotifyReq.setRequest_no(req.getRequest_no());
                    auditResultNotifyReq.setAnode_id(businFlowTask.getTask_type());
                    auditResultNotifyReq.setHandle_type("auditNotPass");
                    auditResultNotifyReq.setTask_id(req.getTask_id());
                    auditResultNotifyReq.setFlowtask_id(req.getFlow_task_id());
                    auditCallbackService.auditResultNotify(auditResultNotifyReq);
                    log.info("[自动驳回]回调国泰通知结束------ requestNo ={}", req.getRequest_no());
                });
            }
        });
    }

    /**
     * 保存自动驳回操作记录
     */
    public void saveOperatorRecord(AutoRejectHandleHandleReq req, String auditReasonDesc) {
        HashMap<String, Object> params = new HashMap<>();
        String typeValue = req.getTypEnum().getValue();
        params.put(Fields.OPERATOR_NO, WskhConstant.SUPER_USER);
        params.put(Fields.OPERATOR_NAME, WskhConstant.SUPER_USER);
        params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22408);
        params.put(Fields.BUSINESS_REMARK, "自动驳回-" + typeValue + "，驳回原因：" + auditReasonDesc);
        params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_OPERATE_TYPE);
        log.info("任务自动驳回{}, request_no: {} flow_task_id: {}, 操作记录: {}", typeValue,
                req.getRequest_no(), req.getFlow_task_id(), JSON.toJSONString(params));
        businFlowRecordService.saveBusinFlowRecord(req.getRequest_no(), params);
    }

    /**
     * 保存自动驳回记录
     */
    public void saveAutoRejectRecord(AutoRejectHandleHandleReq req) {
        AutoRejectRecord autoRejectRecord = new AutoRejectRecord();
        String typeValue = req.getTypEnum().getValue();
        autoRejectRecord.setSerial_id(idGenerator.nextUUID(null));
        autoRejectRecord.setRequest_no(req.getRequest_no());
        autoRejectRecord.setTask_id(req.getTask_id());
        autoRejectRecord.setFlow_task_id(req.getFlow_task_id());
        autoRejectRecord.setRecord_type(req.getTypEnum().getCode());
        autoRejectRecord.setMatch_info(JSON.toJSONString(req.getMatch_data()));
        autoRejectRecord.setCreate_datetime(new Date());
        log.info("自动驳回{}记录, request_no: {} flow_task_id: {}, 信息: {}", typeValue,
                req.getRequest_no(), req.getFlow_task_id(), JSON.toJSONString(autoRejectRecord));
        autoRejectRecordService.save(autoRejectRecord);
    }
}
