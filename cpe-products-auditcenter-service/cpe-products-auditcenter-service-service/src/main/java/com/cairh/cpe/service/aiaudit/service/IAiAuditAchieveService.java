package com.cairh.cpe.service.aiaudit.service;

import com.cairh.cpe.aiaudit.common.ImageCategory;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.response.QueryDishonestResult;
import com.cairh.cpe.service.aiaudit.request.AuditRuleQueryReq;
import com.cairh.cpe.service.aiaudit.response.AiAuditRuleResp;
import com.cairh.cpe.service.aiaudit.response.AuditAudioIdentityResp;
import com.cairh.cpe.service.aiaudit.response.AuditBusinRecordQueryResp;
import com.cairh.cpe.service.aiaudit.response.AuditRuleQueryResp;

import java.util.List;

public interface IAiAuditAchieveService {

    /**
     * 将所有基本信息进行智能审核
     */
    void asyncAiauditAll(String request_no);

    /**
     * 重试 对部分信息进行智能审核
     */
    List<AiAuditRuleResp> againExecuteAiaudit(String request_no, String ai_audit_group);

    /**
     * 网厅业务办理重试 对部分信息进行智能审核
     */
    List<AiAuditRuleResp> handAgainExecuteAiaudit(String request_no, String ai_audit_group);

    /**
     * 查询智能审核结果
     */
    List<AuditBusinRecordQueryResp> queryAuditBusinRecord(String request_no, List<String> item_identitys);

    /**
     * 身份证证件信息识别对比
     */
    List<AiAuditRuleResp> auditIdCard(String request_no, ClobContentInfo clob);

    /**
     * 外国人永居证证件信息识别对比
     */
    List<AiAuditRuleResp> auditForeignResidenceInfo(String request_no, ClobContentInfo clob);

    /**
     * 港澳台居民居住证证件信息识别对比
     */
    List<AiAuditRuleResp> auditGATResidenceInfo(String request_no, ImageCategory imageCategory, ClobContentInfo clob);

    /**
     * 港澳台居民来往内地通行证证件信息识别对比
     */
    List<AiAuditRuleResp> auditGATPassInfo(String request_no, ClobContentInfo clob);

    /**
     * 人像信息对比
     */
    List<AiAuditRuleResp> auditFace(String request_no, ClobContentInfo clob);

    /**
     * 视频审核
     */
    List<AiAuditRuleResp> auditVideo(String request_no, ClobContentInfo clob);

    /**
     * 语音发起
     */
    AuditAudioIdentityResp auditAudioIdentify(String request_no, ClobContentInfo clob);

    /**
     * 语音审核
     */
    List<AiAuditRuleResp> auditAudio(String request_no, ClobContentInfo clob);

    /**
     * 身份证质检
     */
    List<AiAuditRuleResp> auditImageQuality(String request_no, String image_no, ClobContentInfo clob);

    /**
     * 客户关键信息审核
     */
    List<AiAuditRuleResp> auditClient(String request_no, ClobContentInfo clob);

    /**
     * 按 子系统编号 + 业务编号 查询审核规则
     *
     * @param auditRuleQueryReq
     * @return
     */
    List<AuditRuleQueryResp> queryAuditRule(AuditRuleQueryReq auditRuleQueryReq);

    String getAuditCode(String request_no);


    /**
     * 失信记录
     *
     * @param request_no
     * @param clob
     */
    QueryDishonestResult getSecurityAlisaRpa(String request_no, ClobContentInfo clob);
}
