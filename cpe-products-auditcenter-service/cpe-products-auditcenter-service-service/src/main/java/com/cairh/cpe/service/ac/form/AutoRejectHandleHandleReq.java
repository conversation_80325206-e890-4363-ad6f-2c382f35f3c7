package com.cairh.cpe.service.ac.form;

import com.cairh.cpe.common.constant.AutoRejectSourceTypeEnum;
import com.cairh.cpe.common.dto.RejectReason;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/22 13:19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AutoRejectHandleHandleReq {

    /**
     * 请求编号
     */
    private String request_no;

    /**
     * 任务编号
     */
    private String task_id;

    /**
     * 任务唯一编号
     */
    private String flow_task_id;

    /**
     * 自动驳回来源类型
     * 1-基础数据 2-智能审核
     */
    private AutoRejectSourceTypeEnum typEnum;

    /**
     * 驳回原因
     */
    private List<RejectReason> rejectReasonList;

    /**
     * 规则编号
     */
    private String rule_id;

    /**
     * 规则名称
     */
    private String rule_name;

    /**
     * 匹配数据
     */
    private Object match_data;

}
