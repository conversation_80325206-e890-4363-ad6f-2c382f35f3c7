package com.cairh.cpe.service.ac.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class RepulsetReasonResp implements Serializable {

    /**
     * 流水号
     */
    private String serial_id;

    /**
     * 任务类别
     */
    private String audit_type;

    /**
     * 前端节点
     */
    private String anode_id;

    /**
     * 整改原因名称
     */
    private String cause_name;

    /**
     * 整改原因内容
     */
    private String cause_content;

    /**
     * 整改原因分组
     */
    private String cause_group;

    /**
     * 适用开户类型
     */
    private String en_open_type;

    /**
     * 顺序号
     */
    private Integer order_no;

    /**
     * 创建人
     */
    private String create_by;

    /**
     * 创建日期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;

    /**
     * 修改人
     */
    private String update_by;

    /**
     * 更新日期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date update_datetime;

    private List<RepulsetReasonResp> children_config;
}
