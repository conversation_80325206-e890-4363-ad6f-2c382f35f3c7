package com.cairh.cpe.task.schedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.PropKeyConstant;
import com.cairh.cpe.common.constant.TaskTypeEnum;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.util.DateUtils;
import com.cairh.cpe.service.aidispatch.service.IAiDispatchAchieveService;
import com.cairh.cpe.service.auditcenter.service.IVideoGroup;
import com.cairh.cpe.service.auditcenter.service.request.VideoUser;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 双向视频从库中查询数据推送给派单
 */
@Slf4j
@Component
public class PushBidirectionalDispatchScheduler {

    private final static int PAGE_SIZE = 5;
    private final static int CUR_PAGE = 1;
    @Resource
    private IBusinFlowTaskService businFlowTaskService;
    @Resource
    private IAiDispatchAchieveService aiDispatchAchieveService;
    @Resource
    private IVideoGroup<VideoUser> videoGroup;

    @XxlJob(value = "pushBidirectionalDispatchHandle")
    public void execute() {
        log.info("推送双向视频给派单系统开始---------");

        boolean openResult = PropertySource.getBoolean(PropKeyConstant.WSKH_ISOPEN_DISPATCH);
        if (!openResult) {
            log.info("推送功能未开启！");
            return;
        }
        String auditSerialId = " ";
        String reviewSerialId = " ";
        while (true) {
            List<BusinFlowTask> auditTasks = fetchTasks(TaskTypeEnum.AUDIT.getCode(), auditSerialId);
            List<BusinFlowTask> reviewTasks = fetchTasks(TaskTypeEnum.REVIEW.getCode(), reviewSerialId);

            boolean hasAuditTasks = !auditTasks.isEmpty();
            boolean hasReviewTasks = !reviewTasks.isEmpty();
            if (!hasAuditTasks && !hasReviewTasks) {
                log.info("双向视频任务为空！");
                break;
            }
            if (hasAuditTasks) {
                auditSerialId = auditTasks.get(auditTasks.size() - 1).getSerial_id();
            }
            if (hasReviewTasks) {
                reviewSerialId = reviewTasks.get(reviewTasks.size() - 1).getSerial_id();
            }
            processTasks(auditTasks);
            processTasks(reviewTasks);
        }
        log.info("推送双向视频给派单系统结束---------");
    }

    private void processTasks(List<BusinFlowTask> tasks) {
        for (BusinFlowTask task : tasks) {
            handleTask(task);
        }
    }

    private List<BusinFlowTask> fetchTasks(String type, String serialId) {
        Page<BusinFlowTask> pageTask = new Page<>(CUR_PAGE, PAGE_SIZE, Boolean.FALSE);
        LambdaQueryWrapper<BusinFlowTask> wrapper = getWrapper(type, serialId);
        Page<BusinFlowTask> page = businFlowTaskService.page(pageTask, wrapper);
        return page.getRecords();
    }

    private LambdaQueryWrapper<BusinFlowTask> getWrapper(String type, String serialId) {
        LambdaQueryWrapper<BusinFlowTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusinFlowTask::getPush_flag, WskhConstant.NO_PUSH_FLAG);
        wrapper.eq(BusinFlowTask::getOperator_no, " ");
        wrapper.eq(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_PENDING);
        wrapper.eq(BusinFlowTask::getVideo_type, WskhConstant.VIDEO_TYPE_2);

        if (StringUtils.equals(type, TaskTypeEnum.AUDIT.getCode())) {
            wrapper.eq(BusinFlowTask::getTask_type, FlowNodeConst.AUDIT);
        } else {
            int delayTime = PropertySource.getInt(PropKeyConstant.WSKH_PUSH_NOT_AUDIT_DELAYTIME, 30);
            wrapper.in(BusinFlowTask::getTask_type, com.google.common.collect.Lists.newArrayList(FlowNodeConst.REVIEW, FlowNodeConst.SECONDARY_REVIEW));
            wrapper.lt(BusinFlowTask::getCreate_datetime, DateUtils.addSeconds(new Date(), -delayTime));
        }
        wrapper.gt(BusinFlowTask::getSerial_id, serialId);
        wrapper.orderByAsc(BusinFlowTask::getSerial_id);
        return wrapper;
    }

    private void handleTask(BusinFlowTask task) {
        try {
            if (TaskTypeEnum.AUDIT.getCode().equals(task.getTask_type())) {
                processAuditTask(task);
            } else {
                aiDispatchAchieveService.handleDispatchTask(task.getRequest_no(), task.getSerial_id());
            }
        } catch (Exception e) {
            log.error("处理双向视频任务失败", e);
        }
    }

    private void processAuditTask(BusinFlowTask task) {
        String uniqueId = WskhConstant.SUBSYS_ID + task.getSerial_id();
        VideoUser user = videoGroup.query(uniqueId);
        if (Objects.nonNull(user)) {
            aiDispatchAchieveService.handleDispatchTask(task.getRequest_no(), task.getSerial_id());
        } else {
            log.info("双向视频任务 request_no = {} serial_id = {}  task_id {} 还未加入队列中！", task.getRequest_no(), task.getSerial_id(), task.getTask_id());
            try {
                TimeUnit.MILLISECONDS.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("双向视频任务线程被中断！", e);
            }
        }
    }

}