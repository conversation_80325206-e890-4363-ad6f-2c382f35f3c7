package com.cairh.cpe.task.schedule;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.support.TaskMessage;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.service.IBusinProcessRequestAuditTrailService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.common.util.KHDateUtil;
import com.cairh.cpe.common.util.RedissonUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * 转交任务 状态维护
 */
@Slf4j
@Component
public class TransferTaskStatusMaintainScheduler {

    @Autowired
    private IBusinFlowTaskService businFlowTaskService;
    @Autowired
    private IRequestService requestService;


    @Resource
    private RedissonUtil redissonUtil;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private IBusinProcessRequestAuditTrailService businProcessRequestAuditTrailService;


    @XxlJob(value = "transferTaskStatusMaintainHandler")
    public void execute() {

        int taskTransferDuration = PropertySource.getInt(PropKeyConstant.TASK_TRANSFER_FIRST_DURATION, 60) + 3;
        List<BusinFlowTask> businFlowTaskList = businFlowTaskService.lambdaQuery()
                .eq(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_TRANSFER).list();
        if (CollectionUtils.isEmpty(businFlowTaskList)) {
            return;
        }
        for (BusinFlowTask businFlowTask : businFlowTaskList) {
            returnBackTransferTask(businFlowTask, taskTransferDuration);
        }
    }


    private void returnBackTransferTask(BusinFlowTask businFlowTask, int taskTransferDuration) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, businFlowTask.getRequest_no());
        try {
            boolean isLock = redissonUtil.tryLock(lockKey, 2, 15, TimeUnit.SECONDS);
            if (!isLock) {
                log.info("转交业务处理，request_no={},serial_id={}", businFlowTask.getRequest_no(), businFlowTask.getSerial_id());
                return;
            }
            BusinFlowTask task = businFlowTaskService.getById(businFlowTask.getSerial_id());
            if (!StringUtils.equalsAny(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_TRANSFER)) {
                return;
            }
            ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(task.getRequest_no());
            if (StringUtils.isBlank(clobContentInfo.getTask_transfer_sponsor_time())) {
                return;
            }
            Date taskTransferSponsorDate = KHDateUtil.formatStrDateTime(clobContentInfo.getTask_transfer_sponsor_time(), KHDateUtil.DATE_TIME_FORMAT);
            Calendar sponsorCalendar = Calendar.getInstance();
            sponsorCalendar.setTime(taskTransferSponsorDate);
            sponsorCalendar.add(Calendar.SECOND, taskTransferDuration);

            Calendar curCalendar = Calendar.getInstance();
            curCalendar.setTime(new Date());
            if (sponsorCalendar.compareTo(curCalendar) < 0) {
                //长时间未响应
                log.info("转交业务长时间未响应,任务状态重置为待审核,request_no={},serial_id={}", businFlowTask.getRequest_no(), businFlowTask.getSerial_id());
                LambdaUpdateWrapper<BusinFlowTask> wrapper = new LambdaUpdateWrapper<>();
                // 双向视频见证任务，需要回滚状态为待审核
                if (StringUtils.equals(businFlowTask.getVideo_type(), WskhConstant.VIDEO_TYPE_2)
                        && StringUtils.equals(businFlowTask.getTask_type(), TaskTypeEnum.AUDIT.getCode())) {
                    wrapper.set(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_PENDING);
                } else {
                    wrapper.set(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_AUDITING);
                }
                wrapper.eq(BusinFlowTask::getSerial_id, task.getSerial_id());
                businFlowTaskService.update(wrapper);

                //发送弹框
                TaskMessage message = new TaskMessage();
                message.setOperator_no(businFlowTask.getOperator_no());
                message.setRequest_no(businFlowTask.getRequest_no());
                message.setMessage_content("对方长时间未回应！");
                redisTemplate.convertAndSend(Constant.CHANNEL_TRANSFER_TASK, JSON.toJSONString(message));

                // 更新业务流程信息
                businProcessRequestAuditTrailService.updateBusinProcessRequestAuditTrail(businFlowTask, null,
                        UpdateBusinProcessRequestAuditTrailSourceEnum.SOURCE_TASK_TRANSFERNOACCEPT);
            }

        } catch (Exception e) {
            log.error("转交业务处理失败,request_no={},serial_id={}", businFlowTask.getRequest_no(), businFlowTask.getSerial_id(), e);
        } finally {
            redissonUtil.unlock(lockKey);
        }


    }
}