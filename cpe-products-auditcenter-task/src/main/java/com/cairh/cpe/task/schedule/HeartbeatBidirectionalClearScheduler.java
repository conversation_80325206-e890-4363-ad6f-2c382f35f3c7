package com.cairh.cpe.task.schedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.service.auditcenter.service.IBidirectionalVideoHandlerService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * Description：双向视频心跳监控停止任务作废处理
 * Author： slx
 * Date： 2024/7/17 下午3:55
 */
@Slf4j
@Component
public class HeartbeatBidirectionalClearScheduler {

    @Resource
    private RedisTemplate<Object, Object> redisTemplate;
    @Resource
    private IBidirectionalVideoHandlerService bidirectionalVideoHandlerService;
    @Resource
    private IBusinFlowTaskService businFlowTaskService;

    /**
     * 每15s执行一次
     */
    @XxlJob(value = "heartbeatBidirectionalClear")
    public void execute() {
        log.info("heartbeatBidirectionalClear开始-双向视频心跳停止作废任务！");
        // 查询处理中的双向视频任务
        LambdaQueryWrapper<BusinFlowTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_AUDITING)
                .eq(BusinFlowTask::getTask_type, TaskTypeEnum.AUDIT.getCode())
                .eq(BusinFlowTask::getVideo_type, WskhConstant.VIDEO_TYPE_2);
        List<BusinFlowTask> list = businFlowTaskService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            log.info("heartbeatBidirectionalClear没有需要处理的双向视频任务!");
        }
        LambdaQueryWrapper<BusinFlowTask> invalidQueryWrapper = new LambdaQueryWrapper<>();
        invalidQueryWrapper.eq(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_PENDING)
                .eq(BusinFlowTask::getTask_type, TaskTypeEnum.AUDIT.getCode())
                .eq(BusinFlowTask::getVideo_type, WskhConstant.VIDEO_TYPE_2)
                .eq(BusinFlowTask::getOperator_no, " ")
                .eq(BusinFlowTask::getPush_flag, WskhConstant.NO_PUSH_FLAG);
        List<BusinFlowTask> invalidTaskList = businFlowTaskService.list(invalidQueryWrapper);
        if (CollectionUtils.isEmpty(invalidTaskList)) {
            log.info("heartbeatBidirectionalClear没有需要作废的双向视频待处理任务!");
        }
        // 处理双向视频心跳任务
        if (CollectionUtils.isNotEmpty(list)) {
            int heartbeatTimeoutTime = PropertySource.getInt(PropKeyConstant.WSKH_BIDIRECTIONAL_HEARTBEAT_TIMEOUT_TIME, 30);

            // 获取所有在执行双向视频心跳任务
            Set<ZSetOperations.TypedTuple<Object>> heartTasks =
                    redisTemplate.opsForZSet().rangeWithScores(RedisKeyConstant.WSKH_AC_BIDIRECTIONAL_TASK_HEARTBEAT, 0, -1);

            list.forEach(businFlowTask -> {
                try {
                    // 如果心跳任务列表为空
                    if (CollectionUtils.isEmpty(heartTasks)) {
                        // 处理任务，使用正常超时时间
                        handleTaskIfTimeDifference(businFlowTask, businFlowTask.getDeal_datetime(), heartbeatTimeoutTime);
                    } else {
                        // 查找与当前任务序列号匹配的心跳任务
                        ZSetOperations.TypedTuple<Object> matchingHeartTask = heartTasks
                                .stream()
                                .filter(heartTask -> Objects.equals(heartTask.getValue(), businFlowTask.getSerial_id()))
                                .findFirst()
                                .orElse(null);

                        // 如果找到了匹配的心跳任务
                        if (matchingHeartTask != null) {
                            // 处理任务，使用特定超时时间
                            handleTaskIfTimeDifference(businFlowTask, new Date(Objects.requireNonNull(matchingHeartTask.getScore()).longValue()), heartbeatTimeoutTime);
                        } else {
                            // 没有找到匹配的心跳任务，处理任务，使用正常超时时间
                            handleTaskIfTimeDifference(businFlowTask, businFlowTask.getDeal_datetime(), heartbeatTimeoutTime);
                        }
                    }
                } catch (Exception e) {
                    log.error("heartbeatBidirectionalClear处理任务时出现异常", e);
                }
            });
        }

        // 处理双向视频待处理任务
        if (CollectionUtils.isNotEmpty(invalidTaskList)) {
            // 双向视频待处理任务作废超时时间
            int invalidTimeoutTime = PropertySource.getInt(PropKeyConstant.WSKH_BIDIRECTIONAL_INVALID_TASK_TIME, 120);
            invalidTaskList.forEach(businFlowTask -> {
                try {
                    boolean timeDifference = calculateTimeDifference(businFlowTask.getCreate_datetime(), invalidTimeoutTime);
                    // 双向视频待处理任务作废
                    if (timeDifference) {
                        // 当时间差超过timeout时，任务作废
                        bidirectionalVideoHandlerService.clearBidirectionalVideoTask(businFlowTask, VideoConstant.BIDIRECTIONAL_CLEAR_TIMEOUT_HANDLE);
                    }
                } catch (Exception e) {
                    log.error("heartbeatBidirectionalClear双向视频待处理任务作废时出现异常", e);
                }
            });
        }
        log.info("heartbeatBidirectionalClear结束-双向视频心跳停止作废任务！");
    }

    /**
     * 根据时间差处理任务
     * 如果当前时间与任务的处理时间差超过指定超时值，则执行相应操作
     *
     * @param businFlowTask 业务流程任务对象，包含任务的相关信息
     * @param timeout       超时时间阈值，用于判断时间差是否超过允许的范围
     */
    private void handleTaskIfTimeDifference(BusinFlowTask businFlowTask, Date date, int timeout) {
        log.info("heartbeatBidirectionalClear心跳超时作废任务，request_no={} serial_id={} ", businFlowTask.getRequest_no(), businFlowTask.getSerial_id());
        // 判断任务的处理时间与当前时间是否存在超过timeout的时间差
        boolean timeDifference = calculateTimeDifference(date, timeout);
        if (timeDifference) {
            // 当时间差超过timeout时，任务作废
            bidirectionalVideoHandlerService.clearBidirectionalVideoTask(businFlowTask, VideoConstant.BIDIRECTIONAL_CLEAR_HEARTBEAT_TIMEOUT);
            // 从Redis的有序集合中移除该任务
            redisTemplate.opsForZSet().remove(RedisKeyConstant.WSKH_AC_BIDIRECTIONAL_TASK_HEARTBEAT, businFlowTask.getSerial_id());
        }
    }

    /**
     * 计算当前时间与指定时间之间的差值是否超过允许的超时时间
     *
     * @param date        指定的时间日期对象
     * @param timeoutTime 允许的时间差值（以秒为单位）
     * @return 如果时间差值的绝对值超过timeoutTime，则返回true；否则返回false
     */
    private boolean calculateTimeDifference(Date date, int timeoutTime) {
        Instant specifiedTime = date.toInstant();
        // 计算当前时间与指定时间之间的时间差，结果以秒为单位
        long differenceInSeconds = ChronoUnit.SECONDS.between(Instant.now(), specifiedTime);
        // 判断时间差的绝对值是否超过允许的超时时间
        return Math.abs(differenceInSeconds) > timeoutTime;
    }

}
