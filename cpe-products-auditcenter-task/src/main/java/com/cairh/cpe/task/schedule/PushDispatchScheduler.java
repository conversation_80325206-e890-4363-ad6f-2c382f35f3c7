package com.cairh.cpe.task.schedule;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.businflow.service.impl.AutoRejectRuleHandleStrategy;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.AutoRejectSourceTypeEnum;
import com.cairh.cpe.common.constant.LockKeyConstant;
import com.cairh.cpe.common.constant.PropKeyConstant;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.dto.BaseRuleInfo;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.common.util.DateUtils;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.service.ac.form.AutoRejectHandleHandleReq;
import com.cairh.cpe.service.ac.service.IAutoRejectHandleService;
import com.cairh.cpe.service.aidispatch.service.IAiDispatchAchieveService;
import com.google.common.collect.Lists;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 从库中查询数据推送给派单
 */
@Slf4j
@Component
public class PushDispatchScheduler {

    @Resource
    private IBusinFlowTaskService businFlowTaskService;
    @Resource
    private IAiDispatchAchieveService aiDispatchAchieveService;
    @Resource
    private AutoRejectRuleHandleStrategy autoRejectRuleHandleStrategy;
    @Resource
    private IRequestService requestService;
    @Resource
    private RedissonUtil redissonUtil;
    @Resource
    private IAutoRejectHandleService autoRejectHandleService;

    @XxlJob(value = "pushDispatchHandle")
    public void excute() {
        log.info("推送给派单系统开始---------");

        boolean open_result = PropertySource.getBoolean(PropKeyConstant.WSKH_ISOPEN_DISPATCH);
        // 基础信息-自动驳回开关
        boolean auto_reject_result = PropertySource.getBoolean(PropKeyConstant.WSKH_BASEINFO_AUTO_REJECT_SWITCH);
        int delayTime = PropertySource.getInt(PropKeyConstant.WSKH_PUSH_DELAYTIME, 60);
        if (open_result) {
            int cur_page = 1;
            int page_size = 20;
            while (true) {
                LambdaQueryWrapper<BusinFlowTask> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(BusinFlowTask::getPush_flag, WskhConstant.NO_PUSH_FLAG);
                wrapper.eq(BusinFlowTask::getOperator_no, " ");
                wrapper.eq(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_PENDING);
                wrapper.eq(BusinFlowTask::getVideo_type, WskhConstant.VIDEO_TYPE_1);
                wrapper.nested(wr -> wr.eq(BusinFlowTask::getTask_type, FlowNodeConst.AUDIT)
                        .lt(BusinFlowTask::getCreate_datetime, DateUtils.addSeconds(new Date(), -delayTime))
                        .or()
                        .in(BusinFlowTask::getTask_type, Lists.newArrayList(FlowNodeConst.REVIEW, FlowNodeConst.SECONDARY_REVIEW)));
                wrapper.orderByAsc(BusinFlowTask::getCreate_datetime);
                Page<BusinFlowTask> pageTask = new Page<>(cur_page, page_size, Boolean.FALSE);
                Page<BusinFlowTask> page = businFlowTaskService.page(pageTask, wrapper);
                if (CollectionUtils.isEmpty(page.getRecords())) {
                    break;
                }
                for (BusinFlowTask record : page.getRecords()) {
                    try {
                        if (auto_reject_result) {
                            // 任务自动驳回规则校验
                            AutoRejectHandleHandleReq autoRejectHandleHandleReq = autoRejectRuleCheckHandle(record);
                            if (autoRejectHandleHandleReq != null) {
                                baseInfoAutoRejectHandle(autoRejectHandleHandleReq);
                            }
                        }
                        aiDispatchAchieveService.handleDispatchTask(record.getRequest_no(), record.getSerial_id());
                    } catch (Exception e) {
                        log.error("推送给派单任务 serial_id={} 出错：{}", record.getSerial_id(), e.getMessage(), e);
                    }
                }
            }
        }
        log.info("推送给派单系统结束---------");
    }

    /**
     * 基础数据自动驳回规则校验
     */
    private AutoRejectHandleHandleReq autoRejectRuleCheckHandle(BusinFlowTask businFlowTask) {
        //公安照
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(businFlowTask.getRequest_no());
        BaseRuleInfo baseRuleInfo = autoRejectRuleHandleStrategy.ruleMatch(businFlowTask, clobContentInfo);
        if (Objects.isNull(baseRuleInfo)) {
            return null;
        }
        AutoRejectHandleHandleReq autoRejectHandleHandleReq = new AutoRejectHandleHandleReq();
        autoRejectHandleHandleReq.setTypEnum(AutoRejectSourceTypeEnum.BASE_INFO);
        autoRejectHandleHandleReq.setRule_id(baseRuleInfo.getSerial_id());
        autoRejectHandleHandleReq.setRule_name(baseRuleInfo.getRule_name());
        autoRejectHandleHandleReq.setRejectReasonList(baseRuleInfo.getRejectReasonList());
        autoRejectHandleHandleReq.setMatch_data(businFlowTask);
        autoRejectHandleHandleReq.setRequest_no(businFlowTask.getRequest_no());
        autoRejectHandleHandleReq.setTask_id(businFlowTask.getTask_id());
        autoRejectHandleHandleReq.setFlow_task_id(businFlowTask.getSerial_id());
        return autoRejectHandleHandleReq;
    }

    /**
     * 基础数据自动驳回处理
     */
    private void baseInfoAutoRejectHandle(AutoRejectHandleHandleReq req) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_REQUEST_NO, req.getRequest_no());
        boolean isLock = redissonUtil.tryLock(lockKey, 0, 10, TimeUnit.SECONDS);
        if (!isLock) {
            log.info("[自动驳回-基础数据]未获取到锁，request_no={}", req.getRequest_no());
            return;
        }
        try {
            // 任务查询
            BusinFlowTask businFlowTask = businFlowTaskService.getById(req.getFlow_task_id());
            if (Objects.isNull(businFlowTask)) {
                log.warn("[自动驳回-基础数据]任务不存在，request_no={}", req.getRequest_no());
                return;
            }
            if (!StringUtils.equals(businFlowTask.getTask_status(), FlowStatusConst.AUDIT_PENDING)) {
                log.warn("[自动驳回-基础数据]任务状态不是待审核，request_no={}", req.getRequest_no());
                return;
            }
            // 自动驳回数据处理
            autoRejectHandleService.autoRejectHandle(req);
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }

}
