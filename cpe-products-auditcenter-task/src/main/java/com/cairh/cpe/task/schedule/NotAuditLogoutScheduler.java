package com.cairh.cpe.task.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.common.constant.PropKeyConstant;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.support.NoAuditLogoutMessage;
import com.cairh.cpe.common.mapper.QcTaskMapper;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.cairh.cpe.common.util.DateUtils;
import com.cairh.cpe.esb.base.rpc.IVBaseOnlineUserDubboService;
import com.cairh.cpe.esb.base.rpc.IVBaseUserLoginJourDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseUserLoginJourRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.OnlineUserResponse;
import com.cairh.cpe.esb.base.rpc.dto.resp.PageResponse;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseUserLoginJourResponse;
import com.google.common.collect.Lists;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 超过一定时间未有审核完成信息退出登录，重新登陆后 重新计时
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class NotAuditLogoutScheduler {


    @DubboReference(check = false, lazy = true)
    private IVBaseOnlineUserDubboService baseOnlineUserDubboService;

    @DubboReference(check = false, lazy = true)
    private IVBaseUserLoginJourDubboService baseUserLoginJourDubboService;

    @Resource
    private IBusinFlowTaskService businFlowTaskService;

    @Resource
    private QcTaskMapper qcTaskMapper;

    @Autowired
    private RedisTemplate<String,String> redisTemplate;


    @XxlJob(value = "notAuditLogoutScheduler")
    public void execute() {

        List<OnlineUserResponse> onlineUserResponseList = baseOnlineUserDubboService.onlineUserInfo();
        if (CollectionUtils.isEmpty(onlineUserResponseList)) {
            log.info("current  No online user");
            return;
        }
        // 时间阈值
        int logoutTime = PropertySource.getInt(PropKeyConstant.WSKH_LOGOUT_MINUTE, 60);
        Date limitDate = DateUtils.addMinutes(new Date(), -logoutTime);
        List<String> staffUsers = Lists.newArrayList();
        for (OnlineUserResponse onlineUserResponse : onlineUserResponseList) {
            //1 获取用户的最后登录时间
            VBaseUserLoginJourRequest request = new VBaseUserLoginJourRequest();
            request.setLogin_type("S");
            request.setStaff_no(onlineUserResponse.getStaff_no());
            request.setCurr_page(1);
            request.setPage_size(5);
            PageResponse<VBaseUserLoginJourResponse> jourResponses = baseUserLoginJourDubboService.queryUserLoginJour(request);
            if (CollectionUtils.isEmpty(jourResponses.getRecords())) {
                continue;
            }
            Date loginTime = DateUtils.parseDateStrictly(jourResponses.getRecords().get(0).getLogin_time(), DateUtils.DATE_TIME_FORMAT);
            if (loginTime.before(limitDate)) {
                staffUsers.add(onlineUserResponse.getStaff_no());
            }
        }

        if (CollectionUtils.isEmpty(staffUsers)) {
            log.info("current  No online user");
            return;
        }

        String white_staff_no = PropertySource.get(PropKeyConstant.WSKH_AUDIT_STAFF_WHITE,"");
        if (StringUtils.isNotBlank(white_staff_no)) {
            Set<String> white_staff =  Arrays.stream(white_staff_no.split(",")).collect(Collectors.toSet());
            staffUsers = staffUsers.stream().filter(item -> !white_staff.contains(item)).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(staffUsers)) {
            log.info("exist white staff  filter after current  No online user");
            return;
        }

        LambdaQueryWrapper<BusinFlowTask> taskLambdaQueryWrapper = new LambdaQueryWrapper<>();
        taskLambdaQueryWrapper.select(BusinFlowTask::getOperator_no);
        taskLambdaQueryWrapper.in(BusinFlowTask::getTask_status, Lists.newArrayList(FlowStatusConst.AUDIT_PASS, FlowStatusConst.AUDIT_NO_PASS));
        taskLambdaQueryWrapper.gt(BusinFlowTask::getFinish_datetime, limitDate);
        taskLambdaQueryWrapper.in(BusinFlowTask::getOperator_no, staffUsers);
        List<String> operator_nos = businFlowTaskService.list(taskLambdaQueryWrapper).stream().map(BusinFlowTask::getOperator_no).distinct().collect(Collectors.toList());

        log.info("ac exist audit task operator_nos list ={}",JSON.toJSONString(operator_nos));
        List<String> notAuditOperator = staffUsers.stream().filter(item -> !operator_nos.contains(item)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notAuditOperator)) {
            log.info("current  No  user need logout");
            return;
        }

        //质检任务审核人列表
        List<String> qcAuditOperator =  qcTaskMapper.selectQcTaskOperators(notAuditOperator,limitDate);
        log.info("qcAuditOperator list ={}",JSON.toJSONString(qcAuditOperator));

        // 发送websocket
        notAuditOperator.stream().filter(item -> !qcAuditOperator.contains(item)).forEach(e -> {
            //发送websocket 消息
            NoAuditLogoutMessage logoutMessage = new NoAuditLogoutMessage();
            logoutMessage.setStaff_no(e);
            logoutMessage.setOperator_no(e);
            logoutMessage.setOp_command(1002);
            redisTemplate.convertAndSend(Constant.CHANNEL_TRANSFER_TASK, JSON.toJSONString(logoutMessage));
            log.info("发送websocket消息通知前端调用登出操作,staff_no={}", e);
        });


    }
}
