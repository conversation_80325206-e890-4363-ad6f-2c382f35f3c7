package com.cairh.cpe.task.schedule;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.PropKeyConstant;
import com.cairh.cpe.common.constant.TaskTypeEnum;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.service.IBusinFlowTaskService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 回收双向视频见证长时间未处理的任务
 */
@Slf4j
@Component
public class RecycleBidirectionalTaskScheduler extends RecyleTaskScheduler {

    @Resource
    private IBusinFlowTaskService businFlowTaskService;

    @Override
    @XxlJob(value = "recycleBidirectionalTaskHandler")
    public void execute() {
        int bidirectionalRecycleTime = PropertySource.getInt(PropKeyConstant.WSKH_BIDIRECTIONAL_RECYLE_TASK_TIME, 30);
        if (PropertySource.getBoolean(PropKeyConstant.WSKH_ISOPEN_AUTO_RESYCLE)) {
            log.info("回收双向视频待审核待审核的任务开始---------");
            recycleTask(bidirectionalRecycleTime);
            log.info("回收双向视频待审核待审核的任务结束---------");
        }
    }

    public void recycleTask(int recycleTime) {
        int page_size = 5;
        //回收策略不包含 双向视频
        LambdaQueryWrapper<BusinFlowTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.ne(BusinFlowTask::getOperator_no, " ")
                .eq(BusinFlowTask::getTask_status, FlowStatusConst.AUDIT_PENDING)
                .lt(BusinFlowTask::getDeal_datetime, DateUtils.addSeconds(new Date(), -recycleTime))
                .eq(BusinFlowTask::getVideo_type, WskhConstant.VIDEO_TYPE_2)
                .eq(BusinFlowTask::getTask_type, TaskTypeEnum.AUDIT.getCode())
                .orderByDesc(BusinFlowTask::getCreate_datetime);
        while (true) {
            Page<BusinFlowTask> pageTask = new Page<>(1, page_size, Boolean.FALSE);
            Page<BusinFlowTask> page = businFlowTaskService.page(pageTask, wrapper);
            if (CollectionUtils.isEmpty(page.getRecords())) {
                break;
            }
            for (BusinFlowTask businFlowTask : page.getRecords()) {
                tasKSingleHandle(businFlowTask, FlowStatusConst.AUDIT_PENDING);
            }
            if (CollectionUtils.isEmpty(page.getRecords()) || page.getRecords().size() < page_size) {
                break;
            }
        }
    }

}
