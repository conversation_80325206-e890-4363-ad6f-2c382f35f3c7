plugins {
    id 'war'
    id 'org.springframework.boot'
}

archivesBaseName = 'cpe-products-auditcenter-task'

dependencies {
    api(project(":cpe-products-auditcenter-common"))
    api(project(":cpe-products-auditcenter-cache"))
    api(project(":cpe-products-auditcenter-businflow:cpe-products-auditcenter-businflow-core"))
    api(project(":cpe-products-auditcenter-businflow:cpe-products-auditcenter-businflow-service"))
    api(project(":cpe-products-auditcenter-service:cpe-products-auditcenter-service-core"))
    api(project(":cpe-products-auditcenter-service:cpe-products-auditcenter-service-service"))

    api("com.cairh:cpe-common-backend")
    api("com.cairh:cpe-trace")

    api('org.springframework.boot:spring-boot-starter-web')
    api('org.springframework.boot:spring-boot-starter-actuator')

    api('org.apache.dubbo:dubbo')
    api('org.apache.dubbo:dubbo-spring-boot-starter')
    api('com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery')
    api('com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config')
    api('org.springframework.cloud:spring-cloud-starter-consul-discovery')
    api('org.springframework.cloud:spring-cloud-starter-consul-config')

    api('com.cairh:cpe-job-core')
    api('org.apache.commons:commons-dbcp2')
    api('com.alibaba.spring:spring-context-support')
}
