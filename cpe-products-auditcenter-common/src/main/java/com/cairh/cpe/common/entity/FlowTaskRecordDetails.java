package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.lang.reflect.Field;
import java.util.Date;

/**
 * Description：流程任务记录详情表
 * Author： slx
 * Date： 2024/4/25 下午2:52
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("FlowTaskRecordDetails")
public class FlowTaskRecordDetails {

    /**
     * 请求编号
     */
    private String request_no;

    /**
     * 任务id
     */
    private String task_id;

    /**
     * task流水号
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 时间属性,进件时间 1-工作日 2-非工作日
     */
    private String time_property;

    /**
     * 操作用户编号
     */
    private String operator_no;

    /**
     * 操作用户名称
     */
    private String operator_name;

    /**
     * 操作员营业部编号
     */
    private String operator_branch_no;

    /**
     * 操作员上级用户营业部编号
     */
    private String operator_up_branch_no;

    /**
     * 用户营业部编号
     */
    private String branch_no;

    /**
     * 用户所属分公司
     */
    private String branch_name;

    /**
     * 上级用户营业部编号
     */
    private String up_branch_no;

    /**
     * 上级用户所属分公司
     */
    private String up_branch_name;

    /**
     * 见证申请时间：BusinFlowRequest表中的request_datetime
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date request_datetime;

    /**
     * 任务创建时间：BusinFlowTask表中的create_datetime
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date task_datetime;

    /**
     * 任务类型
     */
    private String task_type;

    /**
     * 任务编号
     */
    private String task_status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;

    /**
     * 处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date deal_datetime;

    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date finish_datetime;

    /**
     * 暂存标志 0-未暂存 1-暂存过
     */
    private String pause_flag;

    /**
     * 首次暂存时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date first_pause_datetime;

    /**
     * 首次处理操作员
     */
    private String first_deal_operator_no;

    /**
     * 首次处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date first_deal_datetime;

    /**
     * 最后处理操作员
     */
    private String end_deal_operator_no;

    /**
     * 最后处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date end_deal_datetime;

    /**
     * 是否托管分支机构 0-托管分支机构 1-非托管分支机构
     */
    private String is_branch_managed;

    /**
     * 绿通标识
     */
    private String white_flag;

    /**
     * 绿通时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date white_datetime;

    /**
     * 任务作废标识 0-正常  1-作废
     */
    private String invalid_flag;

    /**
     * 见证完成时间-只有复核任务才会有值
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date audit_finish_datetime;

    /**
     * 业务类型
     */
    private String busin_type;

    /**
     * 首次暂存操作员
     */
    private String first_pause_operator_no;

    /**
     * 视频见证类型
     */
    private String video_type;

    public void setFieldValue(String fieldName, Object value) throws NoSuchFieldException, IllegalAccessException {
        // 获取字段对应的Field对象
        Field field = FlowTaskRecordDetails.class.getDeclaredField(fieldName);
        // 设置访问权限，以便可以修改私有字段
        field.setAccessible(true);
        // 将值设置到对应的字段
        field.set(this, value);
    }
}
