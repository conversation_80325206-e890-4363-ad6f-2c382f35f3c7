package com.cairh.cpe.common.constant;

public class WskhConstant {

    /**
     * 集中审核
     */
    public static final String SUBSYS_ID = "24";

    /**
     * 系统处理传递给智能派单的操作员
     */
    public static final String AUTO_DISPATCH_OPERATOR_NO = " ";
    /**
     * 网开业务编号 100058
     */
    public static final String BUSIN_TYPE_NORMAL = "100058";
    /**
     * 跨境理财通业务 100100
     */
    public static final String CROSS_BUSIN_TYPE = "100100";

    /**
     * 见证开户-普通证券账户 100910
     */
    public static final String NORMAL_SECURITIES_ACCOUNT_BUSIN_TYPE = "100910";
    /**
     * 见证开户-普通资金账户 100920
     */
    public static final String ONLY_NORMAL_FUND_ACCOUNT_BUSIN_TYPE = "100920";
    /**
     * 见证开户-信用账户 100930
     */
    public static final String CREDIT_SECURITIES_ACCOUNT_BUSIN_TYPE = "100930";
    /**
     * 网厅业务办理业务类型合集
     */
    public static final String[] HAND_BUSIN_TYPE_COLLECTION =
            {WskhConstant.NORMAL_SECURITIES_ACCOUNT_BUSIN_TYPE, WskhConstant.ONLY_NORMAL_FUND_ACCOUNT_BUSIN_TYPE,
                    WskhConstant.CREDIT_SECURITIES_ACCOUNT_BUSIN_TYPE};
    /**
     * 绿通标识
     */
    public static final String WHITE_FLAG_LABEL = "********";

    /**
     * 预约开户业务 100150
     */
    public static final String APPOINTMENT_BUSIN_TYPE = "100150";
    /**
     * 预约开户类型
     */
    public static final String APPOINTMENT_OPEN_TYPE = "9";
    /**
     * 基金开户业务 100151
     */
    public static final String FUND_BUSIN_TYPE = "100151";

    /**
     * 网开流程编号 1150
     */
    public static final String BUSINFLOW_NO = "1150";

    /**
     * 工作流ID
     */
    public static final String FLOW_TYPE = "WSKH";
    /**
     * 机构标志个人
     */
    public static final String ORGAN_FLAG = "0"; //

    public static final String ACCOUNT_SUIT_STOCK = "100017";// 股东匹配结果
    public static final String ACCOUNT_SUIT_FUND = "100018";// 基金匹配结果
    public static final String ACCOUNT_SUIT_100150 = "100150";// 场内基金账户
    /**
     * 视频类型单向
     */
    public static final String VIDEO_TYPE_1 = "1";
    /**
     * 视频类型双向
     */
    public static final String VIDEO_TYPE_2 = "2";

    /**
     * 柜台类型 ifs
     */
    public static final String COUNTER_TYPE_IFS = "ifs";
    /**
     * 柜台类型 t2
     */
    public static final String COUNTER_TYPE_T2 = "t2";
    /**
     * 柜台类型 gd
     */
    public static final String COUNTER_TYPE_GD = "gd";

    /**
     * 普通短信类型
     */
    public static final String SMS_APPLY_TYPE_0 = "0";
    /**
     * 断点短信类型
     */
    public static final String SMS_APPLY_TYPE_1 = "1";

    public static final String SMS_APPLY_TYPE_2 = "2";

    /**
     * 模板类型 2 通用
     */
    public static final String SMS_MODEL_TYPE_2 = "2";
    /**
     * 模板类型 1 定制
     */
    public static final String SMS_MODEL_TYPE_1 = "1";

    /**
     * 短信发送类型 0 短信
     */
    public static final String SMS_SEND_TYPE_0 = "0";
    /**
     * 短信发送类型 1 语音
     */
    public static final String SMS_SEND_TYPE_1 = "1";

    /**
     * 正在复核的操作员列表在redis中的键
     */
    public static final String EMP_REVIEWING = "emp_reviewing";

    public static final String TASK_TYPE_1 = "1";// 初审核
    public static final String TASK_TYPE_6 = "6";// 二次核查

    public static final String REPLY_STATUS_0 = "0";// 0:无回报
    public static final String REPLY_STATUS_1 = "1";// 1:有回报

    public static final String OCR_SFZ = "SFZ";// 身份证ocr

    public static final String OCR_GATTXZ = "GATTXZ";// 港澳台通行证ocr

    public static final String OCR_GATJZZ = "GATJZZ";// 港澳台居住证ocr

    public static final String IMAGE_NO_6A = "6A"; // 身份证正面
    public static final String IMAGE_NO_6B = "6B"; // 身份证反面
    public static final String IMAGE_NO_7C = "7C"; // 港澳居民居住证正面
    public static final String IMAGE_NO_7D = "7D"; // 港澳居民居住证反面
    public static final String IMAGE_NO_7E = "7E"; // 港澳来往内地通行证正面
    public static final String IMAGE_NO_7F = "7F"; // 港澳来往内地通行证反面
    public static final String IMAGE_NO_7I = "7I"; // 台湾居民居住证正面
    public static final String IMAGE_NO_7J = "7J"; // 台湾居民居住证反面
    public static final String IMAGE_NO_7K = "7K"; // 台湾来往内地通行证正面
    public static final String IMAGE_NO_7L = "7L"; // 台湾来往内地通行证反面
    public static final String IMAGE_NO_80 = "80";// 大头照
    public static final String IMAGE_NO_82 = "82";// 公案照

    public static final String IMAGE_NO_8K = "8K"; // 港澳台居住证证明文件

    /*主证件类型：0身份证 1港澳台居住证 2港澳台通行证*/
    public static final String ID_TYPE_0 = "0";
    public static final String ID_TYPE_1 = "1";
    public static final String ID_TYPE_2 = "2";

    /*辅证件类型：0港澳台居住证 1居住证证明文件 2港澳台通行证*/
    public static final String ASSIST_TYPE_0 = "0";
    public static final String ASSIST_TYPE_1 = "1";
    public static final String ASSIST_TYPE_2 = "2";

    /**
     * 系统工作时间类型
     */
    public static final String WORK_TIME_VIDEO = "1";        // 视频见证时间
    public static final String WORK_TIME_ZD_KH = "2";        // 中登开户时间
    public static final String WORK_TIME_ZD_QUERY = "3";    // 中登查询时间
    public static final String WORK_TIME_BANK = "4";        // 银行工作时间
    public static final String WORK_TIME_FUND = "5";        // 基金公司工作时间
    public static final String WORK_TIME_T2 = "6";            // 柜台工作时间
    public static final String WORK_TIME_FH = "7";       // 复核工作时间
    public static final String WORK_TIME_WH = "8";        // 系统维护时间
    public static final String WORK_TIME_VIDEO_NOT_TRANS = "a";        // 视频见证（非交易日）
    public static final String WORK_TIME_FIX = "b";        // 顶点柜台工作时间
    public static final String WORK_TIME_KESB = "e"; // 金证柜台工作时间
    public static final String WORK_TIME_XJB = "f"; // 现金宝工作时间
    public static final String WORK_TIME_ZD_GA = "g"; // 中登公安时间
    public static final String WORK_TIME_AUDIT_WORKING = "h"; // 见证人工作时间


    public static final String CLOUD_SHARE = "1";    // 云分享类型
    public static final String LE_SHARE = "2";        // 乐分享类型

    /**
     * 注册/短信验证
     */
    public static final String BUSINESS_FLAG_12100 = "12100";
    public static final String BUSINESS_FLAG_12101 = "12101";// 下载安全控件
    public static final String BUSINESS_FLAG_12102 = "12102";// 实名验证选择
    public static final String BUSINESS_FLAG_12103 = "12103";// 银行验证
    public static final String BUSINESS_FLAG_12104 = "12104";// 视频验证
    public static final String BUSINESS_FLAG_12105 = "12105";// 安装CA证书
    public static final String BUSINESS_FLAG_22106 = "22106";// 完善用户资料
    public static final String BUSINESS_FLAG_22107 = "22107";// 证件上传
    public static final String BUSINESS_FLAG_22108 = "22108";// 选择投资品种
    public static final String BUSINESS_FLAG_22109 = "22109";// 协议签署
    public static final String BUSINESS_FLAG_22110 = "22110";// 风险评测
    public static final String BUSINESS_FLAG_22111 = "22111";// 选择营业部
    public static final String BUSINESS_FLAG_22112 = "22112";// 设置密码
    public static final String BUSINESS_FLAG_22113 = "22113";// 指定存管银行
    public static final String BUSINESS_FLAG_22115 = "22115";// 回访
    public static final String BUSINESS_FLAG_22116 = "22116";// 协议保存
    public static final String BUSINESS_FLAG_22117 = "22117";// 港股通评测
    public static final String BUSINESS_FLAG_22118 = "22118";// 用户登陆
    public static final String BUSINESS_FLAG_22119 = "22119";// 佣金确认

    public static final String BUSINESS_FLAG_33210 = "33210";// 预开户成功
    public static final String BUSINESS_FLAG_33211 = "33211";// 预开户失败
    public static final String BUSINESS_FLAG_33901 = "33901";// 账户开立成功
    public static final String BUSINESS_FLAG_33902 = "33902";// 账户开立失败

    public static final String BUSINESS_FLAG_33905 = "33905";// 股东账户预开户成功
    public static final String BUSINESS_FLAG_33906 = "33906";// 股东账户预开户失败
    public static final String BUSINESS_FLAG_33907 = "33907";// 基金账户预开户成功
    public static final String BUSINESS_FLAG_33908 = "33908";// 基金账户预开户失败
    public static final String BUSINESS_FLAG_33213 = "33213";// 开户成功
    public static final String BUSINESS_FLAG_33214 = "33214";// 开户失败
    public static final String BUSINESS_FLAG_33401 = "33401";// 账户等待激活
    public static final String BUSINESS_FLAG_33402 = "33402";// 账户激活失败
    public static final String BUSINESS_FLAG_33230 = "33230";// 天添利开户成功
    public static final String BUSINESS_FLAG_33231 = "33231";// 天添利开户失败
    public static final String BUSINESS_FLAG_33232 = "33232";// 存管协议签署
    public static final String BUSINESS_FLAG_33233 = "33233";// 获取单项视频验证码
    public static final String BUSINESS_FLAG_33500 = "33500";// 录制单向视频

    public static final String BUSINESS_FLAG_22120 = "22120";// 开户初审通过
    public static final String BUSINESS_FLAG_22121 = "22121";// 开户初审驳回
    public static final String BUSINESS_FLAG_22122 = "22122";// 风险评测结果
    public static final String BUSINESS_FLAG_22123 = "22123";// 个人税收居民身份确认
    public static final String BUSINESS_FLAG_22124 = "22124";// 辅助证件上传
    public static final String BUSINESS_FLAG_22125 = "22125";// 风险揭示书协议签署
    public static final String BUSINESS_FLAG_22126 = "22126";// 匹配确认书协议签署
    public static final String BUSINESS_FLAG_22127 = "22127";// 不匹配确认书协议签署
    public static final String BUSINESS_FLAG_22128 = "22128";// 风评结果告知函协议签署
    public static final String BUSINESS_FLAG_22129 = "22129";// 强制激活
    public static final String BUSINESS_FLAG_22130 = "22130";// 适当性匹配详情确认
    public static final String BUSINESS_FLAG_22131 = "22131";// 观看风险揭示视频
    public static final String BUSINESS_FLAG_22132 = "22132";// 初审超时自动通过
    public static final String BUSINESS_FLAG_22133 = "22133";// 开始初审审核
    public static final String BUSINESS_FLAG_22134 = "22134";// 股东账户补开
    public static final String BUSINESS_FLAG_22135 = "22135";// 身份证信息识别
    public static final String BUSINESS_FLAG_22136 = "22136";// 人脸识别对比
    public static final String BUSINESS_FLAG_22137 = "22137";// 创业板转签登记
    public static final String BUSINESS_FLAG_22138 = "22138";// 创业板转签成功
    public static final String BUSINESS_FLAG_22139 = "22139";// 营销经纪关系确认
    public static final String BUSINESS_FLAG_22140 = "22140";// 人脸识别
    public static final String BUSINESS_FLAG_22141 = "22141";// 视频补录
    public static final String BUSINESS_FLAG_22142 = "22142";// 新意档案同步
    public static final String BUSINESS_FLAG_22143 = "22143";// 兴业银行理财过渡账户协议签署
    public static final String BUSINESS_FLAG_33903 = "33903";// 债券逆回购权限开通
    public static final String BUSINESS_FLAG_22144 = "22144";// 客户须知协议签署
    public static final String BUSINESS_FLAG_22145 = "22145";// 客户确认个人信息
    public static final String BUSINESS_FLAG_22146 = "22146";// 隐私政策协议签署
    public static final String BUSINESS_FLAG_22147 = "22147";// 开户模式切换
    public static final String BUSINESS_FLAG_22148 = "22148";// 中登查询
    public static final String BUSINESS_FLAG_22149 = "22149";// 经纪人客户二次开发
    public static final String BUSINESS_FLAG_22150 = "22150";// 投顾服务开通
    public static final String BUSINESS_FLAG_22151 = "22151";// 投顾服务取消
    public static final String BUSINESS_FLAG_22152 = "22152";// 道琼斯名单检测

    public static final String BUSINESS_FLAG_22156 = "22156";// 恢复开户
    public static final String BUSINESS_FLAG_22157 = "22157";// 二次核查
    public static final String BUSINESS_FLAG_22158 = "22158";// 撤销隐私政策协议

    public static final String BUSINESS_FLAG_22169 = "22169"; // 客户端中登查询
    public static final String BUSINESS_FLAG_22170 = "22170"; // 视频见证中登查询
    public static final String BUSINESS_FLAG_22171 = "22171"; // 审核中登查询
    public static final String BUSINESS_FLAG_22172 = "22172"; // 复核中登查询
    public static final String BUSINESS_FLAG_22173 = "22173"; // 智能审核中登查询
    public static final String BUSINESS_FLAG_22174 = "22174"; // 自动中登查询
    public static final String BUSINESS_FLAG_22175 = "22175"; // 重新问卷回访
    public static final String BUSINESS_FLAG_22176 = "22176"; // 预审通过
    public static final String BUSINESS_FLAG_22177 = "22177"; // 电话回访成功
    public static final String BUSINESS_FLAG_22178 = "22178"; // 电话回访失败
    public static final String BUSINESS_FLAG_23001 = "23001";// 审核等待时长耗时：
    public static final String BUSINESS_FLAG_23002 = "23002";// 审核时长耗时
    public static final String BUSINESS_FLAG_23003 = "23003";// 复核等待时长耗时
    public static final String BUSINESS_FLAG_23004 = "23004";// 复核时长耗时
    public static final String BUSINESS_FLAG_22181 = "22181";// 失效渠道重置用户信息
    public static final String BUSINESS_FLAG_22182 = "22182";// 活体检测
    public static final String BUSINESS_FLAG_22183 = "22183";// 回访电话呼叫接口调用
    public static final String BUSINESS_FLAG_22184 = "22184";// 触发电话回访
    public static final String BUSINESS_FLAG_22185 = "22185";// 电话回访整改
    public static final String BUSINESS_FLAG_22186 = "22186";// 取消深A开户
    public static final String BUSINESS_FLAG_22187 = "22187";// 取消沪A开户
    public static final String BUSINESS_FLAG_22188 = "22188";// 审核超时取消
    public static final String BUSINESS_FLAG_22189 = "22189";// 复核超时取消
    public static final String BUSINESS_FLAG_22190 = "22190";// 回访超时取消
    public static final String BUSINESS_FLAG_22191 = "22191";// 视频整改确认
    public static final String BUSINESS_FLAG_22192 = "22192";// 电话回访开始
    public static final String BUSINESS_FLAG_22193 = "22193";// 异地开户确认
    public static final String BUSINESS_FLAG_22194 = "22194";// 坐席电子名片确认
    public static final String BUSINESS_FLAG_22195 = "22195";// 修改渠道
    public static final String BUSINESS_FLAG_22196 = "22196";// 修改营业部
    public static final String BUSINESS_FLAG_22197 = "22197";// 取消电话回访
    public static final String BUSINESS_FLAG_22198 = "22198";// 投顾签约成功
    public static final String BUSINESS_FLAG_22199 = "22199";// 客户取消签约
    public static final String BUSINESS_FLAG_22200 = "22200";// 无需投顾签约
    public static final String BUSINESS_FLAG_22201 = "22201";// 推送审核任务
    public static final String BUSINESS_FLAG_22202 = "22202";// 推送复核任务
    public static final String BUSINESS_FLAG_22203 = "22203";// 推送投顾任务
    public static final String BUSINESS_FLAG_22204 = "22204";// 开始视频录制
    public static final String BUSINESS_FLAG_22205 = "22205";// 开始活体检测
    public static final String BUSINESS_FLAG_22206 = "22206";// 活体检测失败
    public static final String BUSINESS_FLAG_22207 = "22207";// 活体检测成功
    public static final String BUSINESS_FLAG_22208 = "22208";// 人脸识别成功
    public static final String BUSINESS_FLAG_22209 = "22209";// 人脸识别失败
    public static final String BUSINESS_FLAG_22210 = "22210";// 视频上传失败
    public static final String BUSINESS_FLAG_22211 = "22211";// 重新电话回访
    public static final String BUSINESS_FLAG_22212 = "22212";// 放弃电话回访
    public static final String BUSINESS_FLAG_25001 = "25001";// 上传页面完成
    public static final String BUSINESS_FLAG_22213 = "22213";// 用户作废
    public static final String BUSINESS_FLAG_22214 = "22214";// 诚信记录查询
    public static final String BUSINESS_FLAG_22215 = "22215";// 异常重试
    public static final String BUSINESS_FLAG_22216 = "22216";// 回访驳回
    public static final String BUSINESS_FLAG_22217 = "22217";// 选择理财账户
    public static final String BUSINESS_FLAG_22218 = "22218";// 取消理财账户
    public static final String BUSINESS_FLAG_22220 = "22220";// 推荐人确认
    public static final String BUSINESS_FLAG_22221 = "22221";// 报价回购权限开通
    public static final String BUSINESS_FLAG_22222 = "22222";// 暂不处理
    public static final String BUSINESS_FLAG_22223 = "22223";// 下次调查
    public static final String BUSINESS_FLAG_22224 = "22224";// 开户主证件选择
    public static final String BUSINESS_FLAG_22225 = "22225";// 证件信息提交
    public static final String BUSINESS_FLAG_22226 = "22226";// 取消账户新开
    public static final String BUSINESS_FLAG_22228 = "22228";// 开户辅证件选择
    public static final String BUSINESS_FLAG_22229 = "22229";// 客户整改
    public static final String BUSINESS_FLAG_REVIEW_FAIL = "40013";// 复核未通过
    public static final String BUSINESS_FLAG_BREAK_POINT_SMS = "40016";// 断点跟踪短信
    //    public static final String BUSINESS_FLAG_52232 = "52232";//公安认证
//    public static final String BUSINESS_FLAG_52233 = "52233";//手机实名制认证
    public static final String BUSINESS_FLAG_52234 = "52234";// 图片旋转
    public static final String BUSINESS_FLAG_52235 = "52235";// 图片交换
    public static final String BUSINESS_FLAG_52236 = "52236";// 图片保存
    public static final String BUSINESS_FLAG_52237 = "52237";// 一键替换大头照
    public static final String BUSINESS_FLAG_52238 = "52238";// 电话回访试卷提交
    public static final String BUSINESS_FLAG_52239 = "52239";// 诚信图片上传
    // public static final String BUSINESS_FLAG_52240 = "52240";//删除诚信图片
//    public static final String BUSINESS_FLAG_22241 = "22241";//开户重试
    public static final String BUSINESS_FLAG_22242 = "22242";// 重新提交柜台
    public static final String BUSINESS_FLAG_22243 = "22243";// 归档流水申请
    public static final String BUSINESS_FLAG_22244 = "22244";// 重点监控账户
    public static final String BUSINESS_FLAG_22241 = "22241";// 写黑名单
    public static final String BUSINESS_FLAG_22246 = "22246";// 外国人预约信息
    public static final String BUSINESS_FLAG_22247 = "22247";// 重开银行
    public static final String BUSINESS_FLAG_22248 = "22248";// 推送cc
    public static final String BUSINESS_FLAG_22249 = "22249";// 回访延后
    public static final String BUSINESS_FLAG_22250 = "22250";// cc 通话流水
    public static final String BUSINESS_FLAG_22261 = "22261";// 现金宝重新激活
    public static final String BUSINESS_FLAG_22262 = "22262";// 养老金重新激活
    public static final String BUSINESS_FLAG_22263 = "22263";// 养老金开通成功
    public static final String BUSINESS_FLAG_22264 = "22264";// 养老金开通失败
    public static final String BUSINESS_FLAG_22265 = "22265";// 现金宝开通成功
    public static final String BUSINESS_FLAG_22266 = "22266";// 现金宝开通失败
    //    public static final String BUSINESS_FLAG_22267 = "22267";//辅助证件类型选择
    public static final String BUSINESS_FLAG_22268 = "22268";// 销户重开

    // xpe
    public static final String BUSINESS_FLAG_22231 = "22231";// 延后回访
    public static final String BUSINESS_FLAG_22232 = "22232";// 回访任务推送CC
    public static final String BUSINESS_FLAG_22233 = "22233";// 回访备注
    public static final String BUSINESS_FLAG_22234 = "22234";// CC电话回访成功
    public static final String BUSINESS_FLAG_22235 = "22235";// CC电话回访失败
    public static final String BUSINESS_FLAG_22236 = "22236";// CC电话回访延后
    // xpe公安
    public static final String BUSINESS_FLAG_22237 = "22237";// 前端公安认证
    public static final String BUSINESS_FLAG_22238 = "22238";// 视频公安认证
    public static final String BUSINESS_FLAG_22239 = "22239";// 审核公安认证
    public static final String BUSINESS_FLAG_22240 = "22240";// 复核公安认证

    /*回访预审*/
    public static final String BUSINESS_FLAG_22251 = "22251";// 开始预审
    public static final String BUSINESS_FLAG_22252 = "22252";// 预审通过
    public static final String BUSINESS_FLAG_22253 = "22253";// 预审不通过
    public static final String BUSINESS_FLAG_22254 = "22254";// 取消预审
    public static final String BUSINESS_FLAG_22255 = "22255";// 超时取消预审
    public static final String BUSINESS_FLAG_22256 = "22256";// 视频手机实名验证
    public static final String BUSINESS_FLAG_22257 = "22257";// 审核手机实名验证
    public static final String BUSINESS_FLAG_22258 = "22258";// 复核手机实名验证
    public static final String BUSINESS_FLAG_22259 = "22259";// 前端手机实名验证
    public static final String BUSINESS_FLAG_22260 = "22260";// 权限开通 (静态佣金权限开通)
    public static final String BUSINESS_FLAG_22270 = "22270";// 推荐营业部
    public static final String BUSINESS_FLAG_22271 = "22271";// 开户异常处理
    public static final String BUSINESS_FLAG_22272 = "22272";// 办理退回复核
    public static final String BUSINESS_FLAG_22273 = "22273";// 客户业务协议签署
    public static final String BUSINESS_FLAG_22274 = "22274";// 风险名单证明文件操作
    public static final String BUSINESS_FLAG_22275 = "22275";// 诚信档案删除
    public static final String BUSINESS_FLAG_22277 = "22277";// 回访异常
    public static final String BUSINESS_FLAG_22278 = "22278";// 主辅证件手动切换
    public static final String BUSINESS_FLAG_22281 = "22281";// 开始整改
    public static final String BUSINESS_FLAG_22282 = "22282";// 整改通过
    public static final String BUSINESS_FLAG_22283 = "22283";// 整改任务超时回收
    public static final String BUSINESS_FLAG_22284 = "22284";// 拦截客户批量驳回
    public static final String BUSINESS_FLAG_22285 = "22285";// 拦截客户自动驳回

    // ----------------------------dic字典-----------------------
    /**
     * 开立账户
     */
    public static final String DIC_1002 = "account_type";
    /**
     * 证件类别 id_kind
     */
    public static final String DIC_1011 = "id_kind";
    /**
     * 性别  client_gender
     */
    public static final String DIC_1012 = "client_gender";
    /**
     * 国籍 nationality_code
     */
    public static final String DIC_1013 = "nationality_code";
    /**
     * 用户学历 degree_code
     */
    public static final String DIC_1014 = "degree_code";
    /**
     * 用户职业 profession_code
     */
    public static final String DIC_1015 = "profession_code";
    public static final String DIC_1025 = "1025";
    /**
     * 行业类别 industry_code
     */
    public static final String DIC_1019 = "industry_code";
    /**
     * 年收入 year_income
     */
    public static final String DIC_1020 = "year_income";
    /**
     * 本地转中登
     */
    public static final String DICT_TRANS_KIND_0 = "0";
    /**
     * 本地转柜台
     */
    public static final String DICT_TRANS_KIND_1 = "1";
    /**
     * 本地转新意
     */
    public static final String DICT_TRANS_KIND_2 = "2";
    /**
     * 本地转光大
     */
    public static final String DICT_TRANS_KIND_3 = "3";
    /**
     * 本地转恒生
     */
    public static final String DICT_TRANS_KIND_4 = "4";
    /**
     * 本地转t2
     */
    public static final String DICT_TRANS_KIND_5 = "5";
    /**
     * 本地转金证直销柜台
     */
    public static final String DICT_TRANS_KIND_6 = "6";
    /**
     * 本地转BOP
     */
    public static final String DICT_TRANS_KIND_7 = "7";

    /**
     * 预约时间
     */
    public static final String DIC_1022 = "1022";
    /**
     * 房产价值
     */
    public static final String DIC_1023 = "1023";
    /**
     * 入市资金来源
     */
    public static final String DIC_11313 = "11313";
    /**
     * 民族 nation_code
     */
    public static final String DIC_1033 = "nation_code";
    /**
     * 反洗钱风险等级
     */
    public static final String DIC_1038 = "1038";
    /**
     * 社会关系 socialral_type
     */
    public static final String DIC_1068 = "socialral_type";
    /**
     * 接入方式 app_id
     */
    public static final String DIC_1084 = "app_id";
    /**
     * 自然人身份类别 natural_person_type
     */
    public static final String DIC_10011 = "natural_person_type";
    /**
     * 无法提供纳税人识别号原因  no_revenue_number_reason
     */
    public static final String DIC_NO_REVENUE_NUMBER_REASON = "no_revenue_number_reason";
    /**
     * 职务 duty_code
     */
    public static final String DIC_11301 = "duty_code";
    /**
     * 不良诚信记录来源 dishonest_type
     */
    public static final String DIC_11302 = "dishonest_type";
    /**
     * 其它职业列 other_profession_code
     */
    public static final String DIC_11303 = "other_profession_code";
    /**
     * 金融类资产 finance_asset
     */
    public static final String DIC_11306 = "finance_asset";
    /**
     * 机动车价值 car_worth
     */
    public static final String DIC_11307 = "car_worth";
    /**
     * 产品风险等级 product_risk_level
     */
    public static final String DIC_11505 = "product_risk_level";
    /**
     * 投资人风险等级 user_risk_level
     */
    public static final String DIC_11519 = "user_risk_level";
    /**
     * 客户风险承受能力 user_risk_level
     */
    public static final String DIC_13003 = "user_risk_level";

    /**
     * 投资期限 invest_term
     */
    public static final String DIC_13008 = "invest_term";

    /**
     * 投资类别 invest_kind
     */
    public static final String DIC_13009 = "invest_kind";
    /**
     * 个人税收居民身份类别 cust_tax_type
     */
    public static final String DIC_11312 = "cust_tax_type";

    /**
     * 家庭年收入 home_year_income
     */
    public static final String DIC_11329 = "home_year_income";

    /**
     * 诚信信息查询相关
     */
    public static final String DIC_11314 = "11314";

    /**
     * 非本人开户其他原因
     */
    public static final String DIC_11316 = "11316";
    /**
     * 收入来源
     */
    public static final String DIC_11315 = "income_source";

    public static final String DIC_11318 = "11318"; // 投资品种

    public static final String DIC_11319 = "11319"; // 投资年限

    public static final String DIC_11320 = "11320"; // 资金来源
    public static final String DIC_11321 = "11321"; // 投资期限
    public static final String DIC_11322 = "11322"; // 期望投资回报率
    public static final String DIC_11323 = "11323"; // 管理人关联方
    public static final String DIC_11325 = "11325"; // 投资品种
    public static final String DIC_11326 = "11326"; // 行业是否与期货交易品种有关
    public static final String DIC_11327 = "11327"; // 参与期货的主要交易类型
    public static final String DIC_11328 = "11328"; // 是否存在债务
    /**
     * 万联证券下拉工作单位字典值 work_unit_type
     */
    public static final String DIC_11332 = "11332";
    public static final String DIC_11324 = "11324"; // 异地开户确认

    /**
     * common_status  状态 8 正常 9失效
     */
    public static final String DIC_common_status = "common_status";

    /**
     * 顶点webservice接口定义
     */
    public static final String cifwsMedia_Upload = "cifwsMedia_Upload";// 影像管理_上传影像
    public static final String cifwsFXCKH_KHSQ = "cifwsFXCKH_KHSQ";// 非现场股东开户_开户申请
    public static final String cifCXFXCKHSQ = "cifCXFXCKHSQ";// cif查询非现场开户申请
    public static final String cifCXFXCKHZGYJ = "cifCXFXCKHZGYJ";// cif查询非现场开户整改意见
    public static final String cifQueryKHYWSQLS = "cifQueryKHYWSQLS";// cif查询开户业务申请流水
    public static final String cifwsZDYZ_ZDZHCX = "cifwsZDYZ_ZDZHCX";// 中登验证_中登账户查询
    public static final String cifQueryZDGDCXJG = "cifQueryZDGDCXJG";// cif查询中登股东查询结果

    public static final String AC_KAFKA_TOPIC = "G_P_USER_JZXT2MSG";

    /**
     * 中登字典
     */
    public static final String CSDC_ACODE_STATUS_NORMAL = "0"; // 一码通账户状态-正常
    public static final String CSDC_ACODE_STATUS_CANCEL = "1"; // 一码通账户状态-注销

    public static final String CSDC_HOLDER_STATUS_NORMAL = "00"; // 中登证券状态-正常
    public static final String CSDC_HOLDER_STATUS_DORMANT = "03";// 中登证券状态-休眠
    public static final String CSDC_HOLDER_STATUS_CANCEL = "04"; // 中登证券状态-注销

    public static final String AUDIT_REVISIT_BACK = "3";// 电话回访整改
    public static final String REVIEW_REVISIT_BACK = "6";// 复核打回电话回访整改
    public static final String REVIEW_BACK = "7";// 退回审核整改

    //系统管理员
    public static final String SUPER_USER = "系统管理员";

    //系统管理员
    public static final String SYSTEM = "system";

    public static final String SUPER_BRANCH = "总部";


    // rpa 任务状态

    public static final String RPASTATUS_0 = "0";/*已创建*/

    public static final String RPASTATUS_ING = "1";/*执行中*/

    public static final String RPASTATUS_SUCCESS = "2";/*执行成功*/

    /*public static final String BUSINESS_FLAG_22381 = "22381";// rpa自动查询

    public static final String BUSINESS_FLAG_22382 = "22382";// 操作员手动上传*/

    //websocket消息类型

    //提醒消息
    public static final String REMIND_MESSAGE = "remind_task";/*提醒消息toast*/

    //提醒消息
    public static final String REMIND_MESSAGE_POPUP = "remind_task_popup";/*提醒消息弹窗*/

    //任务提醒消息
    public static final String DEAL_MESSAGE = "task_transfer_timeout";/*处理消息*/

    //任务转交
    public static final String TASK_TRANSFER = "task_transfer";/*任务转交*/


    //任务回收
    public static final String TASK_RECYLE = "task_recyle";/*任务回收*/

    public static final String NO_AUDIT_LOGOUT = "no_audit_logout";

    public static final String BUSINESS_FLAG_22114 = "22114";// 提交见证申请


    // 开户结果尚未回调
    public static final String FLOW_REQUEST_INFORM_NOT_YET = "0";

    // 开户结果回调完成
    public static final String FLOW_REQUEST_INFORM_FINISH = "1";


    // 中登时间待推送
    public static final String NEED_PUSH_FLAG = "1";

    // 已推送智能派单
    public static final String ALREADY_PUSH_FLAG = "8";

    // 未推送智能派单
    public static final String NO_PUSH_FLAG = "9";

    // 暂存状态
    public static final String STAGING_PUSH_FLAG = "7";

    // 不满足参加派单条件的用户和派单未开启未参加派单的用户
    public static final String BLANK_PUSH_FLAG = " ";


    // 智能派单业务标识

    public static final String BUSINESS_FLAG_22383 = "22383";// 智能派单

    // 智能审核业务标识

    public static final String BUSINESS_FLAG_22384 = "22384";// 智能审核

    // 视频话术常量


    // 更新节点信息
    public static final String BUSINESS_FLAG_22385 = "22385";// 更新节点信息

    // 更新流程标志
    //public static final String BUSINESS_FLAG_22386 = "22386";// 更新流程标志

    // 公安认证
    public static final String BUSINESS_FLAG_22387 = "22387";// 公安认证

    // 失信记录
    public static final String BUSINESS_FLAG_22388 = "22388";// 失信记录


    // 任务移交发起
    public static final String BUSINESS_FLAG_22389 = "22389";

    // 任务移交接受
    public static final String BUSINESS_FLAG_22390 = "22390";

    // 任务移交拒绝
    public static final String BUSINESS_FLAG_22391 = "22391";

    // 任务回收
    public static final String BUSINESS_FLAG_22392 = "22392";

    // 任务信息推送
    public static final String BUSINESS_FLAG_22393 = "22393";

    //强制通过
    public static final String BUSINESS_FLAG_22394 = "22394"; //人脸识别分数过低强制通过

    //挂起
    public static final String BUSINESS_FLAG_22395 = "22395"; //挂起

    //删除智能审核项不通过原因
    public static final String BUSINESS_FLAG_22396 = "22396"; //挂起
    //派单分配流水
    public static final String BUSINESS_FLAG_22397 = "22397";

    // RCPMIS
    public static final String BUSINESS_FLAG_22398 = "22398";// RCPMIS
    // 复审无公安照非中登时间回收
    public static final String BUSINESS_FLAG_22399 = "22399";// 上传视频
    // 见证作废
    public static final String BUSINESS_FLAG_22400 = "22400";// 见证作废
    // 双向视频复核申请
    public static final String BUSINESS_FLAG_22401 = "22401";// 见证作废
    //kafka 异步同步
    public static final String BUSINESS_FLAG_22402 = "22402";
    // 派单模式切换 数据重置
    public static final String BUSINESS_FLAG_22403 = "22403";
    // 刷新视频以及音频
    public static final String BUSINESS_FLAG_22404 = "22404";
    // 手动暂存
    public static final String BUSINESS_FLAG_22405 = "22405";
    // 手动释放
    public static final String BUSINESS_FLAG_22406 = "22406";
    // 自动暂存
    public static final String BUSINESS_FLAG_22407 = "22407";
    // 自动驳回
    public static final String BUSINESS_FLAG_22408 = "22408";


    //开始派发状态
    public static final String OPEN_DISPATCH = "1";
    //结束派发状态
    public static final String CLOSE_DISPATCH = "0";

    //有效工作量
    public static final String EFFECTIVE_WORK = "1";
    //无效工作量
    public static final String INVALID_WORK = "0";

    public static final String DICT_TASK_STATUS = "task_status";

    // 正常状态
    public static final String NORMAL_STATUS = "0";
    // 作废状态
    public static final String INVALID_STATUS = "1";
    // 数据标签
    public static final String DATA_SIGN_STATUS = "1";

    // T2接口状态：成功
    public static final String STATUS_SUCCESS = "1";
    // T2接口状态：失败
    public static final String STATUS_FAIL = "0";
    // T2接口比对分值，成功默认100
    public static final String FACE_SCORE_SUCCESS = "100";
    // T2接口比对分值，失败默认1
    public static final String FACE_SCORE_FAIL = "1";
    // 可用状态
    public static final String AVAILABLE_STATUS = "1";
    // 不可用状态
    public static final String UNAVAILABLE_STATUS = "0";
    // 认证结果状态-成功
    public static final String AUTH_RESULT_STATUS_SUCCESS = "1";
    // 认证结果状态-失败
    public static final String AUTH_RESULT_STATUS_FAIL = "0";
    // 数据导出分页数量
    public static final int DATA_EXPORT_PAGE_SIZE = 1000;
    // 营运中心
    public static final String SPECIAL_BRANCH = "3";
    // 复核通过
    public static final String REVIEW_3 = "review-3";
    // 二次复核通过
    public static final String SECONDARY_REVIEW_3 = "secondary_review-3";
    // 自主选择驳回原因
    public static final String REJECT = "reject";
    // 智能审核驳回原因
    public static final String AIAUDIT_REJECT = "aiaudit_reject";
    // 自定义驳回原因
    public static final String PRIVATE_REJECT = "private_reject";

}