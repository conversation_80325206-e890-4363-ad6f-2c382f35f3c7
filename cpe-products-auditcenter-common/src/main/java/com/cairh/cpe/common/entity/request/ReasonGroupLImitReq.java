package com.cairh.cpe.common.entity.request;


import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ReasonGroupLImitReq {

    /**
     * 原因类型
     */
    @NotBlank(message = "原因类型不能为空")
    private String audit_type;

    /**
     * 业务类型
     */
    @NotBlank(message = "业务类型不能为空")
    private String busin_type;

    /**
     * 证件类型
     */
    @NotBlank(message = "证件类型不能为空")
    private String id_kind;
}
