package com.cairh.cpe.common.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 规则信息base
 *
 * <AUTHOR>
 * @since 2025/5/21 17:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BaseRuleInfo implements Serializable {

    /**
     * 主键ID
     */
    private String serial_id;

    /**
     * 规则名称
     */
    private String rule_name;

    /**
     * 规则类型（1-暂存任务 2-自动驳回）
     */
    private String rule_type;

    /**
     * 规则开始时间
     */
    private Date rule_datetime_start;

    /**
     * 规则结束时间
     */
    private Date rule_datetime_end;

    /**
     * 任务类型
     */
    private String task_type;

    /**
     * 自动驳回类型
     * 1-基础数据 2-智能审核
     */
    private String reject_type;

    /**
     * 业务类型
     */
    private String busin_type;

    /**
     * 证件类型
     */
    private String id_kind;

    /**
     * 驳回原因
     */
    private String reject_reason;

    /**
     * 驳回原因
     */
    private List<RejectReason> rejectReasonList;

    /**
     * 可选规则
     */
    private String expression;

    /**
     * 关联智能审核项
     */
    private String aiaudit_items;

    /**
     * 关联智能审核驳回原因
     */
    private String aiaudit_reject_reason;

    /**
     * 状态（1-可用，0-禁用）
     */
    private String status;

    /**
     * 顺序，优先级
     */
    private Integer order_no;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建人
     */
    private String create_by;

    /**
     * 创建时间
     */
    private Date create_datetime;

    /**
     * 更新人
     */
    private String update_by;

    /**
     * 更新时间
     */
    private Date update_datetime;
}
