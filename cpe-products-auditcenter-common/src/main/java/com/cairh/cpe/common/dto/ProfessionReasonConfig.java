package com.cairh.cpe.common.dto;

import lombok.Data;

import java.util.List;

/**
 * Description：年龄+职业备注编码动态配置原因
 * Author： slx
 * Date： 2024/10/25 16:11
 */
@Data
public class ProfessionReasonConfig {

    /**
     * 年龄
     */
    private int age;

    /**
     * 年龄表达式 大于:> 小于:< 等于: = 大于等于:>= 小于等于:<=
     */
    private String ageExpression;

    /**
     * 职业编号
     */
    private List<String> professionCode;

    /**
     * 理由编号 逗号分隔
     */
    private List<String> reasonCode;

    /**
     * 性别 0:男 1:女
     */
    private String sex;

}
