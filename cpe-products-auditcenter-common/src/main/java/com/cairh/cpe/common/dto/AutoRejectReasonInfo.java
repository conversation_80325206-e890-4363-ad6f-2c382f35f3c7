package com.cairh.cpe.common.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @since 2025/6/12 09:51
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AutoRejectReasonInfo {

    /**
     * drl规则名称
     */
    private String drl_rule_name;

    /**
     * 驳回原因
     */
    private String reject_reason;
}
