package com.cairh.cpe.common.constant;

public enum StagingRuleTypeEnum {

    STAGING_TASK("1", "暂存任务"),

    AUTO_REJECT("2", "自动驳回");

    private String code;
    private String value;

    private StagingRuleTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static String getValue(String code) {
        for (StagingRuleTypeEnum c : StagingRuleTypeEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
