package com.cairh.cpe.common.util;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cairh.cpe.common.dto.ProfessionReasonConfig;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 配置获取
 */
public class PropertiesUtils {

    private static final CompositePropertySources compositePropertySources = SpringUtil.getBean(CompositePropertySources.class);

    public static String get(String key) {
        return get(key, "");
    }

    public static boolean getBoolean(String key) {
        String value = get(key, "");
        return "1".equals(value);
    }

    public static List<String> getList(String key) {
        String[] value = get(key, "").split(",");
        return Arrays.asList(value);
    }

    public static Map<String, String> getMap(String key) {
        Map<String, String> map = new HashMap<>();
        String value = get(key, "");
        if (StringUtils.isNotBlank(value)) {
            String[] strs = value.split(";");
            for (String str : strs) {
                String[] keyItem = str.split(":");
                if (keyItem.length == 2) {
                    map.put(keyItem[0], keyItem[1]);
                }
            }
        }
        return map;
    }

    public static String get(String key, String defaultValue) {
        String value = compositePropertySources.getProperty(key);
        if (null == value || value.toLowerCase().equals("null") || StringUtils.isBlank(value)
                || (value.startsWith("${") && value.endsWith("}"))) {
            value = defaultValue;
        }
        if ("security.footer".equals(key)) {
            value = value.replace("#0#", "©");
        }
        return value;
    }

    public static int getInt(String key, int defaultValue) {
        String value = get(key, String.valueOf(defaultValue));
        return Integer.parseInt(value);
    }

    /**
     * 解析配置
     */
    public static List<ProfessionReasonConfig> parseConfig(String jsonConfig) {
        List<ProfessionReasonConfig> configs = new ArrayList<>();

        JSONArray jsonArray = JSON.parseArray(jsonConfig);
        for (Object obj : jsonArray) {
            JSONObject jsonObject = (JSONObject) obj;

            ProfessionReasonConfig config = new ProfessionReasonConfig();
            config.setAge(jsonObject.getIntValue("age"));
            config.setAgeExpression(jsonObject.getString("age_expression"));
            config.setProfessionCode(Arrays.asList(jsonObject.getString("profession_code").split(",")));
            config.setReasonCode(Arrays.asList(jsonObject.getString("reason_code").split(",")));

            if (jsonObject.containsKey("sex")) {
                config.setSex(jsonObject.getString("sex"));
            }
            configs.add(config);
        }
        return configs;
    }

    /**
     * 条件校验
     */
    public static List<String> evaluateConditions(int age, String professionCode, String clientGender, List<ProfessionReasonConfig> conditions) {
        List<String> resultList = new ArrayList<>();

        for (ProfessionReasonConfig config : conditions) {
            // 判断年龄
            if (!evaluateAgeCondition(age, config.getAge(), config.getAgeExpression())) {
                continue;
            }
            // 判断职业代码
            if (!config.getProfessionCode().contains(professionCode)) {
                continue;
            }
            // 判断性别
            if (config.getSex() != null && !config.getSex().equals(clientGender)) {
                continue;
            }
            // 添加满足条件的reason_code
            resultList.addAll(config.getReasonCode());
        }
        return resultList;
    }

    /**
     * 获取匹配的职业编号列表
     */
    public static List<String> getProfessionCodeList(int age, String clientGender, List<ProfessionReasonConfig> conditions) {
        List<String> resultList = new ArrayList<>();

        for (ProfessionReasonConfig config : conditions) {
            // 判断年龄
            if (!evaluateAgeCondition(age, config.getAge(), config.getAgeExpression())) {
                continue;
            }
            // 判断性别
            if (config.getSex() != null && !config.getSex().equals(clientGender)) {
                continue;
            }
            // 添加满足条件的reason_code
            resultList.addAll(config.getProfessionCode());
        }
        return resultList;
    }

    private static boolean evaluateAgeCondition(int age, int configAge, String ageExpression) {
        if (">".equals(ageExpression)) {
            return age > configAge;
        } else if ("<".equals(ageExpression)) {
            return age < configAge;
        } else if (">=".equals(ageExpression)) {
            return age >= configAge;
        } else if ("<=".equals(ageExpression)) {
            return age <= configAge;
        } else if ("=".equals(ageExpression)) {
            return age == configAge;
        }
        return false;
    }
}
