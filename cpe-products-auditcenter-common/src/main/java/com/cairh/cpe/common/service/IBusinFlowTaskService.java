package com.cairh.cpe.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.common.dto.BaseRuleInfo;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.request.AuditForm;

import java.util.Map;

public interface IBusinFlowTaskService extends IService<BusinFlowTask> {

    /**
     * 创建待审核任务
     */
    BusinFlowTask createBusinFlowTask(BusinFlowRequest businFlowRequest, String task_type, String video_type, String allow_auditor, String not_auditor, String push_flag, Map<String, Object> contentMap, String task_id);

    /**
     * 开始处理任务
     */
    BusinFlowTask dealBusinFlowTask(AuditForm auditForm);

    /**
     * 处理完成任务
     */
    BusinFlowTask finishBusinFlowTask(AuditForm auditForm);

    /**
     * 查询当前的审核任务
     */
    BusinFlowTask getCurrTaskType(String request_no, String task_type);

    /**
     * 根据流程id查询当前的审核任务
     */
    BusinFlowTask getCurrTaskTypeByTaskId(String request_no, String task_type);

    BusinFlowTask getCurrTaskType(String request_no);

    /**
     * 任务暂存处理
     *
     * @param businFlowTask
     * @param baseRuleInfo
     */
    void taskStagingHandle(BusinFlowTask businFlowTask, BaseRuleInfo baseRuleInfo);
}
