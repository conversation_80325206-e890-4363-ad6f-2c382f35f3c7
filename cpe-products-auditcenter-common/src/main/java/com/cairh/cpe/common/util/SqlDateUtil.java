package com.cairh.cpe.common.util;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.AbstractWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cairh.cpe.common.constant.PropKeyConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;

import java.lang.reflect.Field;
import java.util.Date;
import java.util.Map;

@Slf4j
public class SqlDateUtil {

    private static final Environment environment = SpringUtil.getBean(Environment.class);

    public static String getDbType() {
        return environment.getProperty(PropKeyConstant.DB_TYPE);
    }

    public static String getDateStart(String startDate) {
        String dbType = environment.getProperty(PropKeyConstant.DB_TYPE);
        if (StringUtils.isBlank(startDate)) {
            return "";
        }
        if (StringUtils.equals(dbType, "mysql")) {
            return "str_to_date('" + startDate + " 00:00:00', '%Y%m%d %H:%i:%s')";
        } else {
            return "to_date('" + startDate + " 00:00:00','yyyyMMdd hh24:mi:ss')";
        }
    }

    public static String getDateStartDetail(String startDate) { //传入格式 20231115 00:00:00
        String dbType = environment.getProperty(PropKeyConstant.DB_TYPE);
        if (StringUtils.isBlank(startDate)) {
            return "";
        }
        if (StringUtils.equals(dbType, "mysql")) {
            return "str_to_date('" + startDate + "', '%Y%m%d %H:%i:%s')";
        } else {
            return "to_date('" + startDate + "','yyyyMMdd hh24:mi:ss')";
        }
    }

    public static String getDateSql(String startDate) {
        String dbType = environment.getProperty(PropKeyConstant.DB_TYPE);
        if (StringUtils.isBlank(startDate)) {
            return "";
        }
        if (StringUtils.equals(dbType, "mysql")) {
            return "str_to_date('" + startDate + "', '%Y-%m-%d %H:%i:%s')";
        } else {
            return "to_date('" + startDate + "','yyyy-MM-dd hh24:mi:ss')";
        }
    }

    public static String get16Time(String endDate) {
        if (StringUtils.isBlank(endDate)) {
            return "";
        }
        String dbType = environment.getProperty(PropKeyConstant.DB_TYPE);
        if (StringUtils.equals(dbType, "mysql")) {
            return "str_to_date('" + endDate + " 16:00:00', '%Y-%m-%d %H:%i:%s')";
        } else {
            return "to_date('" + endDate + " 16:00:00','yyyy-MM-dd hh24:mi:ss')";
        }
    }

    public static String getDateEnd(String endDate) {
        if (StringUtils.isBlank(endDate)) {
            return "";
        }
        String dbType = environment.getProperty(PropKeyConstant.DB_TYPE);
        if (StringUtils.equals(dbType, "mysql")) {
            return "str_to_date('" + endDate + " 23:59:59', '%Y%m%d %H:%i:%s')";
        } else {
            return "to_date('" + endDate + " 23:59:59','yyyyMMdd hh24:mi:ss')";
        }
    }

    public static String getDateEndDetail(String endDate) { //传入格式 20231115 00:00:00
        if (StringUtils.isBlank(endDate)) {
            return "";
        }
        String dbType = environment.getProperty(PropKeyConstant.DB_TYPE);
        if (StringUtils.equals(dbType, "mysql")) {
            return "str_to_date('" + endDate + "', '%Y%m%d %H:%i:%s')";
        } else {
            return "to_date('" + endDate + "','yyyyMMdd hh24:mi:ss')";
        }
    }

    public static Date getParseDateStart(String startDate) {
        try {
            if (StringUtils.isNotBlank(startDate)) {
                return KHDateUtil.parseDate(startDate + " 00:00:00", "yyyyMMdd HH:mm:ss");
            }
        } catch (Exception e) {
            log.error("日期格式化失败:{}", startDate);
        }
        return null;
    }

    public static Date getParseDateEnd(String endDate) {
        try {
            if (StringUtils.isNotBlank(endDate)) {
                return KHDateUtil.parseDate(endDate + " 23:59:59", "yyyyMMdd HH:mm:ss");
            }
        } catch (Exception e) {
            log.error("日期格式化失败:{}", endDate);
        }
        return null;
    }

    public static Date getParseDateStartDetail(String startDate) { //传入格式 20231115 00:00:00
        try {
            if (StringUtils.isNotBlank(startDate)) {
                return KHDateUtil.parseDate(startDate, "yyyyMMdd HH:mm:ss");
            }
        } catch (Exception e) {
            log.error("日期格式化失败:{}", startDate);
        }
        return null;
    }

    public static Date getParseDateEndDetail(String endDate) {
        try {
            if (StringUtils.isNotBlank(endDate)) {
                return KHDateUtil.parseDate(endDate, "yyyyMMdd HH:mm:ss");
            }
        } catch (Exception e) {
            log.error("日期格式化失败:{}", endDate);
        }
        return null;
    }

    public static <T> void setDefaultValue(T object1) {
        String dbType = environment.getProperty(PropKeyConstant.DB_TYPE);
        if (!StringUtils.equals(dbType, "mysql")) {
            Field[] fields1 = object1.getClass().getDeclaredFields();
            Object tempValue1;
            int i;
            for (i = 0; i < fields1.length; i++) {
                fields1[i].setAccessible(true);
                try {
                    String type = fields1[i].getGenericType().toString(); // 获取属性的类型
                    if (type.equals("class java.lang.String")) {
                        tempValue1 = fields1[i].get(object1);
                        if ("".equals(tempValue1)) {
                            fields1[i].set(object1, " ");
                        }

                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 打印查询条件
     */
    public static <T> String getWrapperSql(LambdaQueryWrapper<T> wrapper) {
        try {
            // 获取SQL片段
            String sqlSegment = wrapper.getCustomSqlSegment();
            // 获取参数映射
            Map<String, Object> paramMap = ((AbstractWrapper) wrapper).getParamNameValuePairs();
            // 构建完整SQL
            StringBuilder fullSql = new StringBuilder("SELECT * FROM table ");
            fullSql.append(sqlSegment);

            // 替换参数
            String sql = fullSql.toString();
            for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
                String paramName = "#{ew.paramNameValuePairs." + entry.getKey() + "}";
                Object value = entry.getValue();
                String valueStr = value instanceof String ? "'" + value + "'" : String.valueOf(value);
                sql = sql.replace(paramName, valueStr);
            }
            return sql;
        } catch (Exception e) {
            log.warn("打印查询sql失败", e);
        }
        return "";
    }
}
