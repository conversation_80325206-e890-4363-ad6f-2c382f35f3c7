package com.cairh.cpe.common.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.FlowTaskRecordDetails;
import com.cairh.cpe.common.mapper.FlowTaskRecordDetailsMapper;
import com.cairh.cpe.common.service.IFlowTaskRecordDetailsService;
import com.cairh.cpe.common.util.KHDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Description：流程业务记录详情表服务实现
 * Author： slx
 * Date： 2024/4/25 下午3:06
 */
@Slf4j
@Service
public class FlowTaskRecordDetailsServiceImpl extends ServiceImpl<FlowTaskRecordDetailsMapper, FlowTaskRecordDetails>
        implements IFlowTaskRecordDetailsService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public void saveFlowTaskRecordDetails(BusinFlowTask businFlowTask, Map<String, Object> params) {
        log.info("新增流程业务记录详情表[saveFlowTaskRecordDetails]，类型={}, request_no={}, task_id={}, serial_id={}",
                businFlowTask.getTask_type(), businFlowTask.getRequest_no(), businFlowTask.getTask_id(), businFlowTask.getSerial_id());

        FlowTaskRecordDetails flowTaskRecordDetails = createFlowTaskRecordDetails(businFlowTask, params);
        save(flowTaskRecordDetails);
    }

    @Override
    public void updateFlowTaskRecordDetails(BusinFlowTask businFlowTask, Map<String, Object> userParams, UpdateBusinProcessRequestAuditTrailSourceEnum sourceEnum) {
        FlowTaskRecordDetails flowTaskRecordDetails = getById(businFlowTask.getSerial_id());
        if (flowTaskRecordDetails != null) {
            updateFlowTaskRecordFromSourceEnum(flowTaskRecordDetails, businFlowTask, userParams, sourceEnum);
            log.info("更新流程业务记录详情表[updateFlowTaskRecordDetails]，数据来源={}, request_no={}, task_id={}, serial_id={}",
                    sourceEnum.getDesc(), businFlowTask.getRequest_no(), businFlowTask.getTask_id(), businFlowTask.getSerial_id());
            // 更新信息
            updateById(flowTaskRecordDetails);
        }
    }

    private FlowTaskRecordDetails createFlowTaskRecordDetails(BusinFlowTask businFlowTask, Map<String, Object> params) {
        log.info("新增流程业务记录详情表[createFlowTaskRecordDetails]，request_no={}, params={}",
                businFlowTask.getRequest_no(), params);
        FlowTaskRecordDetails flowTaskRecordDetails = new FlowTaskRecordDetails()
                .setRequest_no(businFlowTask.getRequest_no())
                .setTask_id(businFlowTask.getTask_id())
                .setSerial_id(businFlowTask.getSerial_id())
                .setTime_property(getTimeProperty(params, businFlowTask))
                .setRequest_datetime(Objects.nonNull(params.get(Fields.REQUEST_DATETIME))
                        ? (Date) params.get(Fields.REQUEST_DATETIME) : businFlowTask.getCreate_datetime())
                .setTask_datetime(businFlowTask.getCreate_datetime())
                .setTask_type(businFlowTask.getTask_type())
                .setTask_status(businFlowTask.getTask_status())
                .setBranch_no(getStringFieldValue(params, Fields.BRANCH_NO, StrUtil.SPACE))
                .setBranch_name(getStringFieldValue(params, Fields.BRANCH_NAME, StrUtil.SPACE))
                .setUp_branch_no(getStringFieldValue(params, Fields.UP_BRANCH_NO, StrUtil.SPACE))
                .setUp_branch_name(getStringFieldValue(params, Fields.UP_BRANCH_NAME, StrUtil.SPACE))
                .setIs_branch_managed(getStringFieldValue(params, Fields.IS_BRANCH_MANAGED, Constant.BRANCH_MANAGED_Y))
                .setBusin_type(getStringFieldValue(params, Fields.BUSIN_TYPE, StrUtil.SPACE))
                .setVideo_type(businFlowTask.getVideo_type());
        // 复核任务查询有效见证数据的见证完成时间
        if (StringUtils.equals(businFlowTask.getTask_type(), TaskTypeEnum.REVIEW.getCode())) {
            try {
                LambdaQueryWrapper<FlowTaskRecordDetails> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(FlowTaskRecordDetails::getTask_type, TaskTypeEnum.AUDIT.getCode())
                        .eq(FlowTaskRecordDetails::getRequest_no, businFlowTask.getRequest_no())
                        .eq(FlowTaskRecordDetails::getTask_id, businFlowTask.getTask_id())
                        .eq(FlowTaskRecordDetails::getInvalid_flag, WskhConstant.NORMAL_STATUS)
                        .orderByDesc(FlowTaskRecordDetails::getCreate_datetime);
                List<FlowTaskRecordDetails> auditFlowTaskList = this.getBaseMapper().selectList(queryWrapper);
                if (CollectionUtils.isNotEmpty(auditFlowTaskList)) {
                    flowTaskRecordDetails.setAudit_finish_datetime(auditFlowTaskList.get(0).getFinish_datetime());
                }
            } catch (Exception e) {
                log.error("[createFlowTaskRecordDetails]创建复核任务查询有效见证数据的见证完成时间异常，request_no={} task_id={} serial_id={}",
                        businFlowTask.getRequest_no(), businFlowTask.getTask_id(), businFlowTask.getSerial_id(), e);
            }
        }
        return flowTaskRecordDetails;
    }

    private void updateFlowTaskRecordFromSourceEnum(FlowTaskRecordDetails flowTaskRecordDetails, BusinFlowTask businFlowTask,
                                                    Map<String, Object> userParams, UpdateBusinProcessRequestAuditTrailSourceEnum sourceEnum) {
        flowTaskRecordDetails
                .setTask_type(businFlowTask.getTask_type())
                .setTask_status(businFlowTask.getTask_status())
                .setWhite_flag(businFlowTask.getWhite_flag());
        switch (sourceEnum) {
            case SOURCE_TASK_AUDITING:
                updateFlowTaskRecordDetailsFromTaskAuditing(flowTaskRecordDetails, businFlowTask, userParams);
                break;
            case SOURCE_TASK_SUSPEND:
                updateFlowTaskRecordDetailsFromTaskSuspend(flowTaskRecordDetails, businFlowTask);
                break;
            case SOURCE_TASK_TRANSFER:
                updateFlowTaskRecordDetailsFromTaskTransfer(flowTaskRecordDetails, businFlowTask);
                break;
            case SOURCE_TASK_TRANSFERNOACCEPT:
                updateFlowTaskRecordDetailsFromTaskTransferNoAccept(flowTaskRecordDetails, businFlowTask);
                break;
            case SOURCE_TASK_TRANSFERACCEPT:
                updateFlowTaskRecordDetailsFromTaskTransferAccept(flowTaskRecordDetails, businFlowTask, userParams, sourceEnum);
                break;
            case SOURCE_TASK_RECOVERY:
                updateFlowTaskRecordDetailsFromTaskRecovery(flowTaskRecordDetails, businFlowTask);
                break;
            case SOURCE_TASK_AUDITPASS:
                updateFlowTaskRecordDetailsFromTaskAuditPass(flowTaskRecordDetails, businFlowTask);
                break;
            case SOURCE_TASK_AUDITNOPASS:
                updateFlowTaskRecordDetailsFromTaskAuditNoPass(flowTaskRecordDetails, businFlowTask);
                break;
            case SOURCE_TASK_SPECIALFIELDS:
                updateFlowTaskRecordDetailsFromSpecialFields(flowTaskRecordDetails, userParams);
                break;
            case SOURCE_TASK_AUDITINVALIDATE:
                updateBusinProcessRequestAuditTrailFromTaskAuditInvalidate(flowTaskRecordDetails, businFlowTask);
                break;
            default:
                break;
        }
    }

    /**
     * 设置flowTaskRecordDetails对象数据通过任务审核中
     * 主要为操作员相关信息
     *
     * @param flowTaskRecordDetails 对象
     * @param businFlowTask         任务对象
     */
    private void updateFlowTaskRecordDetailsFromTaskAuditing(FlowTaskRecordDetails flowTaskRecordDetails,
                                                             BusinFlowTask businFlowTask, Map<String, Object> userParams) {
        if (StringUtils.isBlank(flowTaskRecordDetails.getFirst_deal_operator_no())) {
            flowTaskRecordDetails.setFirst_deal_operator_no(businFlowTask.getOperator_no())
                    .setFirst_deal_datetime(businFlowTask.getDeal_datetime());
        }
        String operator_branch_no = getStringFieldValue(userParams, Fields.BRANCH_NO, StrUtil.SPACE);
        String operator_up_branch_no = getStringFieldValue(userParams, Fields.UP_BRANCH_NO, StrUtil.SPACE);
        flowTaskRecordDetails.setOperator_no(businFlowTask.getOperator_no())
                .setOperator_name(businFlowTask.getOperator_name())
                .setOperator_branch_no(operator_branch_no)
                .setOperator_up_branch_no(operator_up_branch_no)
                .setDeal_datetime(businFlowTask.getDeal_datetime())
                .setEnd_deal_operator_no(businFlowTask.getOperator_no())
                .setEnd_deal_datetime(businFlowTask.getDeal_datetime());
    }

    /**
     * 挂起更新flowTaskRecordDetails对象数据
     * 更新挂起标识：pause_flag
     *
     * @param flowTaskRecordDetails 对象
     * @param businFlowTask         任务对象
     */
    private void updateFlowTaskRecordDetailsFromTaskSuspend(FlowTaskRecordDetails flowTaskRecordDetails, BusinFlowTask businFlowTask) {
        checkPauseAndUpdate(flowTaskRecordDetails, null, businFlowTask);
        flowTaskRecordDetails.setTask_type(businFlowTask.getTask_type())
                .setTask_status(FlowStatusConst.AUDIT_SUSPEND)
                .setEnd_deal_operator_no(businFlowTask.getOperator_no())
                .setEnd_deal_datetime(businFlowTask.getDeal_datetime());
    }

    /**
     * 转交更新flowTaskRecordDetails对象数据
     *
     * @param flowTaskRecordDetails 对象
     * @param businFlowTask         任务对象
     */
    private void updateFlowTaskRecordDetailsFromTaskTransfer(FlowTaskRecordDetails flowTaskRecordDetails, BusinFlowTask businFlowTask) {
        taskTransferNoAccept(flowTaskRecordDetails, businFlowTask, FlowStatusConst.AUDIT_TRANSFER);
    }

    /**
     * 转交未接受更新flowTaskRecordDetails对象数据
     *
     * @param flowTaskRecordDetails 对象
     * @param businFlowTask         任务对象
     */
    private void updateFlowTaskRecordDetailsFromTaskTransferNoAccept(FlowTaskRecordDetails flowTaskRecordDetails, BusinFlowTask businFlowTask) {
        // 双向视频见证任务，需要回滚状态为待审核
        String taskStatus = FlowStatusConst.AUDIT_AUDITING;
        if (StringUtils.equals(businFlowTask.getVideo_type(), WskhConstant.VIDEO_TYPE_2)
                && StringUtils.equals(businFlowTask.getTask_type(), TaskTypeEnum.AUDIT.getCode())) {
            taskStatus = FlowStatusConst.AUDIT_PENDING;
        }
        taskTransferNoAccept(flowTaskRecordDetails, businFlowTask, taskStatus);
    }

    private void taskTransferNoAccept(FlowTaskRecordDetails flowTaskRecordDetails, BusinFlowTask businFlowTask, String taskStatus) {
        flowTaskRecordDetails.setTask_type(businFlowTask.getTask_type())
                .setTask_status(taskStatus)
                .setEnd_deal_operator_no(businFlowTask.getOperator_no())
                .setEnd_deal_datetime(businFlowTask.getDeal_datetime());
    }

    /**
     * 转交接受成功更新flowTaskRecordDetails对象数据
     *
     * @param flowTaskRecordDetails 对象
     * @param businFlowTask         任务对象
     */
    private void updateFlowTaskRecordDetailsFromTaskTransferAccept(FlowTaskRecordDetails flowTaskRecordDetails,
                                                                   BusinFlowTask businFlowTask, Map<String, Object> userParams,
                                                                   UpdateBusinProcessRequestAuditTrailSourceEnum sourceEnum) {
        checkPauseAndUpdate(flowTaskRecordDetails, sourceEnum, businFlowTask);
        String operator_no = getStringFieldValue(userParams, Fields.OPERATOR_NO, StrUtil.SPACE);
        String operator_name = getStringFieldValue(userParams, Fields.OPERATOR_NAME, StrUtil.SPACE);
        String operator_branch_no = getStringFieldValue(userParams, Fields.BRANCH_NO, StrUtil.SPACE);
        String operator_up_branch_no = getStringFieldValue(userParams, Fields.UP_BRANCH_NO, StrUtil.SPACE);
        flowTaskRecordDetails.setTask_status(FlowStatusConst.AUDIT_PENDING)
                .setOperator_no(operator_no)
                .setOperator_name(operator_name)
                .setOperator_branch_no(operator_branch_no)
                .setOperator_up_branch_no(operator_up_branch_no)
                .setEnd_deal_operator_no(businFlowTask.getOperator_no())
                .setEnd_deal_datetime(businFlowTask.getDeal_datetime());
    }

    /**
     * 任务回收更新flowTaskRecordDetails对象数据
     *
     * @param flowTaskRecordDetails 对象
     */
    private void updateFlowTaskRecordDetailsFromTaskRecovery(FlowTaskRecordDetails flowTaskRecordDetails, BusinFlowTask businFlowTask) {
        checkPauseAndUpdate(flowTaskRecordDetails, null, businFlowTask);
        flowTaskRecordDetails.setTask_status(FlowStatusConst.AUDIT_PENDING)
                .setOperator_no(StrUtil.SPACE)
                .setOperator_name(StrUtil.SPACE)
                .setOperator_branch_no(StrUtil.SPACE)
                .setOperator_up_branch_no(StrUtil.SPACE)
                .setDeal_datetime(null);
    }

    /**
     * 审核通过更新flowTaskRecordDetails对象数据
     *
     * @param flowTaskRecordDetails 对象
     * @param businFlowTask         任务对象
     */
    private void updateFlowTaskRecordDetailsFromTaskAuditPass(FlowTaskRecordDetails flowTaskRecordDetails, BusinFlowTask businFlowTask) {
        taskAuditPassAndNoPass(flowTaskRecordDetails, businFlowTask, FlowStatusConst.AUDIT_PASS);
    }

    /**
     * 审核不通过更新flowTaskRecordDetails对象数据
     *
     * @param flowTaskRecordDetails 对象
     * @param businFlowTask         任务对象
     */
    private void updateFlowTaskRecordDetailsFromTaskAuditNoPass(FlowTaskRecordDetails flowTaskRecordDetails, BusinFlowTask businFlowTask) {
        taskAuditPassAndNoPass(flowTaskRecordDetails, businFlowTask, FlowStatusConst.AUDIT_NO_PASS);
    }

    /**
     * 见证作废更新businProcessRequestAuditTrail对象数据 invalidate
     *
     * @param flowTaskRecordDetails 对象
     * @param businFlowTask         任务对象
     */
    private void updateBusinProcessRequestAuditTrailFromTaskAuditInvalidate(FlowTaskRecordDetails flowTaskRecordDetails, BusinFlowTask businFlowTask) {
        taskAuditPassAndNoPass(flowTaskRecordDetails, businFlowTask, FlowStatusConst.AUDIT_INVALIDATE);
    }

    private void taskAuditPassAndNoPass(FlowTaskRecordDetails flowTaskRecordDetails, BusinFlowTask businFlowTask, String taskStatus) {
        flowTaskRecordDetails.setTask_type(businFlowTask.getTask_type())
                .setTask_status(taskStatus)
                .setFinish_datetime(businFlowTask.getFinish_datetime())
                .setEnd_deal_operator_no(businFlowTask.getOperator_no())
                .setEnd_deal_datetime(businFlowTask.getDeal_datetime());
    }

    /**
     * 特殊字段更新flowTaskRecordDetails对象数据
     * 目前：deal_datetime、end_deal_datetime
     *
     * @param flowTaskRecordDetails 对象
     */
    private void updateFlowTaskRecordDetailsFromSpecialFields(FlowTaskRecordDetails flowTaskRecordDetails, Map<String, Object> userParams) {
        // deal_datetime、end_deal_datetime
        if (userParams.containsKey(Fields.DEAL_DATETIME)) {
            Object dealDateTime = MapUtils.getObject(userParams, Fields.DEAL_DATETIME);
            updateFlowTaskRecordDetailsField(flowTaskRecordDetails, userParams, Fields.DEAL_DATETIME);
            userParams.put("end_deal_datetime", dealDateTime);
            updateFlowTaskRecordDetailsField(flowTaskRecordDetails, userParams, "end_deal_datetime");
        }
        // 系统派单分配指定一个用户，更新operator_no、operator_name
        if (userParams.containsKey(Fields.OPERATOR_NO)) {
            updateFlowTaskRecordDetailsField(flowTaskRecordDetails, userParams, Fields.OPERATOR_NO);
        }
        if (userParams.containsKey(Fields.OPERATOR_NAME)) {
            updateFlowTaskRecordDetailsField(flowTaskRecordDetails, userParams, Fields.OPERATOR_NAME);
        }
        if (userParams.containsKey(Fields.OPERATOR_BRANCH_NO)) {
            updateFlowTaskRecordDetailsField(flowTaskRecordDetails, userParams, Fields.OPERATOR_BRANCH_NO);
        }
        if (userParams.containsKey(Fields.OPERATOR_UP_BRANCH_NO)) {
            updateFlowTaskRecordDetailsField(flowTaskRecordDetails, userParams, Fields.OPERATOR_UP_BRANCH_NO);
        }
        if (userParams.containsKey(Fields.TASK_TYPE)) {
            updateFlowTaskRecordDetailsField(flowTaskRecordDetails, userParams, Fields.TASK_TYPE);
        }
        if (userParams.containsKey(Fields.INVALID_FLAG)) {
            updateFlowTaskRecordDetailsField(flowTaskRecordDetails, userParams, Fields.INVALID_FLAG);
        }
        if (userParams.containsKey(Fields.WHITE_FLAG)) {
            updateFlowTaskRecordDetailsField(flowTaskRecordDetails, userParams, Fields.WHITE_FLAG);
        }
        if (userParams.containsKey(Fields.WHITE_DATETIME)) {
            updateFlowTaskRecordDetailsField(flowTaskRecordDetails, userParams, Fields.WHITE_DATETIME);
        }
    }

    private void updateFlowTaskRecordDetailsField(FlowTaskRecordDetails flowTaskRecordDetails, Map<String, Object> userParams, String fieldName) {
        try {
            Object obj = MapUtils.getObject(userParams, fieldName);
            Object value;
            if (obj instanceof String) {
                value = StringUtils.isNotEmpty((String) obj) ? obj : StrUtil.SPACE;
            } else {
                value = obj;
            }
            flowTaskRecordDetails.setFieldValue(fieldName, value);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.error("updateFlowTaskRecordDetailsField error", e);
        }
    }

    /**
     * 检查是否已经存在暂存标识，无则更新
     *
     * @param flowTaskRecordDetails 对象
     * @param sourceEnum            来源
     * @param businFlowTask         当前任务
     */
    private void checkPauseAndUpdate(FlowTaskRecordDetails flowTaskRecordDetails, UpdateBusinProcessRequestAuditTrailSourceEnum sourceEnum, BusinFlowTask businFlowTask) {
        if ("0".equals(flowTaskRecordDetails.getPause_flag())) {
            try {
                flowTaskRecordDetails.setPause_flag("1");
                flowTaskRecordDetails.setFirst_pause_operator_no(businFlowTask.getOperator_no());
                // 转交需要获取转交发起时间进行保存
                if (UpdateBusinProcessRequestAuditTrailSourceEnum.SOURCE_TASK_TRANSFERACCEPT.equals(sourceEnum)) {
                    // 获取redis中该任务的转交时间
                    String key = String.format(RedisKeyConstant.WSKH_AC_TASK_TRANSFER_TIME, flowTaskRecordDetails.getRequest_no());
                    String transferDate = redisTemplate.opsForValue().get(key);
                    // 转为时间类型
                    if (StringUtils.isNotEmpty(transferDate)) {
                        log.info("[FlowTaskRecordDetails]转交时间={} request_no={}", transferDate, flowTaskRecordDetails.getRequest_no());
                        flowTaskRecordDetails.setFirst_pause_datetime(KHDateUtil.parseDate(transferDate, KHDateUtil.DATE_TIME_FORMAT));
                        return;
                    }
                }
                flowTaskRecordDetails.setFirst_pause_datetime(new Date());
            } catch (Exception e) {
                log.error("checkPauseAndUpdate error request_no={}", flowTaskRecordDetails.getRequest_no(), e);
                flowTaskRecordDetails.setPause_flag("1");
                flowTaskRecordDetails.setFirst_pause_operator_no(businFlowTask.getOperator_no());
                flowTaskRecordDetails.setFirst_pause_datetime(new Date());
            }
        }
    }

    /**
     * 时间属性
     *
     * @return 1:工作时间 2:非工作时间
     */
    private String getTimeProperty(Map<String, Object> params, BusinFlowTask businFlowTask) {
        if (!TaskTypeEnum.AUDIT.getCode().equals(businFlowTask.getTask_type())) {
            // 查询flowTaskRecordDetails中request_no相同的见证数据
            FlowTaskRecordDetails flowTaskRecordDetails = queryFlowTaskRecordDetails(businFlowTask);
            if (flowTaskRecordDetails != null) {
                return flowTaskRecordDetails.getTime_property();
            }
        }
        if (MapUtils.getBoolean(params, Fields.IS_WORK_TIME, false)) {
            return TimePropertyEnum.WORKTIME.getCode();
        }
        return TimePropertyEnum.NOWORKTIME.getCode();
    }

    /**
     * 查询FlowTaskRecordDetails根据task_id查询见证的time_property
     *
     * @param businFlowTask 任务对象
     * @return FlowTaskRecordDetails
     */
    private FlowTaskRecordDetails queryFlowTaskRecordDetails(BusinFlowTask businFlowTask) {
        LambdaQueryWrapper<FlowTaskRecordDetails> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FlowTaskRecordDetails::getRequest_no, businFlowTask.getRequest_no())
                .eq(FlowTaskRecordDetails::getTask_id, businFlowTask.getTask_id())
                .eq(FlowTaskRecordDetails::getTask_type, TaskTypeEnum.AUDIT.getCode());
        List<FlowTaskRecordDetails> flowTaskRecordDetailsList = list(wrapper);
        return CollectionUtils.isNotEmpty(flowTaskRecordDetailsList) ? flowTaskRecordDetailsList.get(0) : null;
    }

    /**
     * 获取字符串类型的参数
     *
     * @param params       map数据
     * @param fieldName    字段名
     * @param defaultValue 默认值
     * @return 字符串
     */
    private String getStringFieldValue(Map<String, Object> params, String fieldName, String defaultValue) {
        if (params == null) {
            return defaultValue;
        }
        Object object = params.get(fieldName);
        if (object instanceof String && !StringUtils.isEmpty((String) object)) {
            return object.toString();
        }
        return defaultValue;
    }

}
