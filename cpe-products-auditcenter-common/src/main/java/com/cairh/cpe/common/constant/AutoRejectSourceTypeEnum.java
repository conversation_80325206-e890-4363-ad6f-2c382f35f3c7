package com.cairh.cpe.common.constant;

public enum AutoRejectSourceTypeEnum {

    BASE_INFO("1", "基础数据"),

    AI_AUDIT("2", "智能审核");

    private String code;
    private String value;

    private AutoRejectSourceTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static String getValue(String code) {
        for (AutoRejectSourceTypeEnum c : AutoRejectSourceTypeEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
