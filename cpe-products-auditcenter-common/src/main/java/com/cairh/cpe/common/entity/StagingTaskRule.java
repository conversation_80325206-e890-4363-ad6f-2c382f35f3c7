package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 暂存任务规则表
 *
 * <AUTHOR>
 * @since 2025/3/11 16:11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("STAGINGTASKRULE")
public class StagingTaskRule implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 规则名称
     */
    @TableField("rule_name")
    private String rule_name;

    /**
     * 规则类型（1-暂存任务 2-自动驳回任务）
     */
    @TableField("rule_type")
    private String rule_type;

    /**
     * 规则开始时间
     */
    @TableField("rule_datetime_start")
    private Date rule_datetime_start;

    /**
     * 规则结束时间
     */
    @TableField("rule_datetime_end")
    private Date rule_datetime_end;

    /**
     * 业务类型
     */
    @TableField("busin_type")
    private String busin_type;

    /**
     * 证件类型
     */
    @TableField("id_kind")
    private String id_kind;

    /**
     * 任务类型
     */
    @TableField("task_type")
    private String task_type;

    /**
     * 自动驳回类型
     * 1-基础数据 2-智能审核
     */
    @TableField("reject_type")
    private String reject_type;

    /**
     * 可选规则
     */
    @TableField("expression")
    private String expression;

    /**
     * 驳回原因
     */
    @TableField("reject_reason")
    private String reject_reason;

    /**
     * 关联智能审核项
     */
    @TableField("aiaudit_items")
    private String aiaudit_items;

    /**
     * 关联智能审核驳回原因
     */
    @TableField("aiaudit_reject_reason")
    private String aiaudit_reject_reason;

    /**
     * 状态（1-可用，0-禁用）
     */
    @TableField("status")
    private String status;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 顺序，优先级
     */
    @TableField("order_no")
    private Integer order_no;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String create_by;

    /**
     * 创建时间
     */
    @TableField("create_datetime")
    private Date create_datetime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String update_by;

    /**
     * 更新时间
     */
    @TableField("update_datetime")
    private Date update_datetime;

}
