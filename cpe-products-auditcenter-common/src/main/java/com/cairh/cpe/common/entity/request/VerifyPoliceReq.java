package com.cairh.cpe.common.entity.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 公安认证入参
 */
@Data
public class VerifyPoliceReq implements Serializable {

    /**
     * 机构标识
     */
    @NotBlank
    private String organ_flag;

    /**
     * 证件类别
     */
    @NotBlank
    private String id_kind;

    /**
     * 证件号码
     */
    @NotBlank
    private String id_no;

    /**
     * 姓名
     */
    @NotBlank
    private String full_name;

    /**
     * 实时标志  默认为0。传入1时实时调用底层公安接口，不取本地数据
     */
    private String realtime_flag;

    /**
     * 营业部编号
     */
    @NotBlank
    private String branch_no;

    /**
     * 营业部编号
     */
    private String op_branch_no;

    /**
     * 真实营业部编号
     */
    @NotBlank
    private String real_branch_no;

    /**
     * 服务厂商定制, 非业务参数
     */
    private String service_vender;

    /**
     * 大头照base64 csdc_busi_kind=03时必传
     * 当厂商为中登公安时，才会涉及csdc_busi_kind和base64_image字段
     */
    private String base64_image;

    /**
     * 统一社会信用代码  organ_flag为1时 必传
     */
    private String usc_code;

    /**
     * 组织机构代码  organ_flag为1时 必传
     */
    private String organ_code;

    /**
     * 02：简项查询，返回照片， 03：人像比对，默认值为02  ，当csdc_busi_kind=03时，此时机构标志只能传0
     */
    private String csdc_busi_kind;
}
