package com.cairh.cpe.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 暂存记录表
 *
 * <AUTHOR>
 * @since 2025/5/22 11:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("AUTOREJECTRECORD")
public class AutoRejectRecord implements Serializable {

    /**
     * 主键ID
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 规则id
     */
    @TableField("rule_id")
    private String rule_id;

    /**
     * 规则名称
     */
    @TableField("rule_name")
    private String rule_name;

    /**
     * 任务请求编号
     */
    @TableField("request_no")
    private String request_no;

    /**
     * 任务id
     */
    @TableField("task_id")
    private String task_id;

    /**
     * 任务唯一编号
     */
    @TableField("flow_task_id")
    private String flow_task_id;

    /**
     * 类型（1-基础数据驳回 2-智能审核驳回）
     */
    @TableField("record_type")
    private String record_type;

    /**
     * 创建时间
     */
    @TableField("create_datetime")
    private Date create_datetime;

    /**
     * 匹配信息
     */
    @TableField("match_info")
    private String match_info;

}
