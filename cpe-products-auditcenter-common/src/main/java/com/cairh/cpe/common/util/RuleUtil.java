package com.cairh.cpe.common.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.cairh.cpe.common.constant.ExpressionEnum;
import com.cairh.cpe.common.constant.RelationShipEnum;
import com.cairh.cpe.common.dto.Rule;
import com.cairh.cpe.common.dto.Rule.Expression;
import com.cairh.cpe.common.entity.RuleConfiguration;
import com.cairh.cpe.common.entity.clob.MatchInfo;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 规则匹配工具类
 *
 * <AUTHOR>
 * @since 2023/9/23 10:24
 */
public class RuleUtil {

    /**
     * 判断任务是否满足规则
     *
     * @param stagingMatchInfo
     * @param rule
     * @return Boolean
     * <AUTHOR>
     * @since 2023/9/23 10:19
     */
    public static Boolean match(MatchInfo stagingMatchInfo, Rule rule, List<RuleConfiguration> ruleConfigurationList) {
        String relationShip = rule.getRelation_ship();
        Map<String, RuleConfiguration> fieldCodeAndRuleConfigurationMap = ruleConfigurationList.stream().collect(Collectors.toMap(RuleConfiguration::getField_code, Function.identity(), (k1, k2) -> k1));
        return (StringUtils.equals(relationShip, RelationShipEnum.AND.getRelationShip()) ? andMatch(stagingMatchInfo, rule, fieldCodeAndRuleConfigurationMap) : orMatch(stagingMatchInfo, rule, fieldCodeAndRuleConfigurationMap));
    }

    /**
     * 且关系匹配
     *
     * @param stagingMatchInfo
     * @param rule
     * @return boolean
     * <AUTHOR>
     * @since 2023/10/13 11:16
     */
    private static boolean andMatch(MatchInfo stagingMatchInfo, Rule rule, Map<String, RuleConfiguration> fieldCodeAndRuleConfigurationMap) {
        boolean notMacth = Boolean.FALSE;
        for (Expression expression : rule.getExpressionList()) {
            String fieldCode = expression.getField_code();
            if (StringUtils.isBlank(fieldCode)) {
                continue;
            }
            String expressionStr = expression.getExpression();
            if (StringUtils.isBlank(expressionStr)) {
                continue;
            }
            String fieldValue = expression.getField_value();
            if (StringUtils.isBlank(fieldValue)) {
                continue;
            }

            RuleConfiguration ruleConfiguration = fieldCodeAndRuleConfigurationMap.get(fieldCode);
            String fieldType = ruleConfiguration.getField_type();
            if (StringUtils.equals(fieldType, "date")) {
                Object fieldValueObj = BeanUtil.getFieldValue(stagingMatchInfo, fieldCode);
                if (Objects.isNull(fieldValueObj)) {
                    continue;
                }
                Date date = (Date) fieldValueObj;
                List<String> collect = Arrays.stream(fieldValue.split(StrUtil.COMMA)).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(collect)) {
                    continue;
                }
                if (StringUtils.isNotBlank(collect.get(0))) {
                    Date startDate = com.cairh.cpe.util.date.DateUtil.dateTime("yyyyMMddHHmmss", collect.get(0));
                    if (date.compareTo(startDate) < 0) {
                        notMacth = Boolean.TRUE;
                        break;
                    }
                }
                if (StringUtils.isNotBlank(collect.get(1))) {
                    Date endDate = com.cairh.cpe.util.date.DateUtil.dateTime("yyyyMMddHHmmss", collect.get(1));
                    if (date.compareTo(endDate) > 0) {
                        notMacth = Boolean.TRUE;
                        break;
                    }
                }
            } else {
                String fieldValueStr = Optional.ofNullable(BeanUtil.getFieldValue(stagingMatchInfo, fieldCode)).map(String::valueOf).orElse(StringUtils.EMPTY);
                List<String> fieldValueList = Arrays.stream(fieldValue.split(StrUtil.COMMA)).collect(Collectors.toList());
                boolean conditionValueContainFieldValue = fieldValueList.contains(fieldValueStr);
                boolean inButNotIn = StringUtils.equals(expressionStr, ExpressionEnum.IN.getRelationShip()) && !conditionValueContainFieldValue;
                boolean notInButIn = StringUtils.equals(expressionStr, ExpressionEnum.NOT_IN.getRelationShip()) && conditionValueContainFieldValue;

                List<String> fieldValueSplitToList = Arrays.stream(fieldValueStr.split(StrUtil.COMMA)).collect(Collectors.toList());
                boolean fieldValueContainConditionValue = CollectionUtil.containsAll(fieldValueSplitToList, fieldValueList);
                boolean containButNotContain = StringUtils.equals(expressionStr, ExpressionEnum.CONTAIN.getRelationShip()) && !fieldValueContainConditionValue;
                boolean fieldValueStartsWithConditionValue = false;
                if (StringUtils.isNotBlank(fieldValueStr)) {
                    fieldValueStartsWithConditionValue = fieldValueStr.startsWith(fieldValue);
                }
                boolean startsWithButNotStartsWith = StringUtils.equals(expressionStr, ExpressionEnum.STARTSWITH.getRelationShip()) && !fieldValueStartsWithConditionValue;

                if (inButNotIn || notInButIn || containButNotContain || startsWithButNotStartsWith) {
                    notMacth = Boolean.TRUE;
                    break;
                }
            }
        }
        return !notMacth;
    }

    /**
     * 或关系匹配
     *
     * @param stagingMatchInfo
     * @param rule
     * @return boolean
     * <AUTHOR>
     * @since 2023/10/13 11:16
     */
    private static boolean orMatch(MatchInfo stagingMatchInfo, Rule rule, Map<String, RuleConfiguration> fieldCodeAndRuleConfigurationMap) {
        boolean match = Boolean.FALSE;
        for (Expression expression : rule.getExpressionList()) {
            String fieldCode = expression.getField_code();
            if (StringUtils.isBlank(fieldCode)) {
                continue;
            }
            String expressionStr = expression.getExpression();
            if (StringUtils.isBlank(expressionStr)) {
                continue;
            }
            String fieldValue = expression.getField_value();
            if (StringUtils.isBlank(fieldValue)) {
                continue;
            }

            RuleConfiguration ruleConfiguration = fieldCodeAndRuleConfigurationMap.get(fieldCode);
            String fieldType = ruleConfiguration.getField_type();
            if (StringUtils.equals(fieldType, "date")) {
                Object fieldValueObj = BeanUtil.getFieldValue(stagingMatchInfo, fieldCode);
                if (Objects.isNull(fieldValueObj)) {
                    continue;
                }
                Date date = (Date) fieldValueObj;
                List<String> collect = Arrays.stream(fieldValue.split(StrUtil.COMMA)).collect(Collectors.toList());
                if (CollectionUtil.isEmpty(collect)) {
                    continue;
                }
                if (StringUtils.isNotBlank(collect.get(0))) {
                    Date startDate = com.cairh.cpe.util.date.DateUtil.dateTime("yyyyMMddHHmmss", collect.get(0));
                    if (date.compareTo(startDate) < 0) {
                        continue;
                    }
                }
                if (StringUtils.isNotBlank(collect.get(1))) {
                    Date endDate = com.cairh.cpe.util.date.DateUtil.dateTime("yyyyMMddHHmmss", collect.get(1));
                    if (date.compareTo(endDate) > 0) {
                        continue;
                    }
                }
                match = Boolean.TRUE;
                break;
            } else {
                // 页面配置值
                List<String> fieldValueList = Arrays.stream(fieldValue.split(StrUtil.COMMA)).collect(Collectors.toList());
                // 派单任务值
                String fieldValueStr = Optional.ofNullable(BeanUtil.getFieldValue(stagingMatchInfo, fieldCode)).map(String::valueOf).orElse(StringUtils.EMPTY);

                boolean conditionValueContainFieldValue = fieldValueList.contains(fieldValueStr);
                boolean in = StringUtils.equals(expressionStr, ExpressionEnum.IN.getRelationShip()) && conditionValueContainFieldValue;
                boolean notIn = StringUtils.equals(expressionStr, ExpressionEnum.NOT_IN.getRelationShip()) && !conditionValueContainFieldValue;

                List<String> fieldValueSplitToList = Arrays.stream(fieldValueStr.split(StrUtil.COMMA)).collect(Collectors.toList());
                boolean fieldValueContainConditionValue = CollectionUtil.containsAll(fieldValueSplitToList, fieldValueList);
                boolean contain = StringUtils.equals(expressionStr, ExpressionEnum.CONTAIN.getRelationShip()) && fieldValueContainConditionValue;
                boolean fieldValueStartsWithConditionValue = false;
                if (StringUtils.isNotBlank(fieldValueStr)) {
                    fieldValueStartsWithConditionValue = fieldValueStr.startsWith(fieldValue);
                }
                boolean startsWith = StringUtils.equals(expressionStr, ExpressionEnum.STARTSWITH.getRelationShip()) && fieldValueStartsWithConditionValue;

                if (in || notIn || contain || startsWith) {
                    match = Boolean.TRUE;
                    break;
                }
            }
        }
        return match;
    }

}
