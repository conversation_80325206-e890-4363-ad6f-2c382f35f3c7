package com.cairh.cpe.common.constant;

public class RedisKeyConstant {
    //提交智能派单失败次数
    public static final String WSKH_AC_SUBMIT_PARAMS_FAIL_COUNT = "wskh_ac_submit_params_fail_count_%s";

    public static final String WSKH_AC_AIADUDIT_FAIL_COUNT = "wskh:summit:aiaudit:count:%s";
    public static final String WSKH_AC_LABEL = "wskh:ac:label";

    public static final String WSKH_REPEATED_WHITELIST = "wskh:repeated:whitelist";

    public static final String WSKH_REPEATED_BLACKLIST = "wskh:repeated:blacklist";

    public static final String WSKH_ADDRESS_HMACSHA1 = "wskh:address:hmacSha1:";

    public static final String WSKH_ADDRESS_HASH = "wskh:address:hashKey";

    public static final String WSKH_AC_TASK_TRANSFER_TIME = "wskh_ac_task_transfer_time_%s";


    public static final String WSKH_DISHONEST_RECORD = "wskh:dishonest:record:%s";

    public static final String WSKH_AC_BIDIRECTIONAL_TASK_HEARTBEAT = "wskh_ac_bidirectional_task_heartbeat";

    public static final String WSKH_ISSUING_AUTHORITY_ORGAN = "wskh:issuing:authority:organ";

    /**
     * 规则配置list
     */
    public static final String AUDTI_CACHE_RULE_LIST = "audit:cache:rule:list";

    /**
     * 任务规则list
     */
    public static final String AUDTI_TASK_CACHE_RULE_LIST = "audit:task:cache:rule:list";

    /**
     * 自动驳回任务规则list
     */
    public static final String AUDTI_AUTO_REJECT_CACHE_RULE_LIST = "audit:auto:reject:cache:rule:list";

}
