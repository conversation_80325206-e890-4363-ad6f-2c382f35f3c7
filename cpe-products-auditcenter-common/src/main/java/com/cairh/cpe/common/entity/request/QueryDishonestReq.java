package com.cairh.cpe.common.entity.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class QueryDishonestReq {

    /**
     * 发起人
     */
    String id;

    /**
     * 用户姓名
     */
    @NotBlank(message = "用户姓名不能为空")
    String client_name;

    /**
     * 证件号码
     */
    @NotBlank(message = "证件号码不能为空")
    String id_no;

    /**
     * 营业部编号
     */
    @NotBlank(message = "营业部编号不能为空")
    String branch_no;
}
