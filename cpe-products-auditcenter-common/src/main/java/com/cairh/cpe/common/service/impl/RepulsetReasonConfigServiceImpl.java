package com.cairh.cpe.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.common.entity.RepulsetReasonConfig;
import com.cairh.cpe.common.entity.request.ReasonGroupLImitReq;
import com.cairh.cpe.common.entity.request.RectificationReasonReq;
import com.cairh.cpe.common.entity.request.RepulsetReasonBatchForm;
import com.cairh.cpe.common.entity.response.DictInfo;
import com.cairh.cpe.common.entity.response.NotAllowReasonGroup;
import com.cairh.cpe.common.entity.response.RepulsetReasonConfigInfo;
import com.cairh.cpe.common.entity.response.RepulsetReasonConfigQueryRes;
import com.cairh.cpe.common.mapper.RepulsetReasonConfigMapper;
import com.cairh.cpe.common.service.IRepulsetReasonConfigService;
import com.cairh.cpe.common.util.BaseBeanUtil;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.db.config.IdGenerator;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class RepulsetReasonConfigServiceImpl extends ServiceImpl<RepulsetReasonConfigMapper, RepulsetReasonConfig> implements IRepulsetReasonConfigService {

    @Resource
    private IdGenerator idGenerator;

    @Override
    public Page<RepulsetReasonConfigQueryRes> selectRectificationReasonListByPage(RectificationReasonReq rectificationReasonReq, List<DictInfo> dictInfos) {
        LambdaQueryWrapper<RepulsetReasonConfig> reasonLambdaQueryWrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(rectificationReasonReq.getAudit_type())) {
            reasonLambdaQueryWrapper.eq(RepulsetReasonConfig::getAudit_type, rectificationReasonReq.getAudit_type());
        }
        if (StringUtils.isNotBlank(rectificationReasonReq.getBusin_type())) {
            reasonLambdaQueryWrapper.eq(RepulsetReasonConfig::getBusin_type, rectificationReasonReq.getBusin_type());
        }
        if (StringUtils.isNotBlank(rectificationReasonReq.getId_kind())) {
            reasonLambdaQueryWrapper.eq(RepulsetReasonConfig::getId_kind, rectificationReasonReq.getId_kind());
        }
        reasonLambdaQueryWrapper.eq(RepulsetReasonConfig::getUn_serial_id, " ");

        // 父级菜单
        Map<String, Integer> collect = dictInfos.stream().collect(Collectors.toMap(DictInfo::getSub_code, dictInfo -> dictInfo.getOrder_no()));
        List<RepulsetReasonConfig> reasonConfigs = this.list(reasonLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(reasonConfigs)) {
            return new Page<>();
        }
        // 分页使用
        int total = reasonConfigs.size();
        int startIndex = (rectificationReasonReq.getCur_page() - 1) * rectificationReasonReq.getPage_size();
        reasonConfigs = reasonConfigs.stream().
                sorted(Comparator.comparing(RepulsetReasonConfig::getAudit_type).thenComparingInt(r -> Integer.valueOf(collect.getOrDefault(r.getCause_group(), 0))))
                .skip(startIndex).limit(rectificationReasonReq.getPage_size()).collect(Collectors.toList());


        LambdaQueryWrapper<RepulsetReasonConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(RepulsetReasonConfig::getUn_serial_id, reasonConfigs.stream().map(RepulsetReasonConfig::getSerial_id).collect(Collectors.toList()));
        List<RepulsetReasonConfig> children_list = this.list(wrapper);
        //数据拼装
        List<RepulsetReasonConfigQueryRes> queryResList = reasonConfigs.stream()
                .map(item -> {
                    RepulsetReasonConfigQueryRes queryRes = new RepulsetReasonConfigQueryRes();
                    BaseBeanUtil.copyProperties(item, queryRes);
                    queryRes.setChildren_config(children_list.stream()
                            .filter(e -> e.getUn_serial_id().equals(item.getSerial_id()))
                            .sorted(Comparator.comparing(r -> r.getCause_name()))
                            .collect(Collectors.toList()));
                    return queryRes;
                })
                .sorted(Comparator.comparing(RepulsetReasonConfigQueryRes::getAudit_type).thenComparingInt(r -> Integer.valueOf(collect.getOrDefault(r.getCause_group(), 0))))
                .collect(Collectors.toList());
        Page<RepulsetReasonConfigQueryRes> pageResult = new Page<>();
        pageResult.setRecords(queryResList);
        pageResult.setSize(rectificationReasonReq.getPage_size());
        pageResult.setCurrent(rectificationReasonReq.getCur_page());
        pageResult.setTotal(total);
        return pageResult;
    }


    @Override
    public List<NotAllowReasonGroup> reasonGroupLimit(ReasonGroupLImitReq req) {
        LambdaQueryWrapper<RepulsetReasonConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RepulsetReasonConfig::getAudit_type, req.getAudit_type());
        wrapper.eq(RepulsetReasonConfig::getBusin_type, req.getBusin_type());
        wrapper.eq(RepulsetReasonConfig::getId_kind, req.getId_kind());
        List<RepulsetReasonConfig> list = this.list(wrapper);
        return list.stream().map(item -> {
            NotAllowReasonGroup notAllowReasonGroup = new NotAllowReasonGroup();
            notAllowReasonGroup.setCause_group(item.getCause_group());
            return notAllowReasonGroup;
        }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int batchAddReasonConfig(BaseUser baseUser, RepulsetReasonBatchForm batchForm) {
        //1 获取数据库中的值
        LambdaQueryWrapper<RepulsetReasonConfig> configWrapper = new LambdaQueryWrapper<>();

        configWrapper.in(RepulsetReasonConfig::getBusin_type, Arrays.asList(batchForm.getBusin_types().split(",")));
        configWrapper.in(RepulsetReasonConfig::getAudit_type, Arrays.asList(batchForm.getAudit_types().split(",")));
        configWrapper.in(RepulsetReasonConfig::getId_kind, Arrays.asList(batchForm.getId_kinds().split(",")));
        configWrapper.eq(RepulsetReasonConfig::getCause_group, batchForm.getCause_group());
        List<RepulsetReasonConfig> reasons = this.list(configWrapper);
        Map<String, RepulsetReasonConfig> dbMap = reasons.stream().collect(Collectors.toMap(
                it -> {
                    if (StringUtils.isBlank(it.getUn_serial_id())) {
                        return StringUtils.joinWith(",", it.getBusin_type(), it.getAudit_type(), it.getCause_group());
                    }
                    return StringUtils.joinWith(",", it.getBusin_type(), it.getAudit_type(), it.getCause_group(), it.getUn_serial_id(), it.getCause_name());
                },
                x -> x, (k, v) -> v));

        // 参数父级数据平铺
        Date date = new Date();
        List<RepulsetReasonConfig> parentConfigs = Stream.of(batchForm.getBusin_types().split(","))
                .flatMap(businType ->
                        Stream.of(batchForm.getAudit_types().split(","))
                                .flatMap(auditType ->
                                        Stream.of(batchForm.getId_kinds().split(","))
                                                .map(idKind -> {
                                                    RepulsetReasonConfig config = new RepulsetReasonConfig();
                                                    config.setUn_serial_id(" ");
                                                    config.setBusin_type(businType);
                                                    config.setId_kind(idKind);
                                                    config.setAudit_type(auditType);
                                                    config.setCreate_datetime(date);
                                                    config.setUpdate_datetime(date);
                                                    config.setCreate_by(baseUser.getStaff_no());
                                                    config.setUpdate_by(baseUser.getStaff_no());
                                                    config.setCause_group(batchForm.getCause_group());
                                                    return config;
                                                })
                                )
                )
                .collect(Collectors.toList());
        // 拼接父级要保存的数据
        List<RepulsetReasonConfig> addConfigs = Lists.newArrayList();
        parentConfigs.stream().forEach(it -> {
            if (!dbMap.containsKey(StringUtils.joinWith(",", it.getBusin_type(), it.getId_kind(), it.getAudit_type(), it.getCause_group()))) {
                String serial_id = idGenerator.nextUUID(null);
                it.setSerial_id(serial_id);
                addConfigs.add(it);
                dbMap.put(StringUtils.joinWith(",", it.getBusin_type(), it.getId_kind(), it.getAudit_type(), it.getCause_group()), it);
            }
        });

        // 参数子级数据平铺
        List<RepulsetReasonConfig> childConfigs = parentConfigs.stream()
                .flatMap(re -> batchForm.getCause_children().stream()
                        .map(it -> {
                            RepulsetReasonConfig config = new RepulsetReasonConfig();
                            config.setUn_serial_id(dbMap.get(StringUtils.joinWith(",", re.getBusin_type(), re.getId_kind(), re.getAudit_type(), re.getCause_group()))
                                    .getSerial_id());
                            config.setBusin_type(re.getBusin_type());
                            config.setId_kind(re.getId_kind());
                            config.setAudit_type(re.getAudit_type());
                            config.setCreate_datetime(date);
                            config.setUpdate_datetime(date);
                            config.setCreate_by(baseUser.getStaff_no());
                            config.setUpdate_by(baseUser.getStaff_no());
                            config.setCause_group(batchForm.getCause_group());
                            config.setCause_name(it.getCause_name());
                            config.setCause_content(it.getCause_content());
                            return config;
                        })).collect(Collectors.toList());
        // 拼接子级要保存以及变更的数据
        List<RepulsetReasonConfig> updateConfigs = Lists.newArrayList();
        childConfigs.stream().forEach(it -> {
            RepulsetReasonConfig reasonConfig = dbMap.get(StringUtils.joinWith(",", it.getBusin_type(), it.getId_kind(), it.getAudit_type(), it.getCause_group(), it.getUn_serial_id(), it.getCause_name()));
            if (null == reasonConfig) {
                String serial_id = idGenerator.nextUUID(null);
                it.setSerial_id(serial_id);
                addConfigs.add(it);
                return;
            }
            if (!StringUtils.equals(reasonConfig.getCause_content(), it.getCause_content())) {
                reasonConfig.setUpdate_by(baseUser.getStaff_no());
                reasonConfig.setUpdate_datetime(date);
                reasonConfig.setCause_content(it.getCause_content());
                updateConfigs.add(reasonConfig);
            }
        });

        // 数据库操作
        if (!CollectionUtils.isEmpty(addConfigs)) {
            this.saveBatch(addConfigs);
        }
        if (!CollectionUtils.isEmpty(updateConfigs)) {
            this.updateBatchById(updateConfigs);
        }
        return addConfigs.size() + updateConfigs.size();
    }

    @Override
    public List<RepulsetReasonConfigInfo> queryAllReasonConfig(RectificationReasonReq req) {
        LambdaQueryWrapper<RepulsetReasonConfig> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(req.getBusin_type())) {
            wrapper.eq(RepulsetReasonConfig::getBusin_type, req.getBusin_type());
        }
        wrapper.notIn(RepulsetReasonConfig::getUn_serial_id, " ");

        List<RepulsetReasonConfig> repulsetReasonConfigs = this.list(wrapper);
        // 将数据进行分组处理，顺序为：业务类型-证件类型-任务类型-原因分组
        return buildCustomTreeStructure(repulsetReasonConfigs);
    }

    public List<RepulsetReasonConfigInfo> buildCustomTreeStructure(List<RepulsetReasonConfig> configs) {
        // 按业务类型分组
        Map<String, List<RepulsetReasonConfig>> businTypeMap = configs.stream()
                .collect(Collectors.groupingBy(RepulsetReasonConfig::getBusin_type));

        return businTypeMap.values().stream()
                .map(repulsetReasonConfigs -> {
                    // 创建业务类型节点
                    RepulsetReasonConfigInfo businNode = new RepulsetReasonConfigInfo();
                    businNode.setBusin_type(repulsetReasonConfigs.get(0).getBusin_type());
                    businNode.setChildren(new ArrayList<>());

                    // 按证件类型分组
                    Map<String, List<RepulsetReasonConfig>> idKindMap = repulsetReasonConfigs.stream()
                            .collect(Collectors.groupingBy(RepulsetReasonConfig::getId_kind));

                    // 处理每个证件类型
                    idKindMap.forEach((idKind, idKindConfigs) -> {
                        RepulsetReasonConfigInfo idKindNode = new RepulsetReasonConfigInfo();
                        idKindNode.setBusin_type(idKindConfigs.get(0).getBusin_type());
                        idKindNode.setId_kind(idKindConfigs.get(0).getId_kind());
                        idKindNode.setChildren(new ArrayList<>());
                        businNode.getChildren().add(idKindNode);

                        // 按任务类型分组
                        Map<String, List<RepulsetReasonConfig>> auditTypeMap = idKindConfigs.stream()
                                .collect(Collectors.groupingBy(RepulsetReasonConfig::getAudit_type));

                        // 处理每个任务类型
                        auditTypeMap.forEach((auditType, auditConfigs) -> {
                            RepulsetReasonConfigInfo auditNode = new RepulsetReasonConfigInfo();
                            auditNode.setBusin_type(auditConfigs.get(0).getBusin_type());
                            auditNode.setId_kind(auditConfigs.get(0).getId_kind());
                            auditNode.setAudit_type(auditConfigs.get(0).getAudit_type());
                            auditNode.setChildren(new ArrayList<>());
                            idKindNode.getChildren().add(auditNode);

                            // 按原因分组分组
                            Map<String, List<RepulsetReasonConfig>> causeGroupMap = auditConfigs.stream()
                                    .collect(Collectors.groupingBy(RepulsetReasonConfig::getCause_group));

                            // 处理每个原因分组
                            causeGroupMap.forEach((causeGroup, causeGroupConfigs) -> {
                                RepulsetReasonConfigInfo causeGroupNode = new RepulsetReasonConfigInfo();
                                causeGroupNode.setBusin_type(causeGroupConfigs.get(0).getBusin_type());
                                causeGroupNode.setId_kind(causeGroupConfigs.get(0).getId_kind());
                                causeGroupNode.setAudit_type(causeGroupConfigs.get(0).getAudit_type());
                                causeGroupNode.setCause_group(causeGroupConfigs.get(0).getCause_group());
                                causeGroupNode.setChildren(new ArrayList<>());
                                auditNode.getChildren().add(causeGroupNode);

                                // 添加具体原因节点
                                causeGroupConfigs.forEach(config -> {
                                    RepulsetReasonConfigInfo reasonNode = new RepulsetReasonConfigInfo();
                                    BeanUtils.copyProperties(config, reasonNode);
                                    causeGroupNode.getChildren().add(reasonNode);
                                });
                            });
                        });
                    });
                    return businNode;
                })
                .collect(Collectors.toList());
    }

}