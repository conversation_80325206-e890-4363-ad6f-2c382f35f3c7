package com.cairh.cpe.common.constant;

public class Constant {

    public static final String UTF8 = "UTF-8";
    public static final String GBK = "GBK";

    // 券商代码
    public static final String DEFAULT_IMPL = "default";
    // 同花顺APP ID
    public static final String APP_ID_THX = "402";
    /**
     * 不需要归历史
     */
    public static final String TOHIS_FLAG_N = "0";


    /**
     * 最大字段值长度，超过该长度的字段将被截断
     */
    public static final int MAX_FILED_VALUE_LENGTH = 2000;
    /**
     * 需要归历史
     */
    public static final String TOHIS_FLAG_Y = "1";
    /**
     * 湖南金交所
     */
    public static final String COMPANY_CODE_HUNANFAE = "hunanfae";
    /**
     * 航天证券
     */
    public static final String COMPANY_CODE_CASSTOCK = "casstock";
    /**
     * 中原证券
     */
    public static final String COMPANY_CODE_CCNEW = "ccnew";
    /**
     * 中华证券
     */
    public static final String COMPANY_CODE_CESC = "cesc";
    /**
     * 财信证券
     */
    public static final String COMPANY_CODE_CFZQ = "cfzq";
    /**
     * 中金证券
     */
    public static final String COMPANY_CODE_CICC = "cicc";
    /**
     * 中邮证券
     */
    public static final String COMPANY_CODE_CNPSEC = "cnpsec";
    /**
     * 财通证券
     */
    public static final String COMPANY_CODE_CTSEC = "ctsec";
    /**
     * 大同证券
     */
    public static final String COMPANY_CODE_DTZQ = "dtzq";
    /**
     * 国海证券
     */
    public static final String COMPANY_CODE_GHZQ = "ghzq";
    /**
     * 海通证券
     */
    public static final String COMPANY_CODE_HTSEC = "htsec";
    /**
     * 恒泰证券
     */
    public static final String COMPANY_CODE_HTZQ = "htzq";
    /**
     * 华创证券
     */
    public static final String COMPANY_CODE_HCZQ = "hczq";
    /**
     * 江海证券
     */
    public static final String COMPANY_CODE_JHZQ = "jhzq";
    /**
     * 金元证券
     */
    public static final String COMPANY_CODE_JYZQ = "jyzq";
    /**
     * 民族证券
     */
    public static final String COMPANY_CODE_MZZQ = "mzzq";
    /**
     * 东北证券
     */
    public static final String COMPANY_CODE_NESC = "nesc";
    /**
     * 浙商证券
     */
    public static final String COMPANY_CODE_STOCKE = "stocke";
    /**
     * 西南证券
     */
    public static final String COMPANY_CODE_SWSC = "swsc";
    /**
     * 山西证券
     */
    public static final String COMPANY_CODE_SXSEC = "sxsec";
    /**
     * 太平洋证券
     */
    public static final String COMPANY_CODE_TPYZQ = "tpyzq";
    /**
     * 万联证券
     */
    public static final String COMPANY_CODE_WLZQ = "wlzq";
    /**
     * 西部证券
     */
    public static final String COMPANY_CODE_XBZQ = "xbzq";
    /**
     * 厦门证券 长城国瑞
     */
    public static final String COMPANY_CODE_XMZQ = "xmzq";
    /**
     * 新时代证券
     */
    public static final String COMPANY_CODE_XSDZQ = "xsdzq";
    /**
     * 兴业证券
     */
    public static final String COMPANY_CODE_XYZQ = "xyzq";
    /**
     * 开源证券
     */
    public static final String COMPANY_CODE_KYZQ = "kyzq";
    /**
     * 财达证券
     */
    public static final String COMPANY_CODE_CDZQ = "cdzq";
    /**
     * 华信证券
     */
    public static final String COMPANY_CODE_SHHXZQ = "shhxzq";
    /**
     * 爱建证券
     */
    public static final String COMPANY_CODE_AJZQ = "ajzq";
    /**
     * 国融证券
     */
    public static final String COMPANY_CODE_GRZQ = "grzq";
    /**
     * 德邦证券
     */
    public static final String COMPANY_CODE_DBZQ = "dbzq";
    /**
     * 申港证券
     */
    public static final String COMPANY_CODE_SGZQ = "sgzq";
    /**
     * 长江证券
     */
    public static final String COMPANY_CODE_CJZQ = "cjzq";
    /**
     * 华菁证券
     */
    public static final String COMPANY_CODE_HQZQ = "hqzq";
    /**
     * 湘财证券
     */
    public static final String COMPANY_CODE_XCZQ = "xczq";
    /**
     * 中投证券
     */
    public static final String COMPANY_CODE_ZTZQ = "ztzq";
    /**
     * 方正证券
     */
    public static final String COMPANY_CODE_FZZQ = "fzzq";
    /**
     * 国盛证券
     */
    public static final String COMPANY_CODE_GSZQ = "gszq";
    /**
     * 东莞证券
     */
    public static final String COMPANY_CODE_DGZQ = "dgzq";
    /**
     * 华安证券
     */
    public static final String COMPANY_CODE_LJS = "ljs";
    /**
     * 华安证券
     */
    public static final String COMPANY_CODE_HAZQ = "hazq";
    /**
     * 野村证券
     */
    public static final String COMPANY_CODE_YCZQ = "yczq";
    /**
     * 中航证券
     */
    public static final String COMPANY_CODE_ZHZQ = "zhzq";
    /**
     * 国金证券
     */
    public static final String COMPANY_CODE_GJZQ = "gjzq";
    /**
     * 华宝证券
     */
    public static final String COMPANY_CODE_HBZQ = "hbzq";
    /**
     * 宏信证券
     */
    public static final String COMPANY_CODE_HXZQ = "hxzq";
    /**
     * 蚂蚁金服
     */
    public static final String COMPANY_CODE_MYJF = "1000";
    /**
     * 国金财富
     */
    public static final String COMPANY_CODE_GJCF = "gjcf";
    /**
     * 光大证券
     */
    public static final String COMPANY_CODE_GDZQ = "gdzq";
    /**
     * 创元证券
     */
    public static final String COMPANY_CODE_CYZQ = "cyzq";
    /**
     * 金圆统一证券
     */
    public static final String COMPANY_CODE_JYTYZQ = "jytyzq";
    /**
     * 东亚前海证券
     */
    public static final String COMPANY_CODE_DYQHZQ = "dyqhzq";
    /**
     * 国联安基金
     */
    public static final String COMPANY_CODE_GLAJJ = "glajj";

    /**
     * 任务转交通道
     */
    public static final String CHANNEL_TRANSFER_TASK = "cpe.websocket.notice.redis.channel";

    /**
     * 审核年龄默认值=1
     */
    public static final Integer AUDIT_AGE_DEFAULT = 1;

    /**
     * 审核年龄最小值=18
     */
    public static final Integer AUDIT_AGE_MIN = 18;

    /**
     * 审核年龄最大值=70
     */
    public static final Integer AUDIT_AGE_MAX = 70;


    public static final String STATUS_NORMAL = "1";

    public static final String STATUS_DISABLE = "0";


    public static final String BUSIN_HIT = "1";

    public static final String BUSIN_MISS = "0";


    // 为托管分支机构
    public static final String BRANCH_MANAGED_Y = "0";
    // 非托管分支机构
    public static final String BRANCH_MANAGED_N = "1";

    public static final int TWO = 2;

    public static final String SEMICOLON_SEPARATOR = ";";

    // kafka方式发送短信服务名称
    public static final String KAFKA_SERVICE_VENDER = "gtjaKafkaSmsMessageService";

    // 通知方式 0-短信
    public static final String SEND_TYPE = "0";

    // 通知渠道类型
    public static final String CHANNEL_TYPE = "GTJA";
}
