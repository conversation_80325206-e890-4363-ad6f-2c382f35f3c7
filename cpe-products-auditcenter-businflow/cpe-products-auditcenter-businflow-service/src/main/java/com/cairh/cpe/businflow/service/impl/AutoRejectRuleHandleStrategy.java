package com.cairh.cpe.businflow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.cairh.cpe.common.constant.AutoRejectSourceTypeEnum;
import com.cairh.cpe.common.constant.StagingRuleTypeEnum;
import com.cairh.cpe.common.constant.WskhConstant;
import com.cairh.cpe.common.dto.AutoRejectReasonInfo;
import com.cairh.cpe.common.dto.BaseRuleInfo;
import com.cairh.cpe.common.dto.RejectReason;
import com.cairh.cpe.common.dto.Rule;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.RuleConfiguration;
import com.cairh.cpe.common.entity.StagingTaskRule;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.clob.MatchInfo;
import com.cairh.cpe.common.entity.request.AiAuditRuleReq;
import com.cairh.cpe.common.service.IStagingTaskRuleService;
import com.cairh.cpe.common.util.BaseBeanUtil;
import com.cairh.cpe.common.util.RuleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 规则匹配处理接口
 *
 * <AUTHOR>
 * @since 2025/5/21 16:41
 */
@Slf4j
@Service
public class AutoRejectRuleHandleStrategy extends AbstractRuleMatchingStrategy {

    @Resource
    private IStagingTaskRuleService stagingTaskRuleService;

    @Override
    public BaseRuleInfo ruleMatch(BusinFlowTask businFlowTask, ClobContentInfo clobContentInfo) {
        // 获取并过滤符合条件的基础信息自动驳回规则
        List<StagingTaskRule> matchedRules = getMatchedAiAuditRules(businFlowTask, clobContentInfo, AutoRejectSourceTypeEnum.BASE_INFO);
        List<RuleConfiguration> allRules = ruleConfigurationService.findAllRules();

        // 遍历匹配的规则进行规则匹配
        for (StagingTaskRule stagingTaskRule : matchedRules) {
            String expression = stagingTaskRule.getExpression();
            if (StringUtils.isBlank(expression)) {
                continue;
            }
            Rule rule = parseRule(expression);
            if (rule == null) {
                continue;
            }
            MatchInfo matchInfo = buildMatchInfo(businFlowTask, clobContentInfo);
            if (RuleUtil.match(matchInfo, rule, allRules)) {
                BaseRuleInfo baseRuleInfo = new BaseRuleInfo();
                BaseBeanUtil.copyProperties(stagingTaskRule, baseRuleInfo);
                List<RejectReason> rejectReasonList = Arrays.stream(baseRuleInfo.getReject_reason().split(",")).map(id -> {
                    RejectReason rejectReason = new RejectReason();
                    rejectReason.setReject_reason_id(id);
                    rejectReason.setReject_reason_type(WskhConstant.REJECT);
                    return rejectReason;
                }).collect(Collectors.toList());
                baseRuleInfo.setRejectReasonList(rejectReasonList);
                return baseRuleInfo;
            }
        }
        return null;
    }

    /**
     * 智能审核自动驳回规则匹配-智能审核
     */
    public BaseRuleInfo aiAuditRuleMatch(BusinFlowTask businFlowTask, ClobContentInfo clobContentInfo, List<AiAuditRuleReq> aiAuditRuleReqs) {
        // 获取禁止智能审核项目
        Set<String> drlRuleNameSet = aiAuditRuleReqs.stream().map(AiAuditRuleReq::getDrl_rule_name).collect(Collectors.toSet());

        // 获取并过滤符合条件的自动驳回规则
        List<StagingTaskRule> matchedRules = getMatchedAiAuditRules(businFlowTask, clobContentInfo, AutoRejectSourceTypeEnum.AI_AUDIT);

        // 查找匹配的规则和驳回原因
        for (StagingTaskRule stagingTaskRule : matchedRules) {
            // 查找匹配的智能审核驳回原因
            List<String> matchedRejectReasons = findMatchedRejectReasons(stagingTaskRule, drlRuleNameSet);

            if (!matchedRejectReasons.isEmpty()) {
                List<RejectReason> rejectReasonList = matchedRejectReasons.stream().map(id -> {
                    RejectReason rejectReason = new RejectReason();
                    rejectReason.setReject_reason_id(id);
                    rejectReason.setReject_reason_type(WskhConstant.AIAUDIT_REJECT);
                    return rejectReason;
                }).collect(Collectors.toList());

                BaseRuleInfo baseRuleInfo = new BaseRuleInfo();
                BaseBeanUtil.copyProperties(stagingTaskRule, baseRuleInfo);

                baseRuleInfo.setRejectReasonList(rejectReasonList);

                // 智能审核匹配到规则后，对规则中的基础数据进行数据匹配
                return aiAuditBaseInfoRuleMatch(businFlowTask, clobContentInfo, baseRuleInfo);
            }
        }
        return null;
    }

    /**
     * 获取匹配的AI审核规则
     */
    private List<StagingTaskRule> getMatchedAiAuditRules(BusinFlowTask businFlowTask, ClobContentInfo clobContentInfo, AutoRejectSourceTypeEnum typeEnum) {
        List<StagingTaskRule> allAutoRejectRules = stagingTaskRuleService.findAllStagingTaskRules();
        return allAutoRejectRules.stream()
                .filter(rule -> StringUtils.equals(rule.getRule_type(), StagingRuleTypeEnum.AUTO_REJECT.getCode()))
                .filter(rule -> StringUtils.equals(rule.getReject_type(), typeEnum.getCode()))
                .filter(rule -> StringUtils.equals(rule.getTask_type(), businFlowTask.getTask_type()))
                .filter(rule -> StringUtils.equals(rule.getBusin_type(), clobContentInfo.getBusin_type()))
                .filter(rule -> StringUtils.equals(rule.getId_kind(), clobContentInfo.getId_kind()))
                .collect(Collectors.toList());
    }

    /**
     * 查找匹配的驳回原因
     */
    private List<String> findMatchedRejectReasons(StagingTaskRule stagingTaskRule, Set<String> drlRuleNameSet) {
        List<AutoRejectReasonInfo> autoRejectReasonInfos = JSON.parseArray(stagingTaskRule.getAiaudit_reject_reason(), AutoRejectReasonInfo.class);

        if (CollectionUtil.isEmpty(autoRejectReasonInfos)) {
            return Collections.emptyList();
        }

        return autoRejectReasonInfos.stream()
                .filter(reasonInfo -> drlRuleNameSet.contains(reasonInfo.getDrl_rule_name()))
                .map(AutoRejectReasonInfo::getReject_reason)
                .collect(Collectors.toList());
    }

    /**
     * 智能审核自动驳回规则匹配-基础数据
     */
    private BaseRuleInfo aiAuditBaseInfoRuleMatch(BusinFlowTask businFlowTask, ClobContentInfo clobContentInfo, BaseRuleInfo baseRuleInfo) {
        String expression = baseRuleInfo.getExpression();

        // 如果没有表达式，直接使用智能审核驳回原因
        if (StringUtils.isBlank(expression)) {
            return baseRuleInfo;
        }

        // 解析规则表达式
        Rule rule = parseRule(expression);
        if (rule == null) {
            return null;
        }

        // 构建匹配信息并进行规则匹配
        MatchInfo matchInfo = buildMatchInfo(businFlowTask, clobContentInfo);
        List<RuleConfiguration> allRules = ruleConfigurationService.findAllRules();

        if (RuleUtil.match(matchInfo, rule, allRules)) {
            // 基础数据匹配成功，合并驳回原因
            if (StringUtils.isNotBlank(baseRuleInfo.getReject_reason())) {
                List<RejectReason> rejectReasonList = Arrays.stream(baseRuleInfo.getReject_reason().split(",")).map(id -> {
                    RejectReason rejectReason = new RejectReason();
                    rejectReason.setReject_reason_id(id);
                    rejectReason.setReject_reason_type(WskhConstant.REJECT);
                    return rejectReason;
                }).collect(Collectors.toList());
                baseRuleInfo.getRejectReasonList().addAll(rejectReasonList);
            }
            return baseRuleInfo;
        }

        return null;
    }

    /**
     * 构建匹配信息对象
     */
    private MatchInfo buildMatchInfo(BusinFlowTask businFlowTask, ClobContentInfo clobContentInfo) {
        MatchInfo matchInfo = new MatchInfo();
        BaseBeanUtil.copyProperties(clobContentInfo, matchInfo);
        matchInfo.setTask_status(businFlowTask.getTask_status());
        matchInfo.setTask_type(businFlowTask.getTask_type());
        matchInfo.setUp_branch_no(getUpBranchNo(clobContentInfo.getBranch_no()));
        return matchInfo;
    }
}
