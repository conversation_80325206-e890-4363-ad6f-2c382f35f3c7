package com.cairh.cpe.businflow.service.impl;

import com.cairh.cpe.common.constant.StagingRuleTypeEnum;
import com.cairh.cpe.common.dto.BaseRuleInfo;
import com.cairh.cpe.common.dto.Rule;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.RuleConfiguration;
import com.cairh.cpe.common.entity.StagingTaskRule;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.clob.MatchInfo;
import com.cairh.cpe.common.service.IStagingTaskRuleService;
import com.cairh.cpe.common.util.BaseBeanUtil;
import com.cairh.cpe.common.util.RuleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 规则匹配处理接口
 *
 * <AUTHOR>
 * @since 2025/3/12 17:41
 */
@Slf4j
@Service
public class StagingTaskRuleHandleStrategy extends AbstractRuleMatchingStrategy {

    @Resource
    private IStagingTaskRuleService stagingTaskRuleService;

    @Override
    public BaseRuleInfo ruleMatch(BusinFlowTask businFlowTask, ClobContentInfo clobContentInfo) {
        List<RuleConfiguration> allRules = ruleConfigurationService.findAllRules();
        List<StagingTaskRule> allStagingTaskRules = stagingTaskRuleService.findAllStagingTaskRules();
        List<StagingTaskRule> autoRejectRules = allStagingTaskRules.stream()
                .filter(stagingTaskRule -> StringUtils.equals(stagingTaskRule.getRule_type(), StagingRuleTypeEnum.STAGING_TASK.getCode()))
                .collect(Collectors.toList());
        for (StagingTaskRule stagingTaskRule : autoRejectRules) {
            // 检查时间范围是否匹配
            if (!isTimeRangeValid(stagingTaskRule, businFlowTask)) {
                continue;
            }
            String expression = stagingTaskRule.getExpression();
            if (StringUtils.isBlank(expression)) {
                continue;
            }
            Rule rule = parseRule(expression);
            if (rule == null) {
                continue;
            }
            MatchInfo stagingMatchInfo = new MatchInfo();
            BaseBeanUtil.copyProperties(clobContentInfo, stagingMatchInfo);
            stagingMatchInfo.setTask_status(businFlowTask.getTask_status());
            stagingMatchInfo.setTask_type(businFlowTask.getTask_type());
            stagingMatchInfo.setUp_branch_no(getUpBranchNo(clobContentInfo.getBranch_no()));
            if (RuleUtil.match(stagingMatchInfo, rule, allRules)) {
                BaseRuleInfo baseRuleInfo = new BaseRuleInfo();
                BaseBeanUtil.copyProperties(stagingTaskRule, baseRuleInfo);
                return baseRuleInfo;
            }
        }
        return null;
    }

    private boolean isTimeRangeValid(StagingTaskRule stagingTaskRule, BusinFlowTask businFlowTask) {
        Date startTime = stagingTaskRule.getRule_datetime_start();
        Date endTime = stagingTaskRule.getRule_datetime_end();
        Date createDatetime = businFlowTask.getCreate_datetime();
        if (startTime == null || endTime == null) {
            return false;
        }
        return startTime.before(createDatetime) && endTime.after(createDatetime);
    }
}
