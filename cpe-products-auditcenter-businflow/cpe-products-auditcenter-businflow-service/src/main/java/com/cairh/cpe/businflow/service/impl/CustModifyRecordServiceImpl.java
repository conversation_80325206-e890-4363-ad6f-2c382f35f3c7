package com.cairh.cpe.businflow.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.businflow.entity.CustModifyRecord;
import com.cairh.cpe.businflow.entity.request.ModifyRecordReq;
import com.cairh.cpe.businflow.entity.response.ModifyRecordResult;
import com.cairh.cpe.businflow.mapper.CustModifyRecordMapper;
import com.cairh.cpe.businflow.service.ICustModifyRecordService;
import com.cairh.cpe.cache.service.CacheDict;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowRecordEnum;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.clob.*;
import com.cairh.cpe.common.entity.request.AuditChangeForm;
import com.cairh.cpe.common.service.IRequestFlowService;
import com.cairh.cpe.common.service.IRequestService;
import com.cairh.cpe.common.util.BeanMapUtil;
import com.cairh.cpe.common.util.SqlDateUtil;
import com.cairh.cpe.db.config.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CustModifyRecordServiceImpl extends ServiceImpl<CustModifyRecordMapper, CustModifyRecord> implements ICustModifyRecordService {

    @Autowired
    private IRequestService requestService;
    @Autowired
    private IRequestFlowService requestFlowService;
    @Resource
    private IdGenerator idGenerator;
    @Autowired
    private CacheDict cacheDict;
    @Resource
    private RedisTemplate redisTemplate;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<CustModifyRecord> saveParamsAndRecord(AuditChangeForm auditChangeForm) {
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(auditChangeForm.getRequest_no());
        auditChangeForm.setBusin_type(clobContentInfo.getBusin_type());
        List<CustModifyRecord> custModifyRecords = new ArrayList<>();
        Map<String, Object> params = new HashMap<>();

        // 档案信息
        userIndependenceInfo(clobContentInfo, auditChangeForm, params, custModifyRecords);
        // 身份证信息
        changeIdCardInfo(clobContentInfo, auditChangeForm, params, custModifyRecords);
        // 客户补充信息
        changeUserBaseInfo(clobContentInfo, auditChangeForm, params, custModifyRecords);
        // 税收信息
        saveRevenueInfo(clobContentInfo, auditChangeForm, params, custModifyRecords);
        // rpc信息
        changeUserRpcInfo(clobContentInfo, auditChangeForm, params, custModifyRecords);

        if (!params.isEmpty() && CollectionUtils.isNotEmpty(custModifyRecords)) {
            String modify_item = custModifyRecords.get(0).getModify_item();
            if (!StringUtils.equals(auditChangeForm.getSaveCustmodifyRecordFlag(), "1")) {
                log.info("修改记录参数[{}]", JSON.toJSONString(custModifyRecords));
                saveBatch(custModifyRecords);

                String modifyContent = "修改项目：";
                List<String> recordList = custModifyRecords.stream().map(record -> cacheDict.getDictDesc(DicConstant.MODIFY_ITEM, record.getModify_item())).collect(Collectors.toList());
                modifyContent += StringUtils.join(recordList, "，");
                if (StringUtils.isNotBlank(auditChangeForm.getModify_reason()) && ("address".equals(modify_item)
                        || "choose_branch_reason".equals(modify_item)
                        || "choose_profession_reason".equals(modify_item)
                        || "profession_code".equals(modify_item)
                        || "work_unit".equals(modify_item))
                ) {
                    modifyContent += "；修改原因：" + auditChangeForm.getModify_reason();
                } else {
                    String chooseReasonStr = custModifyRecords.stream().filter(r -> !StringUtils.equals(cacheDict.getDictDesc(DicConstant.MODIFY_REASON, r.getModify_item()), r.getModify_item()))
                            .map(item -> cacheDict.getDictDesc(DicConstant.MODIFY_REASON, item.getModify_item())).collect(Collectors.joining("；"));
                    if (StringUtils.isNotBlank(chooseReasonStr)) {
                        modifyContent += "；修改原因：" + chooseReasonStr;
                    }
                }
                if (null != auditChangeForm.getIndependenceInfo_one()) {
                    params.put(Fields.UPLOAD_80_TYPE, auditChangeForm.getIndependenceInfo_one().getUpload_80_type());
                }
                params.put(Fields.OPERATOR_NO, auditChangeForm.getOperator_no());
                params.put(Fields.OPERATOR_NAME, auditChangeForm.getOperator_name());
                params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_OPERATE_TYPE);
                params.put(Fields.BUSINESS_REMARK, modifyContent);
                params.put(Fields.BUSINESS_FLAG, FlowRecordEnum.B1017.getValue());
            }
            if (StringUtils.isNotBlank(auditChangeForm.getChoose_branch_reason_content())) {
                params.put("choose_branch_reason_content", auditChangeForm.getChoose_branch_reason_content());
            }
            if ("".equals(auditChangeForm.getChoose_profession_reason_content()) || StringUtils.isNotBlank(auditChangeForm.getChoose_profession_reason_content())) {
                params.put("choose_profession_reason_content", auditChangeForm.getChoose_profession_reason_content());
            }
            if (StringUtils.isNotBlank(auditChangeForm.getTranslation_address())) {
                params.put("translation_address", auditChangeForm.getTranslation_address());
            }
            if (StringUtils.isNotBlank(auditChangeForm.getAlternative_address())) {
                params.put("alternative_address", auditChangeForm.getAlternative_address());
            }
            if (StringUtils.isNotBlank(auditChangeForm.getAddress_modify_reason())) {
                params.put("address_modify_reason", auditChangeForm.getAddress_modify_reason());
            }
            if (StringUtils.isNotBlank(auditChangeForm.getChoose_branch_reason_modify_reason())) {
                params.put("choose_branch_reason_modify_reason", auditChangeForm.getChoose_branch_reason_modify_reason());
            }
            if (StringUtils.isNotBlank(auditChangeForm.getChoose_profession_reason_modify_reason())) {
                params.put("choose_profession_reason_modify_reason", auditChangeForm.getChoose_profession_reason_modify_reason());
            }
            if (StringUtils.isNotBlank(auditChangeForm.getProfession_code_modify_reason())) {
                params.put("profession_code_modify_reason", auditChangeForm.getProfession_code_modify_reason());
            }
            if (StringUtils.isNotBlank(auditChangeForm.getWork_unit_modify_reason())) {
                params.put("work_unit_modify_reason", auditChangeForm.getWork_unit_modify_reason());
            }
            if (Objects.nonNull(auditChangeForm.getId_card_info()) && StringUtils.isNotBlank(auditChangeForm.getId_card_info().getBirthday())) {
                params.put(Fields.BIRTHDAY, auditChangeForm.getId_card_info().getBirthday());
            }
            params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_OPERATE_TYPE);
            requestFlowService.saveParamsRecord(auditChangeForm.getRequest_no(), params);
            redisTemplate.opsForList().leftPush(QueueConstant.QUERY_CLOB_QUEUE, auditChangeForm.getRequest_no());
        }
        return custModifyRecords;
    }

    @Override
    public List<CustModifyRecord> queryModifyRecordById(String request_no) {
        return this.list(new LambdaQueryWrapper<>(CustModifyRecord.class)
                .eq(CustModifyRecord::getRequest_no, request_no)
                .orderByDesc(CustModifyRecord::getModify_datetime));
    }

    private void userIndependenceInfo(ClobContentInfo clobContentInfo, AuditChangeForm auditChangeForm, Map<String, Object> params, List<CustModifyRecord> custModifyRecords) {
        if (auditChangeForm.getIndependenceInfo() != null) {
            UserIndependenceInfo independenceInfo = auditChangeForm.getIndependenceInfo();
            UserIndependenceInfo independenceInfo_one = auditChangeForm.getIndependenceInfo_one();
            Date currentDate = new Date();

            if (StringUtils.isNotBlank(independenceInfo.getFile_6A()) && StringUtils.isNotBlank(independenceInfo.getFile_6B())) {
                // 网厅业务办理-个人
                if (StringUtils.equals(ClientCategoryEnum.CLIENT_PERSON.getCode(), clobContentInfo.getClient_category())) {
                    params.put("photo_front", independenceInfo.getFile_6A());
                    params.put("photo_back", independenceInfo.getFile_6B());
                    // TODO 客户修改记录暂定是否添加
                    if (StringUtils.equals(auditChangeForm.getSaveCustmodifyRecordFlag(), "1")) {
                        custModifyRecords.add(createRecord("photo_front", clobContentInfo.getOriginal_photo_front(), independenceInfo.getFile_6A(), auditChangeForm, currentDate));
                        custModifyRecords.add(createRecord("photo_back", clobContentInfo.getOriginal_photo_back(), independenceInfo.getFile_6B(), auditChangeForm, currentDate));
                    } else {
                        if (null != independenceInfo_one) {
                            if (StringUtils.isNotBlank(independenceInfo_one.getFile_6A())) {
                                custModifyRecords.add(createRecord("photo_front", clobContentInfo.getPhoto_front(), independenceInfo.getFile_6A(), auditChangeForm, currentDate));
                            }
                            if (StringUtils.isNotBlank(independenceInfo_one.getFile_6B())) {
                                custModifyRecords.add(createRecord("photo_back", clobContentInfo.getPhoto_back(), independenceInfo.getFile_6B(), auditChangeForm, currentDate));
                            }
                        }
                    }
                    // 网厅业务办理-机构
                } else if (StringUtils.equalsAny(clobContentInfo.getClient_category(), ClientCategoryEnum.CLIENT_INSTITUTION.getCode(), ClientCategoryEnum.CLIENT_PRODUCT.getCode())) {
                    params.put("agent_photo_front", independenceInfo.getFile_6A());
                    params.put("agent_photo_back", independenceInfo.getFile_6B());
                    if (StringUtils.equals(auditChangeForm.getSaveCustmodifyRecordFlag(), "1")) {
                        custModifyRecords.add(createRecord("agent_photo_front", clobContentInfo.getOriginal_agent_photo_front(), independenceInfo.getFile_6A(), auditChangeForm, currentDate));
                        custModifyRecords.add(createRecord("agent_photo_back", clobContentInfo.getOriginal_agent_photo_back(), independenceInfo.getFile_6B(), auditChangeForm, currentDate));
                    } else {
                        if (null != independenceInfo_one) {
                            if (StringUtils.isNotBlank(independenceInfo_one.getFile_6A())) {
                                custModifyRecords.add(createRecord("agent_photo_front", clobContentInfo.getAgent_photo_front(), independenceInfo.getFile_6A(), auditChangeForm, currentDate));
                            }
                            if (StringUtils.isNotBlank(independenceInfo_one.getFile_6B())) {
                                custModifyRecords.add(createRecord("agent_photo_back", clobContentInfo.getAgent_photo_back(), independenceInfo.getFile_6B(), auditChangeForm, currentDate));
                            }
                        }
                    }
                } else {
                    params.put("file_6A", independenceInfo.getFile_6A());
                    params.put("file_6B", independenceInfo.getFile_6B());
                    if (StringUtils.equals(auditChangeForm.getSaveCustmodifyRecordFlag(), "1")) {
                        custModifyRecords.add(createRecord("file_6A", clobContentInfo.getOriginal_file_6A(), independenceInfo.getFile_6A(), auditChangeForm, currentDate));
                        custModifyRecords.add(createRecord("file_6B", clobContentInfo.getOriginal_file_6B(), independenceInfo.getFile_6B(), auditChangeForm, currentDate));
                    } else {
                        if (null != independenceInfo_one) {
                            if (StringUtils.isNotBlank(independenceInfo_one.getFile_6A())) {
                                custModifyRecords.add(createRecord("file_6A", clobContentInfo.getFile_6A(), independenceInfo.getFile_6A(), auditChangeForm, currentDate));
                            }
                            if (StringUtils.isNotBlank(independenceInfo_one.getFile_6B())) {
                                custModifyRecords.add(createRecord("file_6B", clobContentInfo.getFile_6B(), independenceInfo.getFile_6B(), auditChangeForm, currentDate));
                            }
                        }
                    }
                }
            }
            if (StringUtils.isNotBlank(independenceInfo.getFile_7C()) && StringUtils.isNotBlank(independenceInfo.getFile_7D())) {
                params.put("file_7C", independenceInfo.getFile_7C());
                params.put("file_7D", independenceInfo.getFile_7D());
                if (StringUtils.equals(auditChangeForm.getSaveCustmodifyRecordFlag(), "1")) {
                    custModifyRecords.add(createRecord("file_7C", clobContentInfo.getOriginal_file_7C(), independenceInfo.getFile_7C(), auditChangeForm, currentDate));
                    custModifyRecords.add(createRecord("file_7D", clobContentInfo.getOriginal_file_7D(), independenceInfo.getFile_7D(), auditChangeForm, currentDate));
                } else {
                    if (null != independenceInfo_one) {
                        if (StringUtils.isNotBlank(independenceInfo_one.getFile_7C())) {
                            custModifyRecords.add(createRecord("file_7C", clobContentInfo.getFile_7C(), independenceInfo.getFile_7C(), auditChangeForm, currentDate));
                        }
                        if (StringUtils.isNotBlank(independenceInfo_one.getFile_7D())) {
                            custModifyRecords.add(createRecord("file_7D", clobContentInfo.getFile_7D(), independenceInfo.getFile_7D(), auditChangeForm, currentDate));
                        }
                    }
                }
            }
            if (StringUtils.isNotBlank(independenceInfo.getFile_Bm())) {
                params.put("file_Bm", independenceInfo.getFile_Bm());
                if (StringUtils.equals(auditChangeForm.getSaveCustmodifyRecordFlag(), "1")) {
                    custModifyRecords.add(createRecord("file_Bm", clobContentInfo.getOriginal_file_Bm(), independenceInfo.getFile_Bm(), auditChangeForm, currentDate));
                } else {
                    if (null != independenceInfo_one) {
                        if (StringUtils.isNotBlank(independenceInfo_one.getFile_Bm())) {
                            custModifyRecords.add(createRecord("file_Bm", clobContentInfo.getFile_Bm(), independenceInfo.getFile_Bm(), auditChangeForm, currentDate));
                        }
                    }
                }
            }
            if (StringUtils.isNotBlank(independenceInfo.getFile_7W())) {
                params.put("file_7W", independenceInfo.getFile_7W());
                if (StringUtils.equals(auditChangeForm.getSaveCustmodifyRecordFlag(), "1")) {
                    custModifyRecords.add(createRecord("file_7W", clobContentInfo.getOriginal_file_7W(), independenceInfo.getFile_7W(), auditChangeForm, currentDate));
                } else {
                    if (null != independenceInfo_one) {
                        if (StringUtils.isNotBlank(independenceInfo_one.getFile_7W())) {
                            custModifyRecords.add(createRecord("file_7W", clobContentInfo.getFile_7W(), independenceInfo.getFile_7W(), auditChangeForm, currentDate));
                        }
                    }
                }
            }
            if (StringUtils.isNotBlank(independenceInfo.getFile_7X())) {
                params.put("file_7X", independenceInfo.getFile_7X());
                if (StringUtils.equals(auditChangeForm.getSaveCustmodifyRecordFlag(), "1")) {
                    custModifyRecords.add(createRecord("file_7X", clobContentInfo.getOriginal_file_7X(), independenceInfo.getFile_7X(), auditChangeForm, currentDate));
                } else {
                    if (null != independenceInfo_one) {
                        if (StringUtils.isNotBlank(independenceInfo_one.getFile_7X())) {
                            custModifyRecords.add(createRecord("file_7X", clobContentInfo.getFile_7X(), independenceInfo.getFile_7X(), auditChangeForm, currentDate));
                        }
                    }
                }
            }
            if (StringUtils.isNotBlank(independenceInfo.getFile_80())) {
                params.put("file_80", independenceInfo.getFile_80());
                if (StringUtils.equals(auditChangeForm.getSaveCustmodifyRecordFlag(), "1")) {
                    custModifyRecords.add(createRecord("file_80", clobContentInfo.getOriginal_file_80(), independenceInfo.getFile_80(), auditChangeForm));
                } else {
                    if (null != independenceInfo_one && StringUtils.isNotBlank(independenceInfo_one.getFile_80())) {
                        custModifyRecords.add(createRecord("file_80", clobContentInfo.getFile_80(), independenceInfo.getFile_80(), auditChangeForm));
                    }
                }
            }
        }
    }

    private void changeIdCardInfo(ClobContentInfo clobContentInfo, AuditChangeForm auditChangeForm, Map<String, Object> params, List<CustModifyRecord> custModifyRecords) {
        if (auditChangeForm.getId_card_info() != null) {
            //修改前的内容
            IDCardInfo initialObj = clobContentInfo.getId_card_info();
            if (initialObj != null) {
                if (StringUtils.equals(auditChangeForm.getSaveCustmodifyRecordFlag(), "1")) {//特殊处理。修改前的内容为原始数据
                    initialObj.setId_address(clobContentInfo.getOriginal_id_address());
                    initialObj.setId_enddate(clobContentInfo.getOriginal_id_enddate());
                    initialObj.setId_begindate(clobContentInfo.getOriginal_id_begindate());
                    initialObj.setAuxiliary_id_address(clobContentInfo.getOriginal_auxiliary_id_address());
                    initialObj.setAuxiliary_id_begindate(clobContentInfo.getOriginal_auxiliary_id_begindate());
                    initialObj.setAuxiliary_id_enddate(clobContentInfo.getOriginal_auxiliary_id_enddate());
                    initialObj.setPrev_id_number(clobContentInfo.getOriginal_prev_id_number());
                    initialObj.setNationality(clobContentInfo.getOriginal_nationality());
                    initialObj.setEnglish_name(clobContentInfo.getOriginal_english_name());
                    initialObj.setBirthday(clobContentInfo.getOriginal_birthday());
                }
                HashMap<String, Object> changeMap = BeanMapUtil.classIsEqual(initialObj, auditChangeForm.getId_card_info());
                if (!changeMap.isEmpty()) {
                    IDCardInfo id_card_info = new IDCardInfo();
                    Map<String, Object> initialMap = BeanMapUtil.beanToMap(initialObj);
                    initialMap.putAll(changeMap);
                    BeanMapUtil.mapToBean(initialMap, id_card_info);
                    if (clobContentInfo.getId_kind().equals(WskhConstant.ID_TYPE_0)) {
                        params.put(Fields.CLIENT_NAME, id_card_info.getClient_name());
                        params.put(Fields.ID_NO, id_card_info.getId_no());
                    }
                    params.put(Fields.ID_CARD_INFO, id_card_info);
                    custModifyRecords.addAll(swapCustModifyRecords(initialObj, auditChangeForm.getId_card_info(), auditChangeForm));
                }
            }
        }
    }

    public <T> List<CustModifyRecord> swapCustModifyRecords(T object1, T object2, AuditChangeForm auditChangeForm) {
        List<CustModifyRecord> custModifyRecords = new ArrayList<>();
        Class c1 = object1.getClass();
        Field[] fields1 = c1.getDeclaredFields();
        Object tempValue1, tempValue2;
        for (Field field : fields1) {
            field.setAccessible(true);
            try {
                tempValue1 = field.get(object1);
                tempValue2 = field.get(object2);

                if (ObjectUtils.isEmpty(tempValue1) && ObjectUtils.isEmpty(tempValue2)) {
                    continue;
                }
                if (tempValue2 != null && !tempValue2.equals(tempValue1)) {
                    custModifyRecords.add(createRecord(field.getName(), tempValue1, tempValue2, auditChangeForm));
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return custModifyRecords;
    }

    @Override
    public CustModifyRecord createRecord(String fieldName, Object initValue, Object changeValue, AuditChangeForm auditChangeForm) {
        return createRecord(fieldName, initValue, changeValue, auditChangeForm, new Date());
    }

    private CustModifyRecord createRecord(String fieldName, Object initValue, Object changeValue, AuditChangeForm auditChangeForm, Date date) {
        CustModifyRecord custModifyRecord = new CustModifyRecord();
        custModifyRecord.setSerial_id(idGenerator.nextUUID(null));
        custModifyRecord.setModify_item(fieldName);
        custModifyRecord.setModify_beforecontent(ObjectUtil.isEmpty(initValue) ? " " : String.valueOf(initValue));
        custModifyRecord.setModify_aftercontent(ObjectUtil.isEmpty(changeValue) ? " " : String.valueOf(changeValue));
        custModifyRecord.setModify_datetime(date);
        custModifyRecord.setModify_link(auditChangeForm.getModify_link());
        custModifyRecord.setRequest_no(auditChangeForm.getRequest_no());
        custModifyRecord.setOperator_no(auditChangeForm.getOperator_no());
        custModifyRecord.setOperator_name(auditChangeForm.getOperator_name());
        custModifyRecord.setTohis_flag(Constant.TOHIS_FLAG_N);
        custModifyRecord.setBusin_type(auditChangeForm.getBusin_type());
        custModifyRecord.setFlow_task_id(auditChangeForm.getFlow_task_id());
        SqlDateUtil.setDefaultValue(custModifyRecord);
        return custModifyRecord;
    }

    private void changeUserBaseInfo(ClobContentInfo clobContentInfo, AuditChangeForm auditChangeForm, Map<String, Object> params, List<CustModifyRecord> custModifyRecords) {
        UserBaseInfo userBaseInfo = auditChangeForm.getUser_base_info();
        if (userBaseInfo != null) {
            UserBaseInfo initialObj = clobContentInfo.getUser_base_info();
            if (initialObj != null) {
                if (!ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, clobContentInfo.getBusin_type())) {
                    if (StringUtils.equals(auditChangeForm.getSaveCustmodifyRecordFlag(), "1")) {//特殊处理。修改前的内容为原始数据
                        initialObj.setAddress(clobContentInfo.getOriginal_address());
                        //字典转换
                        initialObj.setProfession_code(clobContentInfo.getOriginal_profession_code());
                        initialObj.setWork_unit(clobContentInfo.getOriginal_work_unit());
                        String dictDesc = cacheDict.getDictDesc(DicConstant.CHOOSE_BRANCH_REASON, clobContentInfo.getOriginal_choose_branch_reason());
                        initialObj.setChoose_branch_reason(dictDesc);
                        String dictDesc1 = cacheDict.getDictDesc(DicConstant.CHOOSE_PROFESSION_REASON, clobContentInfo.getOriginal_choose_profession_reason());
                        initialObj.setChoose_profession_reason(dictDesc1);
                        if (StringUtils.isNotEmpty(userBaseInfo.getDishonest_record())) {
                            initialObj.setDishonest_record(clobContentInfo.getOriginal_dishonest_record());
                            initialObj.setDishonest_record_remark(clobContentInfo.getOriginal_dishonest_record_remark());
                        }
                        initialObj.setProfession_other(clobContentInfo.getOriginal_profession_other());
                    } else {
                        if (StringUtils.isEmpty(userBaseInfo.getDishonest_record())) {
                            initialObj.setDishonest_record("");
                            initialObj.setDishonest_record_remark("");
                        }
                    }
                } else {
                    if (StringUtils.equals(auditChangeForm.getSaveCustmodifyRecordFlag(), "1")) {//特殊处理。修改前的内容为原始数据
                        // 网厅业务办理-失信记录
                        initialObj = new UserBaseInfo();
                        if (StringUtils.isNotEmpty(userBaseInfo.getDishonest_record())) {
                            initialObj.setDishonest_record(clobContentInfo.getOriginal_dishonest_record());
                            initialObj.setDishonest_record_remark(clobContentInfo.getOriginal_dishonest_record_remark());
                        }
                    } else {
                        if (StringUtils.isEmpty(userBaseInfo.getDishonest_record())) {
                            initialObj.setDishonest_record("");
                            initialObj.setDishonest_record_remark("");
                        }
                    }
                }
                custModifyUserBaseInfoHandle(initialObj, auditChangeForm, params, custModifyRecords);
            }
        }
    }

    private void custModifyUserBaseInfoHandle(UserBaseInfo initialObj, AuditChangeForm auditChangeForm, Map<String, Object> params, List<CustModifyRecord> custModifyRecords) {
        HashMap<String, Object> changeMap = BeanMapUtil.classIsEqual(initialObj, auditChangeForm.getUser_base_info());
        if (!changeMap.isEmpty()) {
            UserBaseInfo userBaseInfo = new UserBaseInfo();
            Map<String, Object> initialMap = BeanMapUtil.beanToMap(initialObj);
            initialMap.putAll(changeMap);
            BeanMapUtil.mapToBean(initialMap, userBaseInfo);
            params.put(Fields.USER_BASE_INFO, userBaseInfo);
            if (StringUtils.isNotEmpty(userBaseInfo.getDishonest_record())) {
                params.put(Fields.DISHONEST_RECORD, userBaseInfo.getDishonest_record());
                params.put(Fields.DISHONEST_RECORD_REMARK, userBaseInfo.getDishonest_record_remark());
            }
            custModifyRecords.addAll(swapCustModifyRecords(initialObj, auditChangeForm.getUser_base_info(), auditChangeForm));
        }
    }

    private void saveRevenueInfo(ClobContentInfo clobContentInfo, AuditChangeForm auditChangeForm, Map<String, Object> params, List<CustModifyRecord> custModifyRecords) {
        if (auditChangeForm.getRevenue_info() != null) {
            RevenueInfo initialObj = clobContentInfo.getRevenue_info();
            if (initialObj != null) {
                HashMap<String, Object> changeMap = BeanMapUtil.classIsEqual(initialObj, auditChangeForm.getRevenue_info());
                if (!changeMap.isEmpty()) {
                    RevenueInfo revenue_info = new RevenueInfo();
                    Map<String, Object> initialMap = BeanMapUtil.beanToMap(initialObj);
                    initialMap.putAll(changeMap);
                    BeanMapUtil.mapToBean(initialMap, revenue_info);
                    params.put(Fields.REVENUE_INFO, revenue_info);
                    custModifyRecords.addAll(swapCustModifyRecords(initialObj, auditChangeForm.getRevenue_info(), auditChangeForm));
                }
            }
        }
    }

    private void changeUserRpcInfo(ClobContentInfo clobContentInfo, AuditChangeForm auditChangeForm, Map<String, Object> params, List<CustModifyRecord> custModifyRecords) {
        if (auditChangeForm.getUser_rpc_info() != null) {
            String rpc_file_id = clobContentInfo.getRpc_file_id();
            String rpc_option = clobContentInfo.getRpc_option();
            String rpc_remark = clobContentInfo.getRpc_remark();
            if (StringUtils.equals(auditChangeForm.getSaveCustmodifyRecordFlag(), "1")) {//特殊处理。修改前的内容为原始数据
                rpc_file_id = clobContentInfo.getOriginal_rpc_file_id();
                rpc_option = clobContentInfo.getOriginal_rpc_option();
                rpc_remark = clobContentInfo.getOriginal_rpc_remark();
            }
            UserRpcInfo user_rpc_info = auditChangeForm.getUser_rpc_info();
            UserRpcInfo initialObj = new UserRpcInfo();
            initialObj.setRpc_file_id(rpc_file_id);
            initialObj.setRpc_option(rpc_option);
            initialObj.setRpc_remark(rpc_remark);
            HashMap<String, Object> changeMap = BeanMapUtil.classIsEqual(initialObj, user_rpc_info);
            if (!changeMap.isEmpty()) {
                UserRpcInfo userRpcInfo = new UserRpcInfo();
                Map<String, Object> initialMap = BeanMapUtil.beanToMap(initialObj);
                initialMap.putAll(changeMap);
                BeanMapUtil.mapToBean(initialMap, userRpcInfo);
                params.put(Fields.RPC_FILE_ID, userRpcInfo.getRpc_file_id());
                params.put(Fields.RPC_OPTION, userRpcInfo.getRpc_option());
                params.put(Fields.RPC_REMARK, userRpcInfo.getRpc_remark());
                custModifyRecords.addAll(swapCustModifyRecords(initialObj, user_rpc_info, auditChangeForm));
            }
        }
    }


    @Override
    public Page<ModifyRecordResult> selectModifyRecordListByPage(Page<ModifyRecordResult> page, ModifyRecordReq modifyRecordReq) {
        modifyRecordReq.setModify_datetime_start(SqlDateUtil.getDateStartDetail(modifyRecordReq.getModify_datetime_start()));
        modifyRecordReq.setModify_datetime_end(SqlDateUtil.getDateEndDetail(modifyRecordReq.getModify_datetime_end()));
        return baseMapper.selectModifyRecordListByPage(page, modifyRecordReq);
    }

    @Override
    public List<CustModifyRecord> queryModifyRecord(String request_no, BusinFlowTask businFlowTask) {
        List<CustModifyRecord> list = this.list(new LambdaQueryWrapper<>(CustModifyRecord.class)
                .gt(CustModifyRecord::getModify_datetime, businFlowTask.getDeal_datetime())
                .notIn(CustModifyRecord::getModify_item, "profession_other")
                .eq(CustModifyRecord::getRequest_no, request_no)
                .orderByDesc(CustModifyRecord::getModify_datetime));
        return list;
    }
}
