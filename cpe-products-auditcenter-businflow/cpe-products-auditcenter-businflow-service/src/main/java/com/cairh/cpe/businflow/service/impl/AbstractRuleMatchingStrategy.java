package com.cairh.cpe.businflow.service.impl;

import com.alibaba.fastjson.JSON;
import com.cairh.cpe.cache.service.CacheBranch;
import com.cairh.cpe.common.constant.BranchConstant;
import com.cairh.cpe.common.dto.BaseRuleInfo;
import com.cairh.cpe.common.dto.Rule;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.response.BranchInfo;
import com.cairh.cpe.common.service.IRuleConfigurationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 规则处理匹配策略
 *
 * <AUTHOR>
 * @since 2025/5/21 17:09
 */
@Slf4j
@Service
public abstract class AbstractRuleMatchingStrategy {

    @Resource
    protected IRuleConfigurationService ruleConfigurationService;
    @Resource
    private CacheBranch cacheBranch;

    /**
     * 规则匹配
     */
    public abstract BaseRuleInfo ruleMatch(BusinFlowTask businFlowTask, ClobContentInfo clobContentInfo);


    protected Rule parseRule(String expression) {
        try {
            return JSON.parseObject(expression, Rule.class);
        } catch (Exception e) {
            log.error("规则解析异常，表达式: {}", expression, e);
            return null;
        }
    }

    /**
     * 获取上级分支结构编号
     */
    protected String getUpBranchNo(String branchNo) {
        BranchInfo branchInfo = cacheBranch.getBranchByNo(branchNo);
        if (branchInfo == null) {
            return branchNo;
        }
        String upBranchNo = branchInfo.getUp_branch_no();
        if (StringUtils.equalsAny(branchInfo.getBranch_type(), BranchConstant.LEVEL_HEADQUARTERS, BranchConstant.LEVEL_SUBSIDIARY_COMPANY, BranchConstant.LEVEL_BRANCH_OFFICE)) {
            upBranchNo = branchInfo.getBranch_no();
        }
        return upBranchNo;
    }
}
