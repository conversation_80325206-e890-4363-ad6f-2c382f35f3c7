package com.cairh.cpe.businflow.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cairh.cpe.businflow.model.ActivityInfo;
import com.cairh.cpe.businflow.service.IActivitiFlowStepService;
import com.cairh.cpe.businflow.service.IRepeatedAddressService;
import com.cairh.cpe.cache.component.ComponentWorkTimeService;
import com.cairh.cpe.cache.service.CacheBackendUser;
import com.cairh.cpe.cache.service.CacheBranch;
import com.cairh.cpe.cache.service.CacheDict;
import com.cairh.cpe.cache.source.PropertySource;
import com.cairh.cpe.common.constant.*;
import com.cairh.cpe.common.constant.flow.FlowNodeConst;
import com.cairh.cpe.common.constant.flow.FlowRecordEnum;
import com.cairh.cpe.common.constant.flow.FlowStatusConst;
import com.cairh.cpe.common.entity.BusinFlowRequest;
import com.cairh.cpe.common.entity.BusinFlowTask;
import com.cairh.cpe.common.entity.Label;
import com.cairh.cpe.common.entity.clob.AuditReason;
import com.cairh.cpe.common.entity.clob.ClobContentInfo;
import com.cairh.cpe.common.entity.clob.IDCardInfo;
import com.cairh.cpe.common.entity.clob.UserBaseInfo;
import com.cairh.cpe.common.entity.request.AuditForm;
import com.cairh.cpe.common.entity.response.BranchInfo;
import com.cairh.cpe.common.entity.response.CreateTaskResult;
import com.cairh.cpe.common.entity.support.BackendUser;
import com.cairh.cpe.common.service.*;
import com.cairh.cpe.common.util.BeanMapUtil;
import com.cairh.cpe.common.util.KHDateUtil;
import com.cairh.cpe.common.util.RedissonUtil;
import com.cairh.cpe.common.util.SqlDateUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.db.config.IdGenerator;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BusinRequestFlowServiceImpl implements IRequestFlowService {

    @Autowired
    private IBusinFlowParamsService businFlowParamsService;
    @Autowired
    private IActivitiFlowStepService activitiFlowStepService;
    @Autowired
    private IBusinFlowTaskService businFlowTaskService;
    @Autowired
    private RedissonUtil redissonUtil;
    @Autowired
    private IBusinFlowRequestService businFlowRequestService;
    @Autowired
    private IUserQueryExtInfoService userQueryExtInfoService;
    @Resource
    private IdGenerator idGenerator;
    @Autowired
    private IBusinFlowRecordService businFlowRecordService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private IRequestService requestService;
    @Autowired
    private CacheDict cacheDict;
    @Autowired
    private CacheBackendUser cacheBackendUser;
    @Autowired
    private ComponentWorkTimeService componentWorkTimeService;
    @Autowired
    private CacheBranch cacheBranch;
    @Resource
    private IRepeatedAddressService repeatedAddressService;
    @Resource
    private ILabelService labelService;

    @Override
    public BusinFlowRequest submitParams(String request_no, Map<String, Object> params) {
        String lockKey = String.format(LockKeyConstant.WSKH_LOCK_SUBMIT_PARAMS, request_no);
        try {
            log.info("开户提交,开户加锁前request_no={}", request_no);
            boolean isLock = redissonUtil.tryLock(lockKey, 60, 30, TimeUnit.SECONDS);
            if (!isLock) {
                throw new BizException(ErrorEnum.REDISSON_LOCK.getValue(), ErrorEnum.REDISSON_LOCK.getDesc());
            }
            log.info("开户提交,加锁成功后 request_no={}", request_no);
            params.put(WskhFields.FLOW_REQUEST_INFORM, WskhConstant.FLOW_REQUEST_INFORM_NOT_YET);
            BusinFlowRequest businFlowRequest = new BusinFlowRequest();
            if (StringUtils.isNotBlank(request_no)) {
                businFlowRequest = businFlowRequestService.getById(request_no);
            } else {
                BeanUtils.populate(businFlowRequest, params);
                businFlowRequest.setRequest_no(idGenerator.nextUUID(null));
                businFlowRequest.setUser_id(businFlowRequest.getRequest_no());
                businFlowRequest.setRequest_datetime(new Date());
                businFlowRequest.setRequest_status(FlowStatusConst.REQUEST_STATUS_APPLYING);
                if (StringUtils.isNotBlank(MapUtils.getString(params, Fields.BUSIN_TYPE))) {
                    businFlowRequest.setBusin_type(MapUtils.getString(params, Fields.BUSIN_TYPE));
                } else {
                    businFlowRequest.setBusin_type(WskhConstant.BUSIN_TYPE_NORMAL);
                }
            }

            String request_status = businFlowRequest.getRequest_status();
            Map<String, Object> contentMap;
            String profession_other = "";
            if (StringUtils.isNotBlank(request_no)) {
                // 之前已经提交过参数
                log.info("用户request_no={},请求状态request_status={}", request_no, request_status);
                if (!ArrayUtils.contains(FlowStatusConst.ALLOW_APPLY_STATUS, request_status)) {
                    // 双向视频提示优化
                    String video_type = MapUtils.getString(params, Fields.VIDEO_TYPE);
                    if (StringUtils.equals(WskhConstant.BUSIN_TYPE_NORMAL, businFlowRequest.getBusin_type()) &&
                            StringUtils.equals(video_type, WskhConstant.VIDEO_TYPE_2)) {
                        throw new BizException(ErrorEnum.NOT_ALLOW_SUBMIT_PARAM.getValue(), "见证已通过，请勿再次提交!");
                    }
                    throw new BizException(ErrorEnum.NOT_ALLOW_SUBMIT_PARAM.getValue(), "业务处理中,不允许提交请求参数");
                }
                JSONObject jsonObject = (JSONObject) businFlowParamsService.getParamContentById(businFlowRequest.getRequest_no()).get("user_base_info");
                profession_other = StringUtils.isNotBlank(jsonObject.getString("profession_other")) ? jsonObject.getString("profession_other") : "";
            }

            // 地址 + 单客户不同营业部提交判断
            log.info("开户提交,营业部联系地址判断 request_no={}", request_no);
            String finalRequest_no = businFlowRequest.getRequest_no();
            if (params.containsKey(Fields.ID_NO) && Objects.nonNull(params.get(Fields.ID_NO))) {
                businFlowRequest.setBranch_repeated(getBranch_repeated(params.get(Fields.ID_NO).toString()));
                params.put("branch_repeated", businFlowRequest.getBranch_repeated());
            }
            if (params.containsKey(Fields.ADDRESS) && Objects.nonNull(params.get(Fields.ADDRESS))) {
                businFlowRequest.setAddress_repeated(getAddress_repeated(params.get(Fields.ADDRESS).toString(), params.get(Fields.ID_NO).toString(),
                        finalRequest_no));
                params.put("address_repeated", businFlowRequest.getAddress_repeated());
            }
            log.info("开户提交,标签控制request_no={}", request_no);
            List<Label> matchLabel = labelService.getMatchLabel(businFlowRequest.getRequest_no(), params);
            if (CollectionUtils.isNotEmpty(matchLabel)) {
                StringBuilder match_labels = new StringBuilder();
                for (Label label : matchLabel) {
                    String serial_id = label.getSerial_id();
                    match_labels.append(serial_id).append(StrUtil.COMMA);
                }
                if (match_labels.length() > 0) {
                    match_labels.deleteCharAt(match_labels.length() - 1);
                }
                businFlowRequest.setMatch_labels(match_labels.toString());
            }
            contentMap = BeanMapUtil.beanToMap(businFlowRequest);
            contentMap.put("request_datetime", KHDateUtil.formatDate(new Date(), KHDateUtil.DATE_TIME_FORMAT));
            if (StringUtils.isNotBlank(profession_other)) {
                UserBaseInfo userBaseInfo = (UserBaseInfo) params.get("user_base_info");
                userBaseInfo.setProfession_other(profession_other);
                params.put("user_base_info", userBaseInfo);
            }
            // 拒绝参数request_no 覆盖为空
            params.remove(Fields.REQUEST_NO);
            params.remove("branch_repeated");
            params.remove("address_repeated");
            // 中登无公安照返回业务开关
            boolean open_result = PropertySource.getBoolean(PropKeyConstant.WSKH_CSDC_VERIFICATION_SWITCH);
            if (open_result) {
                contentMap.put(Fields.DATA_SIGN, "1");
            }
            log.info("开户提交,业务数据保存 request_no={}", request_no);
            saveContentMapParams(businFlowRequest, contentMap, params);

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    redisTemplate.opsForList().leftPush(QueueConstant.QUERY_CLOB_QUEUE, finalRequest_no);
                }
            });

            return businFlowRequest;
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("业务流程提交数据存储失败", e);
            throw new BizException(ErrorEnum.BUSIN_APPLE_DATA_SAVE_ERROR.getValue(), "业务流程提交数据存储失败" + e.getMessage());
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void submitParamsByOperator(String request_no, Map<String, Object> params) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_SUBMITPARAMSBYOPERATOR, request_no);
        boolean isLock = redissonUtil.tryLock(lockKey, 30, 10, TimeUnit.SECONDS);
        if (!isLock) {
            log.error("[submitParamsByOperator]更新用户信息长时间未获取锁，【{}】", request_no);
            throw new BizException(ErrorEnum.REDIS_LOCK_ERROR.getValue(), ErrorEnum.REDIS_LOCK_ERROR.getDesc());
        }
        try {
            BusinFlowRequest businFlowRequest = businFlowRequestService.getById(request_no);
            if (businFlowRequest == null) {
                log.error("[submitParamsByOperator]，requestNo={}，业务数据不存在!", request_no);
                throw new BizException(ErrorEnum.NOT_DATA.getValue(), ErrorEnum.NOT_DATA.getDesc());
            }
            Map<String, Object> contentMap = businFlowParamsService.getParamContentById(businFlowRequest.getRequest_no());
            saveContentMapParams(businFlowRequest, contentMap, params);
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    redisTemplate.opsForList().leftPush(QueueConstant.QUERY_CLOB_QUEUE, request_no);
                }
            });
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            log.error("[submitParamsByOperator]保存用户参数信息，requestNo={}, 异常!", request_no, e);
            throw new BizException(ErrorEnum.SUBMIT_PARAMS_ERROR.getValue(), e.getMessage());
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }

    @Override
    public void applyAudit(String request_no) {
        BusinFlowRequest businFlowRequest = businFlowRequestService.getById(request_no);
        if (!ArrayUtils.contains(FlowStatusConst.ALLOW_SUBMIT_STATUS, businFlowRequest.getRequest_status())) {
            throw new BizException(ErrorEnum.AUDIT_STATUS_NOT_PERMIT.getValue(), ErrorEnum.AUDIT_STATUS_NOT_PERMIT.getDesc());
        }
        String task_id = "";
        String auditor_no = null;
        CreateTaskResult createTaskResult = autoCreateTask(businFlowRequest, StrUtil.EMPTY, auditor_no, WskhConstant.NO_PUSH_FLAG, task_id);
        Map<String, Object> params = new HashMap<>();
        params.put(Fields.REQUEST_STATUS, createTaskResult.getRequest_status());
        params.put(Fields.RECTIFICATION_ITEM, StrUtil.SPACE);
        params.put(Fields.SUBMIT_DATETIME, DateUtil.formatDateTime(new Date()));
        params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22114);
        params.put(Fields.ANODE_ID, FlowNodeConst.START);
        params.put(Fields.OPERATOR_NO, WskhConstant.SUPER_USER);
        params.put(Fields.OPERATOR_NAME, WskhConstant.SUPER_USER);
        params.put(Fields.BUSINESS_REMARK, "用户提交见证申请");
        submitParamsByOperator(request_no, params);
    }


    @Override
    public void dealAudit(AuditForm auditForm) {
        BusinFlowTask businFlowTask = businFlowTaskService.dealBusinFlowTask(auditForm);

        //更新申请表
        LambdaUpdateWrapper<BusinFlowRequest> requestWrapper = new LambdaUpdateWrapper<>();
        requestWrapper.set(BusinFlowRequest::getRequest_status, FlowStatusConst.REQUEST_STATUS_AUDITING);
        requestWrapper.set(BusinFlowRequest::getOperator_no, auditForm.getOperator_no());
        requestWrapper.set(BusinFlowRequest::getAnode_id, businFlowTask.getTask_type());
        requestWrapper.eq(BusinFlowRequest::getRequest_no, businFlowTask.getRequest_no());
        businFlowRequestService.update(requestWrapper);

        // 更新扩展表
        userQueryExtInfoService.updateOperatorInfo(businFlowTask.getRequest_no(),
                businFlowTask.getOperator_no(),
                businFlowTask.getOperator_name(),
                FlowStatusConst.REQUEST_STATUS_AUDITING,
                businFlowTask.getTask_type());

        Map<String, Object> params = new HashMap<>();
        params.put(Fields.OPERATOR_NO, auditForm.getOperator_no());
        params.put(Fields.OPERATOR_NAME, auditForm.getOperator_name());
        params.put(Fields.REQUEST_STATUS, FlowStatusConst.REQUEST_STATUS_AUDITING);
        params.put(Fields.BUSINESS_FLAG, FlowRecordEnum.BEGIN.get(businFlowTask.getTask_type()).getValue());
        params.put(Fields.BUSINESS_REMARK, FlowRecordEnum.BEGIN.get(businFlowTask.getTask_type()).getDesc());
        params.put(Fields.ANODE_ID, businFlowTask.getTask_type());
        params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_RECORD_TYPE);

        //新增 finishNode节点
        Map<String, Object> contentMap = businFlowParamsService.getParamContentById(businFlowTask.getRequest_no());
        List<String> finish_nodes = new ArrayList<>(Arrays.asList(MapUtils.getString(contentMap, WskhFields.FINISH_NODE, "").split(",")));
        finish_nodes.add(MapUtils.getString(params, WskhFields.ANODE_ID));
        params.put(WskhFields.FINISH_NODE, finish_nodes.stream().distinct().collect(Collectors.joining(",")));

        log.info("requestFlowService.dealAudit:{}", JSON.toJSONString(params));
        saveParamsRecord(businFlowTask.getRequest_no(), params);
    }

    @Override
    public void auditNoPass(AuditForm auditForm) {
        auditForm.setTask_status(FlowStatusConst.AUDIT_NO_PASS);
        List<AuditReason> reasons = auditForm.getReasons();
        for (AuditReason reason : reasons) {
            String dictDesc = cacheDict.getDictDesc(DicConstant.REJECT_REASON_GROUP, reason.getReason_group());
            reason.setReason_group(dictDesc);
        }
        auditForm.setAudit_remark((StringUtils.isNotBlank(JSON.toJSONString(auditForm.getReasons()))) ? JSON.toJSONString(auditForm.getReasons()) : " ");
        auditForm.setHandle_type("auditNotPass");
        BusinFlowTask businFlowTask = businFlowTaskService.finishBusinFlowTask(auditForm);

        Map<String, Object> params = auditRecode(auditForm, businFlowTask);
        // 更改初始数据
        changeOriginalData(params, businFlowTask.getRequest_no());
        //params.put(businFlowTask.getTask_type()+"_ai_nopass_reason",auditForm.getAi_nopass_reason());
        params.put(Fields.REQUEST_STATUS, FlowStatusConst.REQUEST_STATUS_AUDIT_FAIL);
        params.put(Fields.BUSINESS_FLAG, FlowRecordEnum.NOPASS.get(businFlowTask.getTask_type()).getValue());
        params.put(Fields.BUSINESS_REMARK, FlowRecordEnum.NOPASS.get(businFlowTask.getTask_type()).getDesc());
        if (StringUtils.isNotBlank(auditForm.getReasonRemark())) {
            params.put(Fields.BUSINESS_REMARK, getReasonRemark(auditForm.getReasons()));
        }
        params.put(Fields.COST_SECOND, DateUtil.between(businFlowTask.getDeal_datetime(), businFlowTask.getFinish_datetime(), DateUnit.SECOND));
        params.put(Fields.RECTIFICATION_ITEM, auditForm.getReasons());

        submitParamsByOperator(businFlowTask.getRequest_no(), params);
    }

    @Override
    public void auditPass(AuditForm auditForm, Map<String, FlowRecordEnum> enumMap) {
        auditForm.setTask_status(auditForm.getTask_status());
        auditForm.setHandle_type("auditPass");
        BusinFlowTask businFlowTask = businFlowTaskService.finishBusinFlowTask(auditForm);
        BusinFlowRequest businFlowRequest = businFlowRequestService.getById(businFlowTask.getRequest_no());

        String request_status = businFlowTask.getTask_status();
        String anode_id = businFlowTask.getTask_type();
        if (!(StringUtils.equals(WskhConstant.BUSIN_TYPE_NORMAL, businFlowRequest.getBusin_type()) &&
                StringUtils.equals(businFlowTask.getVideo_type(), WskhConstant.VIDEO_TYPE_2))
                || !StringUtils.equals(businFlowTask.getTask_type(), TaskTypeEnum.AUDIT.getCode())) {
            //查询最新的task_id
            QueryWrapper<BusinFlowTask> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(Fields.REQUEST_NO, businFlowTask.getRequest_no());
            queryWrapper.orderByDesc("create_datetime");
            List<BusinFlowTask> businFlowTasks = businFlowTaskService.getBaseMapper().selectList(queryWrapper);
            String task_id = businFlowTasks.get(0).getTask_id();
            CreateTaskResult createTaskResult = autoCreateTask(businFlowRequest, businFlowTask.getTask_type(), auditForm.getOperator_no(), task_id);
            request_status = createTaskResult.getRequest_status();
            anode_id = createTaskResult.getAnode_id();
        }

        Map<String, Object> params = auditRecode(auditForm, businFlowTask);
        // 更改初始数据
        changeOriginalData(params, businFlowTask.getRequest_no());
        auditForm.setPass_request_status(request_status);
        params.put(Fields.REQUEST_STATUS, request_status);
        params.put(Fields.BUSINESS_FLAG, enumMap.get(businFlowTask.getTask_type()).getValue());
        params.put(Fields.BUSINESS_REMARK, enumMap.get(businFlowTask.getTask_type()).getDesc());
        params.put(Fields.COST_SECOND, DateUtil.between(businFlowTask.getDeal_datetime(), businFlowTask.getFinish_datetime(), DateUnit.SECOND));
        params.put("not_auditor", auditForm.getOperator_no());

        submitParamsByOperator(businFlowTask.getRequest_no(), params);

        params.clear();
        params.put(Fields.BUSINESS_FLAG, WskhConstant.BUSINESS_FLAG_22385);
        params.put(Fields.BUSINESS_REMARK, "更新节点信息");
        params.put(Fields.RECORD_TYPE, FlowStatusConst.SYSTEM_RECORD_TYPE);
        params.put(Fields.ANODE_ID, anode_id);
        submitParamsByOperator(businFlowTask.getRequest_no(), params);
    }

    private void changeOriginalData(Map<String, Object> params, String request_no) {
        ClobContentInfo clobContentInfo = requestService.getAllDataByRequestNo(request_no);
        // 非网厅业务办理
        IDCardInfo id_card_info = clobContentInfo.getId_card_info();
        UserBaseInfo user_base_info = clobContentInfo.getUser_base_info();
        if (!ArrayUtils.contains(WskhConstant.HAND_BUSIN_TYPE_COLLECTION, clobContentInfo.getBusin_type())) {
            params.put(Fields.ORIGINAL_FILE_6A, clobContentInfo.getFile_6A());
            params.put(Fields.ORIGINAL_FILE_6B, clobContentInfo.getFile_6B());
            params.put(Fields.ORIGINAL_FILE_80, clobContentInfo.getFile_80());
            params.put(WskhFields.ORIGINAL_FILE_7C, clobContentInfo.getFile_7C());
            params.put(WskhFields.ORIGINAL_FILE_7D, clobContentInfo.getFile_7D());
            params.put(WskhFields.ORIGINAL_FILE_7X, clobContentInfo.getFile_7X());
            params.put(WskhFields.ORIGINAL_FILE_7W, clobContentInfo.getFile_7W());
            params.put(WskhFields.ORIGINAL_FILE_BM, clobContentInfo.getFile_Bm());
            params.put(Fields.ORIGINAL_ID_ADDRESS, id_card_info.getId_address());
            params.put(Fields.ORIGINAL_ID_BEGINDATE, id_card_info.getId_begindate());
            params.put(Fields.ORIGINAL_ID_ENDDATE, id_card_info.getId_enddate());
            params.put(WskhFields.ORIGINAL_AUXILIARY_ID_ADDRESS, id_card_info.getAuxiliary_id_address());
            params.put(WskhFields.ORIGINAL_AUXILIARY_ID_BEGINDATE, id_card_info.getAuxiliary_id_begindate());
            params.put(WskhFields.ORIGINAL_AUXILIARY_ID_ENDDATE, id_card_info.getAuxiliary_id_enddate());
            params.put(Fields.ORIGINAL_ADDRESS, user_base_info.getAddress());
            params.put(Fields.ORIGINAL_PROFESSION_CODE, user_base_info.getProfession_code());
            params.put(Fields.ORIGINAL_WORK_UNIT, user_base_info.getWork_unit());
            params.put(Fields.ORIGINAL_CHOOSE_BRANCH_REASON, user_base_info.getChoose_branch_reason());
            params.put(Fields.ORIGINAL_CHOOSE_PROFESSION_REASON, user_base_info.getChoose_profession_reason());
            params.put(Fields.ORIGINAL_DISHONEST_RECORD, user_base_info.getDishonest_record());
            params.put(Fields.ORIGINAL_DISHONEST_RECORD_REMARK, user_base_info.getDishonest_record_remark());
            params.put(Fields.ORIGINAL_PROFESSION_OTHER, user_base_info.getProfession_other());
            params.put(Fields.ORIGINAL_ENGLISH_NAME, id_card_info.getEnglish_name());
            params.put(Fields.ORIGINAL_NATIONALITY, id_card_info.getNationality());
            params.put(Fields.ORIGINAL_PREV_ID_NUMBER, id_card_info.getPrev_id_number());
            params.put(Fields.ORIGINAL_BIRTHDAY, id_card_info.getBirthday());
            // 跨境理财通业务
            if (StringUtils.equals(clobContentInfo.getBusin_type(), WskhConstant.CROSS_BUSIN_TYPE)) {
                params.put(Fields.ORIGINAL_RPC_FILE_ID, clobContentInfo.getRpc_file_id());
                params.put(Fields.ORIGINAL_RPC_OPTION, clobContentInfo.getRpc_option());
                params.put(Fields.ORIGINAL_RPC_REMARK, clobContentInfo.getRpc_remark());
            }
        } else {
            params.put(Fields.ORIGINAL_DISHONEST_RECORD, user_base_info.getDishonest_record());
            params.put(Fields.ORIGINAL_DISHONEST_RECORD_REMARK, user_base_info.getDishonest_record_remark());
            params.put(Fields.ORIGINAL_PHOTO_FRONT, clobContentInfo.getPhoto_front());
            params.put(Fields.ORIGINAL_PHOTO_BACK, clobContentInfo.getPhoto_back());
            params.put(Fields.ORIGINAL_AGENT_PHOTO_FRONT, clobContentInfo.getAgent_photo_front());
            params.put(Fields.ORIGINAL_AGENT_PHOTO_BACK, clobContentInfo.getAgent_photo_back());
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveContentMapParams(BusinFlowRequest businFlowRequest, Map<String, Object> contentMap, Map<String, Object> params) {
        try {
            contentMap.put(Fields.UPDATE_DATETIME, KHDateUtil.formatDate(new Date(), KHDateUtil.DATE_TIME_FORMAT));

            if (ArrayUtils.contains(FlowStatusConst.ALLOW_SUBMIT_STATUS, businFlowRequest.getRequest_status())) {
                setNodeInfo(params, contentMap);
            }
            contentMap.putAll(params);

            //更改前查询request_status
            if (!params.containsKey(Fields.REQUEST_STATUS)) {
                BusinFlowRequest request = businFlowRequestService.getById(businFlowRequest.getRequest_no());
                if (null != request) {
                    contentMap.put(Fields.REQUEST_STATUS, request.getRequest_status());
                }
            }
            //保存或更新主业务流程表数据
            BusinFlowRequest bfr = new BusinFlowRequest();
            BeanMapUtil.mapToBean(contentMap, bfr);
            bfr.setTohis_flag(Constant.TOHIS_FLAG_N);
            if (StringUtils.isBlank(bfr.getBusin_type())) {
                bfr.setBusin_type(WskhConstant.BUSIN_TYPE_NORMAL);
            }
            SqlDateUtil.setDefaultValue(bfr);
            log.info("更改businFlowRequest，申请编号【{}】,更改信息{}", bfr.getRequest_no(), JSON.toJSONString(bfr));
            businFlowRequestService.saveOrUpdate(bfr);

            // 保存本次提交业务流程记录和参数数据
            //对params包含operator_no的用户翻译
            if (StringUtils.isNotBlank(MapUtils.getString(params, Fields.OPERATOR_NO, null))) {
                BackendUser backendUser = cacheBackendUser.getBackendUserByStaffNo(String.valueOf(MapUtils.getString(params, Fields.OPERATOR_NO, null)));
                if (backendUser != null) {
                    params.put(Fields.OPERATOR_NAME, backendUser.getUser_name());
                }
            } else if (StringUtils.equals(MapUtils.getString(params, Fields.RECORD_TYPE, null), FlowStatusConst.SYSTEM_RECORD_TYPE)) {
                params.put(Fields.OPERATOR_NAME, WskhConstant.SUPER_USER);
                params.put(Fields.OPERATOR_NO, WskhConstant.SUPER_USER);
            }
            businFlowRecordService.saveBusinFlowRecord(businFlowRequest, params);
            // 保存主业务流程所有参数数据
            log.info("[saveContentMapParams]开始---------更改businFlowParams，申请编号【{}】,更改信息{}", businFlowRequest.getRequest_no(), JSON.toJSONString(contentMap));
            businFlowParamsService.saveParams(businFlowRequest.getRequest_no(), contentMap);
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            log.error("[saveContentMapParams]保存用户信息，requestNo={}, 异常!", businFlowRequest.getRequest_no(), e);
            throw new BizException(ErrorEnum.SAVE_CONTENTMAP_PARAMS_ERROR.getValue(), e.getMessage());
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public void saveParamsRecord(String requestNo, Map<String, Object> params) {
        String lockKey = String.format(LockKeyConstant.WSKH_AC_LOCK_SUBMITPARAMSBYOPERATOR, requestNo);
        boolean isLock = redissonUtil.tryLock(lockKey, 30, 10, TimeUnit.SECONDS);
        if (!isLock) {
            log.error("[saveParamsRecord]长时间未获取锁，【{}】", requestNo);
            throw new BizException(ErrorEnum.REDIS_LOCK_ERROR.getValue(), ErrorEnum.REDIS_LOCK_ERROR.getDesc());
        }
        try {
            Map<String, Object> contentMap = businFlowParamsService.getParamContentById(requestNo);
            contentMap.putAll(params);
            contentMap.put(Fields.UPDATE_DATETIME, KHDateUtil.formatDate(new Date(), KHDateUtil.DATE_TIME_FORMAT));
            BusinFlowRequest businFlowRequest = businFlowRequestService.getById(requestNo);
            businFlowRecordService.saveBusinFlowRecord(businFlowRequest, params);
            // 保存主业务流程所有参数数据
            businFlowParamsService.saveParams(businFlowRequest.getRequest_no(), contentMap);
        } catch (Exception e) {
            log.error("[saveParamsRecord]保存用户信息，requestNo={}, 异常!", requestNo, e);
            throw new BizException(ErrorEnum.SAVE_PARAMS_DATA_ERROR.getValue(), e.getMessage());
        } finally {
            redissonUtil.unlock(lockKey);
        }
    }

    @Override
    public CreateTaskResult autoCreateTask(BusinFlowRequest businFlowRequest, String activity_id, String
            auditor_no, String task_id) {
        boolean open_result = PropertySource.get(PropKeyConstant.WSKH_ISOPEN_DISPATCH, "0").equals("1");
        return autoCreateTask(businFlowRequest, activity_id, auditor_no, open_result ? WskhConstant.NO_PUSH_FLAG : " ", task_id);
    }

    @Override
    public CreateTaskResult autoCreateTask(BusinFlowRequest businFlowRequest, String activity_id, String
            auditor_no, String push_flag, String task_id) {
        Map<String, Object> contentMap = businFlowParamsService.getParamContentById(businFlowRequest.getRequest_no());
        CreateTaskResult createTaskResult = new CreateTaskResult();

        String video_type = MapUtils.getString(contentMap, Fields.VIDEO_TYPE);
        ActivityInfo result = activitiFlowStepService.getNextActivitiNodeInfo(contentMap, activity_id);
        if (result == null) {
            throw new BizException(ErrorEnum.REVIEW_DEFINITION_NOT_EXIST.getValue(), ErrorEnum.REVIEW_DEFINITION_NOT_EXIST.getDesc());
        }

        createTaskResult.setAnode_id(result.getActivityId());
        log.info("autoCreateTask request_no is:{} activity_id is :{}, response anode_id is:{}", businFlowRequest.getRequest_no(), activity_id, result.getActivityId());
        if (result.getActivityType().getType().equals("serviceTask")) {
            throw new BizException(ErrorEnum.TASK_EXECUTE_ERROR.getValue(), ErrorEnum.TASK_EXECUTE_ERROR.getDesc());
        }

        if (FlowNodeConst.END.equals(result.getActivityId())) {
            createTaskResult.setRequest_status(FlowStatusConst.REQUEST_STATUS_AUDIT_PASS);
        } else {
            String allow_auditor = MapUtils.getString(contentMap, "allow_auditor");
            String not_auditor = MapUtils.getString(contentMap, "not_auditor");
            //已经有人审核过了任务
            if (StringUtils.isNotBlank(auditor_no)) {
                //not_auditor = auditor_no;
                List<String> noAuditorList = getNotAllowAuditorList(task_id);
                not_auditor = StringUtils.join(noAuditorList, ",");
            }
            if (" ".equals(auditor_no)) {
                not_auditor = "";
            }
            // contentMap增加当前时间是否是工作时间、以及转换branch_no的名称
            contentMap.put(Fields.IS_WORK_TIME, componentWorkTimeService.isWorkTime(WskhConstant.WORK_TIME_AUDIT_WORKING));
            // 见证申请时间
            contentMap.put(Fields.REQUEST_DATETIME, businFlowRequest.getRequest_datetime());
            String branchNo = MapUtils.getString(contentMap, Fields.BRANCH_NO);
            if (StringUtils.isBlank(branchNo)) {
                log.warn("创建任务request_no：{}时，查询大字段表branchNo为空！！！", businFlowRequest.getRequest_no());
                branchNo = businFlowRequest.getBranch_no();
                contentMap.put(Fields.BRANCH_NO, branchNo);
            }
            BranchInfo branchInfo = cacheBranch.getBranchByNo(branchNo);
            if (Objects.nonNull(branchInfo)) {
                String upBranchNo = StringUtils.isNotBlank(branchInfo.getUp_branch_no()) ? branchInfo.getUp_branch_no() : branchNo;
                contentMap.put(Fields.BRANCH_NAME, branchInfo.getBranch_name());
                contentMap.put(Fields.UP_BRANCH_NO, upBranchNo);
                BranchInfo upBranchInfo = cacheBranch.getBranchByNo(upBranchNo);
                contentMap.put(Fields.UP_BRANCH_NAME, Objects.nonNull(upBranchInfo) ? upBranchInfo.getBranch_name() : upBranchNo);
                //是否为托管分支机构
                String isBranchManaged = isBranchManaged(branchInfo);
                contentMap.put(Fields.IS_BRANCH_MANAGED, isBranchManaged);
            }
            businFlowTaskService.createBusinFlowTask(businFlowRequest, result.getActivityId(), video_type, allow_auditor, not_auditor, push_flag, contentMap, task_id);
            createTaskResult.setRequest_status(FlowStatusConst.REQUEST_STATUS_PENDING_AUDIT);
        }
        return createTaskResult;
    }


    @Override
    public String getBranch_repeated(String id_no) {
        int repeatNum = PropertySource.getInt(PropKeyConstant.WSKH_BRANCH_REPEAT_NUM, 3) - 1;
        LambdaQueryWrapper<BusinFlowRequest> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusinFlowRequest::getRequest_status, FlowStatusConst.AUDIT_PASS);
        wrapper.eq(BusinFlowRequest::getAnode_id, FlowNodeConst.END);
        wrapper.eq(BusinFlowRequest::getId_no, id_no);
        List<String> requestList = businFlowRequestService.list(wrapper)
                .stream()
                .map(BusinFlowRequest::getRequest_no)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(requestList) && requestList.size() >= repeatNum) {
            return Constant.BUSIN_HIT;
        }
        return Constant.BUSIN_MISS;
    }

    private String isBranchManaged(BranchInfo branchInfo) {
//        String baseNotManageBranch = PropertySource.get(PropKeyConstant.BASE_NOTMANAGE_BRANCH, "0");
//        Set<String> notManageBranchSet = new HashSet<>(Arrays.asList(baseNotManageBranch.split(",")));
//        if (CollectionUtil.isNotEmpty(notManageBranchSet)) {
//            String branchNo = branchInfo.getBranch_no();
//            String upBranchNo = branchInfo.getUp_branch_no();
//            if (notManageBranchSet.contains(branchNo) || notManageBranchSet.contains(upBranchNo)) {
//                return Constant.BRANCH_MANAGED_N;
//            }
//            List<BranchInfo> allBranchList = cacheBranch.getAllBranchList();
//            for (BranchInfo branch : allBranchList) {
//                if (StringUtils.equals(upBranchNo, branch.getBranch_no())) {
//                    if (StringUtils.isNotBlank(branch.getUp_branch_no()) && notManageBranchSet.contains(branch.getUp_branch_no())) {
//                        return Constant.BRANCH_MANAGED_N;
//                    }
//                    upBranchNo = branch.getUp_branch_no();
//                }
//            }
//        }
        return Constant.BRANCH_MANAGED_Y;
    }

    private List<String> getNotAllowAuditorList(String task_id) {
        List<String> list = new ArrayList<>();
        BusinFlowTask auditTask = businFlowTaskService.getCurrTaskTypeByTaskId(task_id, FlowNodeConst.AUDIT);
        if (null != auditTask) {
            list.add(auditTask.getOperator_no());
        }
        BusinFlowTask reviewTask = businFlowTaskService.getCurrTaskTypeByTaskId(task_id, FlowNodeConst.REVIEW);
        if (null != reviewTask) {
            list.add(reviewTask.getOperator_no());
        }
        BusinFlowTask doubleTask = businFlowTaskService.getCurrTaskTypeByTaskId(task_id, FlowNodeConst.SECONDARY_REVIEW);
        if (null != doubleTask) {
            list.add(doubleTask.getOperator_no());
        }
        return list;
    }

    private void setNodeInfo(Map<String, Object> params, Map<String, Object> contentMap) {
        // 流程重新走标志
        if (params.containsKey(WskhFields.CLEAR_NODE)) {
            if ("ALL".equals(MapUtils.getString(params, WskhFields.CLEAR_NODE))) {
                contentMap.remove(WskhFields.FINISH_NODE);
                contentMap.remove(WskhFields.AMEND_NODE);
            } else {
                String finish_node = MapUtils.getString(contentMap, WskhFields.FINISH_NODE, "");
                List<String> finish_nodes = new ArrayList<>(Arrays.asList(finish_node.split(",")));

                String clear_node = MapUtils.getString(params, WskhFields.CLEAR_NODE);
                List<String> clear_set = Arrays.asList(clear_node.split(","));

                finish_nodes.removeAll(clear_set);
                contentMap.put(WskhFields.FINISH_NODE, StringUtils.join(finish_nodes, ","));
            }
        }

        if (params.containsKey(WskhFields.ANODE_ID)) {
            // 完成节点增加
            String finish_node = MapUtils.getString(contentMap, WskhFields.FINISH_NODE, "");
            List<String> finish_nodes = new ArrayList<>(Arrays.asList(finish_node.split(",")));
            finish_nodes.remove("");
            finish_nodes.add(MapUtils.getString(params, WskhFields.ANODE_ID));
            finish_nodes = finish_nodes.stream().distinct().collect(Collectors.toList());
            contentMap.put(WskhFields.FINISH_NODE, StringUtils.join(finish_nodes, ","));

            // 整改节点删除
            String amend_node = MapUtils.getString(contentMap, WskhFields.AMEND_NODE);
            if (StringUtils.isNotBlank(amend_node)) {
                List<String> amend_nodes = new ArrayList<>(Arrays.asList(amend_node.split(",")));
                amend_nodes.remove(MapUtils.getString(params, WskhFields.ANODE_ID));
                contentMap.put(WskhFields.AMEND_NODE, StringUtils.join(amend_nodes, ","));
            }
        }
    }

    private Map<String, Object> auditRecode(AuditForm auditForm, BusinFlowTask businFlowTask) {
        Map<String, Object> params = new HashMap<>();
        params.put(Fields.OPERATOR_NO, auditForm.getOperator_no());
        params.put(Fields.OPERATOR_NAME, auditForm.getOperator_name());
        params.put(Fields.RECORD_TYPE, FlowStatusConst.AUDIT_RECORD_TYPE);
        params.put(Fields.ANODE_ID, businFlowTask.getTask_type());
        return params;
    }

    public String getReasonRemark(List<AuditReason> reasons) {
        return reasons.stream().map(AuditReason::getReason_desc).collect(Collectors.joining(""));
    }


    /**
     * 判断地址 0 - 未命中
     *
     * @param address
     * @param request_no
     * @return
     */
    private String getAddress_repeated(String address, String id_no, String request_no) {
        int truncate_digits = PropertySource.getInt(PropKeyConstant.WSKH_ADDRESS_TRUNCATE_DIGITS, 0);
        StringUtils.right(address, truncate_digits);
        List<String> whiteList = repeatedAddressService.getWhiteList();
        if (CollectionUtils.isNotEmpty(whiteList) && whiteList.contains(address)) {
            return Constant.BUSIN_MISS;
        }
        List<String> blackList = repeatedAddressService.getBlackList();
        if (CollectionUtils.isNotEmpty(blackList) && blackList.contains(address)) {
            return Constant.BUSIN_HIT;
        }
        if (!redisTemplate.hasKey(RedisKeyConstant.WSKH_ADDRESS_HASH)) {
            // 设置键的过期时间为晚上12点
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime midnight = now.toLocalDate().atTime(LocalDateTime.MAX.toLocalTime());
            long secondsToMidnight = ChronoUnit.SECONDS.between(now, midnight);
            redisTemplate.expire(RedisKeyConstant.WSKH_ADDRESS_HASH, secondsToMidnight, TimeUnit.SECONDS);
        }
        String redisKey = RedisKeyConstant.WSKH_ADDRESS_HMACSHA1 + SecureUtil.sha256(address);
        Boolean bool = redisTemplate.opsForHash().hasKey(RedisKeyConstant.WSKH_ADDRESS_HASH, redisKey);
        if (!bool) {
            redisTemplate.opsForHash().put(RedisKeyConstant.WSKH_ADDRESS_HASH, redisKey,
                    JSON.toJSONString(Sets.newHashSet(StringUtils.joinWith(",", request_no, id_no))));
            return Constant.BUSIN_MISS;
        }
        Set<String> request_no_set = JSON.parseObject(redisTemplate.opsForHash().get(RedisKeyConstant.WSKH_ADDRESS_HASH, redisKey).toString(), Set.class);
        List<String> values = request_no_set.stream()
                .filter(item -> !item.contains(id_no))
                .filter(item -> !item.contains(request_no))
                .collect(Collectors.toList());
        // 如果是驳回在提交的单子要过滤掉
        if (CollectionUtils.isEmpty(values)) {
            request_no_set.add(StringUtils.joinWith(",", request_no, id_no));
            redisTemplate.opsForHash().put(RedisKeyConstant.WSKH_ADDRESS_HASH, redisKey, JSON.toJSONString(request_no_set));
            return Constant.BUSIN_MISS;
        }
        return Constant.BUSIN_HIT;
    }


}
