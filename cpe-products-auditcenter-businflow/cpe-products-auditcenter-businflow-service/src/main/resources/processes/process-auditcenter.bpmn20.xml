<?xml version="1.0" encoding="UTF-8"?>
<definitions id="definitions"
             targetNamespace="Examples"
             xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL">

    <process id="process-auditcenter">
        <startEvent id="theStart" name="开始"/>
        <userTask id="audit" name="审核"/>
        <userTask id="review" name="复核"/>
        <userTask id="secondary_review" name="二次复核"/>
        <endEvent id="theEnd" name="结束"/>

        <sequenceFlow id="flow1" sourceRef="theStart" targetRef="audit"/>
        <sequenceFlow id="flow2" sourceRef="audit" targetRef="review"/>
        <sequenceFlow id="flow3" sourceRef="review" targetRef="secondary_review">
            <conditionExpression>
                <![CDATA[${defaultConditionService.evaluate(execution, "review", "secondary_review")}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="flow4" sourceRef="review" targetRef="theEnd">
            <conditionExpression>
                <![CDATA[${defaultConditionService.evaluate(execution, "review", "theEnd")}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="flow5" sourceRef="review" targetRef="review">
            <conditionExpression>
                <![CDATA[${defaultConditionService.evaluate(execution, "review", "review")}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="flow6" sourceRef="secondary_review" targetRef="theEnd">
            <conditionExpression>
                <![CDATA[${defaultConditionService.evaluate(execution, "secondary_review", "theEnd")}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="flow7" sourceRef="secondary_review" targetRef="secondary_review">
            <conditionExpression>
                <![CDATA[${defaultConditionService.evaluate(execution, "secondary_review", "secondary_review")}]]>
            </conditionExpression>
        </sequenceFlow>
        <!--<sequenceFlow id="flow5" sourceRef="secondary_review" targetRef="secondary_review">
            <conditionExpression>
                <![CDATA[${defaultConditionService.evaluate(execution, "secondary_review", "secondary_review")}]]>
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="flow6" sourceRef="secondary_review" targetRef="theEnd">
            <conditionExpression>
                <![CDATA[${defaultConditionService.evaluate(execution, "secondary_review", "theEnd")}]]>
            </conditionExpression>
        </sequenceFlow>-->
    </process>
</definitions>
